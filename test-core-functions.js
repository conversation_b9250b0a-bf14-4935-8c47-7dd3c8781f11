#!/usr/bin/env node

// 核心功能测试脚本
const http = require('http');

const BASE_URL = 'http://localhost:3001';

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// HTTP请求工具函数
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试函数
async function runTest(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`);
    await testFn();
    console.log(`✅ 通过: ${name}`);
    testResults.passed++;
    testResults.tests.push({ name, status: 'PASSED' });
  } catch (error) {
    console.log(`❌ 失败: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, status: 'FAILED', error: error.message });
  }
}

// 断言函数
function assert(condition, message) {
  if (!condition) {
    throw new Error(message);
  }
}

// 测试用例
async function testHealthCheck() {
  const response = await makeRequest('/api/health');
  assert(response.status === 200, '健康检查应该返回200状态码');
  assert(response.data.status === 'healthy', '健康检查应该返回healthy状态');
}

async function testClientsAPI() {
  const response = await makeRequest('/api/clients');
  assert(response.status === 200, '客户API应该返回200状态码');
  assert(response.data.success === true, '客户API应该返回成功状态');
  assert(Array.isArray(response.data.data.clients), '客户API应该返回客户数组');
  console.log(`   📊 找到 ${response.data.data.clients.length} 个客户`);
}

async function testTreatmentsAPI() {
  const response = await makeRequest('/api/treatments');
  assert(response.status === 200, '治疗项目API应该返回200状态码');
  assert(Array.isArray(response.data.treatments), '治疗项目API应该返回治疗项目数组');
  console.log(`   📊 找到 ${response.data.treatments.length} 个治疗项目`);
}

async function testAppointmentsAPI() {
  const response = await makeRequest('/api/appointments?start_date=2025-07-01&end_date=2025-07-31');
  assert(response.status === 200, '预约API应该返回200状态码');
  assert(Array.isArray(response.data.appointments), '预约API应该返回预约数组');
  console.log(`   📊 找到 ${response.data.appointments.length} 个预约`);
}

async function testInvoicesAPI() {
  const response = await makeRequest('/api/invoices');
  assert(response.status === 200, '账单API应该返回200状态码');
  assert(Array.isArray(response.data.invoices), '账单API应该返回账单数组');
  console.log(`   📊 找到 ${response.data.invoices.length} 个账单`);
}

async function testPaymentsAPI() {
  const response = await makeRequest('/api/payments');
  assert(response.status === 200, '付款API应该返回200状态码');
  assert(Array.isArray(response.data.payments), '付款API应该返回付款数组');
  console.log(`   📊 找到 ${response.data.payments.length} 个付款记录`);
}

async function testAppointmentConflictAPI() {
  const testData = {
    appointment_date: '2025-07-25',
    start_time: '10:00',
    end_time: '11:00'
  };
  const response = await makeRequest('/api/appointments/conflicts', 'POST', testData);
  assert(response.status === 200, '预约冲突检测API应该返回200状态码');
  assert(Array.isArray(response.data.conflicts), '冲突检测应该返回冲突数组');
  console.log(`   📊 冲突检测结果: 找到 ${response.data.conflicts.length} 个冲突`);
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始核心功能测试...\n');

  await runTest('健康检查', testHealthCheck);
  await runTest('客户管理API', testClientsAPI);
  await runTest('治疗项目API', testTreatmentsAPI);
  await runTest('预约管理API', testAppointmentsAPI);
  await runTest('账单管理API', testInvoicesAPI);
  await runTest('付款管理API', testPaymentsAPI);
  await runTest('预约冲突检测API', testAppointmentConflictAPI);

  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests.filter(t => t.status === 'FAILED').forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }

  return testResults.failed === 0;
}

// 运行测试
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行出错:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests };
