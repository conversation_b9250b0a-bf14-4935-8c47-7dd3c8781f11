#!/usr/bin/env node

// 简化的修复验证测试脚本
const fs = require('fs');
const path = require('path');

console.log('🚀 开始修复验证测试...\n');

let passed = 0;
let failed = 0;

function runTest(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`);
    testFn();
    console.log(`✅ 通过: ${name}`);
    passed++;
  } catch (error) {
    console.log(`❌ 失败: ${name} - ${error.message}`);
    failed++;
  }
}

function assert(condition, message) {
  if (!condition) {
    throw new Error(message);
  }
}

// 测试下拉菜单滚动修复
runTest('下拉菜单滚动修复', () => {
  // 检查ClientSearchSelect组件
  const clientSearchPath = path.join(process.cwd(), 'src/components/ui/client-search-select.tsx');
  const clientSearchContent = fs.readFileSync(clientSearchPath, 'utf8');
  
  assert(
    clientSearchContent.includes('max-h-[200px] overflow-y-auto'),
    'ClientSearchSelect应该包含滚动样式'
  );
  
  // 检查TreatmentSearchSelect组件
  const treatmentSearchPath = path.join(process.cwd(), 'src/components/ui/treatment-search-select.tsx');
  const treatmentSearchContent = fs.readFileSync(treatmentSearchPath, 'utf8');
  
  assert(
    treatmentSearchContent.includes('max-h-[200px] overflow-y-auto'),
    'TreatmentSearchSelect应该包含滚动样式'
  );
  
  // 检查AppointmentTypeSelect组件
  const appointmentTypeSearchPath = path.join(process.cwd(), 'src/components/ui/appointment-type-select.tsx');
  const appointmentTypeSearchContent = fs.readFileSync(appointmentTypeSearchPath, 'utf8');
  
  assert(
    appointmentTypeSearchContent.includes('max-h-[200px] overflow-y-auto'),
    'AppointmentTypeSelect应该包含滚动样式'
  );
  
  console.log('   📊 所有下拉菜单组件都已添加滚动样式');
});

// 测试货币符号修复
runTest('货币符号修复', () => {
  const filesToCheck = [
    'src/components/dashboard/CRMSummaryCard.tsx',
    'src/components/analytics/BusinessAnalyticsDashboard.tsx',
    'src/app/dashboard/clients/[id]/page.tsx',
    'src/components/treatment/TreatmentPackageManager.tsx'
  ];
  
  let totalYuanFound = 0;
  let totalDollarFound = 0;
  
  for (const filePath of filesToCheck) {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 计算人民币符号数量
      const yuanMatches = content.match(/¥/g);
      const yuanCount = yuanMatches ? yuanMatches.length : 0;
      totalYuanFound += yuanCount;
      
      // 计算美元符号数量
      const dollarMatches = content.match(/\$/g);
      const dollarCount = dollarMatches ? dollarMatches.length : 0;
      totalDollarFound += dollarCount;
      
      if (yuanCount > 0) {
        console.log(`   ⚠️  ${filePath} 仍包含 ${yuanCount} 个人民币符号`);
      } else {
        console.log(`   ✅ ${filePath} 已清理人民币符号`);
      }
      
      if (dollarCount > 0) {
        console.log(`   💰 ${filePath} 包含 ${dollarCount} 个美元符号`);
      }
    }
  }
  
  assert(totalYuanFound === 0, `发现 ${totalYuanFound} 个人民币符号，应该全部替换为美元符号`);
  
  console.log(`   📊 货币符号检查完成: 0个¥符号, ${totalDollarFound}个$符号`);
});

// 测试Overview页面组件更新
runTest('Overview页面组件更新', () => {
  // 检查PieGraph组件
  const pieGraphPath = path.join(process.cwd(), 'src/features/overview/components/pie-graph.tsx');
  const pieGraphContent = fs.readFileSync(pieGraphPath, 'utf8');
  
  assert(
    pieGraphContent.includes('治疗项目分布'),
    'PieGraph应该显示治疗项目分布'
  );
  
  assert(
    pieGraphContent.includes('fetchTreatmentData'),
    'PieGraph应该获取真实的治疗项目数据'
  );
  
  // 检查BarGraph组件
  const barGraphPath = path.join(process.cwd(), 'src/features/overview/components/bar-graph.tsx');
  const barGraphContent = fs.readFileSync(barGraphPath, 'utf8');
  
  assert(
    barGraphContent.includes('每日收入统计'),
    'BarGraph应该显示每日收入统计'
  );
  
  assert(
    barGraphContent.includes('fetchRevenueData'),
    'BarGraph应该获取真实的收入数据'
  );
  
  // 检查RecentSales组件
  const recentSalesPath = path.join(process.cwd(), 'src/features/overview/components/recent-sales.tsx');
  const recentSalesContent = fs.readFileSync(recentSalesPath, 'utf8');
  
  assert(
    recentSalesContent.includes('最近付款'),
    'RecentSales应该显示最近付款'
  );
  
  assert(
    recentSalesContent.includes('fetchRecentPayments'),
    'RecentSales应该获取真实的付款数据'
  );
  
  // 检查AreaGraph组件
  const areaGraphPath = path.join(process.cwd(), 'src/features/overview/components/area-graph.tsx');
  const areaGraphContent = fs.readFileSync(areaGraphPath, 'utf8');
  
  assert(
    areaGraphContent.includes('业务增长趋势'),
    'AreaGraph应该显示业务增长趋势'
  );
  
  assert(
    areaGraphContent.includes('fetchGrowthData'),
    'AreaGraph应该获取真实的增长数据'
  );
  
  console.log('   📊 所有Overview组件都已更新为显示真实数据');
});

// 测试客户搜索数据结构修复
runTest('客户搜索数据结构修复', () => {
  const clientSearchPath = path.join(process.cwd(), 'src/components/ui/client-search-select.tsx');
  const clientSearchContent = fs.readFileSync(clientSearchPath, 'utf8');

  assert(
    clientSearchContent.includes('data.data?.clients'),
    'ClientSearchSelect应该使用正确的数据访问路径'
  );

  // 检查是否有错误的数据访问路径（直接访问data.clients而不是data.data.clients）
  const hasDirectDataClients = clientSearchContent.match(/data\.clients(?!\s*\|\|)/);
  assert(
    !hasDirectDataClients,
    'ClientSearchSelect不应该直接使用data.clients访问路径'
  );

  console.log('   📊 客户搜索组件数据访问路径已修复');
});

console.log('\n📊 修复验证测试结果汇总:');
console.log(`✅ 通过: ${passed}`);
console.log(`❌ 失败: ${failed}`);
console.log(`📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

if (failed === 0) {
  console.log('\n🎉 所有修复验证测试都通过了！');
  console.log('\n✅ 修复总结:');
  console.log('   🔧 客户搜索下拉菜单可以正常滚动');
  console.log('   🔧 治疗项目搜索下拉菜单可以正常滚动');
  console.log('   🔧 预约类型选择下拉菜单可以正常滚动');
  console.log('   💰 所有货币显示已统一为美元符号($)');
  console.log('   📊 Overview页面已移除dummy数据，显示真实数据');
  console.log('   🔍 客户搜索组件数据访问路径已修复');
  console.log('\n🚀 系统现在可以正常使用了！');
} else {
  console.log('\n⚠️  还有一些问题需要解决');
}

process.exit(failed === 0 ? 0 : 1);
