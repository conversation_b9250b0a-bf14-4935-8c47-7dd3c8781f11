/**
 * Payment Gateway Integrations
 * Comprehensive payment processing with Alipay, WeChat Pay, and other Chinese payment methods
 */

import { logger } from '../logger'
import { monitor } from '../monitoring'
import { AuditLogger } from '../security'
import { logger } from '@/lib/logger';

/**
 * Payment method types
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT_PAY = 'wechat_pay',
  UNION_PAY = 'union_pay',
  CREDIT_CARD = 'credit_card',
  CASH = 'cash',
  BANK_TRANSFER = 'bank_transfer',
}

/**
 * Payment status
 */
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIAL_REFUND = 'partial_refund',
}

/**
 * Payment request interface
 */
export interface PaymentRequest {
  amount: number
  currency: string
  method: PaymentMethod
  description: string
  client_id: string
  appointment_id?: string
  invoice_id?: string
  metadata?: Record<string, unknown>
}

/**
 * Payment response interface
 */
export interface PaymentResponse {
  payment_id: string
  status: PaymentStatus
  amount: number
  currency: string
  method: PaymentMethod
  transaction_id?: string
  qr_code?: string
  deep_link?: string
  expires_at?: Date
  created_at: Date
  metadata?: Record<string, unknown>
}

/**
 * Refund request interface
 */
export interface RefundRequest {
  payment_id: string
  amount?: number // Partial refund if specified
  reason: string
  metadata?: Record<string, unknown>
}

/**
 * Payment Gateway Manager
 */
export class PaymentGatewayManager {
  private gateways = new Map<PaymentMethod, PaymentGateway>()

  constructor() {
    this.initializeGateways()
  }

  /**
   * Initialize payment gateways
   */
  private initializeGateways(): void {
    // Initialize Alipay gateway
    this.gateways.set(PaymentMethod.ALIPAY, new AlipayGateway())
    
    // Initialize WeChat Pay gateway
    this.gateways.set(PaymentMethod.WECHAT_PAY, new WeChatPayGateway())
    
    // Initialize UnionPay gateway
    this.gateways.set(PaymentMethod.UNION_PAY, new UnionPayGateway())
    
    // Initialize Credit Card gateway
    this.gateways.set(PaymentMethod.CREDIT_CARD, new CreditCardGateway())
    
    // Initialize Cash gateway (for in-person payments)
    this.gateways.set(PaymentMethod.CASH, new CashGateway())
    
    // Initialize Bank Transfer gateway
    this.gateways.set(PaymentMethod.BANK_TRANSFER, new BankTransferGateway())

    logger.info('Payment gateways initialized', {
      gatewaysCount: this.gateways.size,
      methods: Array.from(this.gateways.keys()),
    })
  }

  /**
   * Process payment
   */
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const timer = monitor.timer('payment.process')
    
    try {
      const gateway = this.gateways.get(request.method)
      if (!gateway) {
        throw new Error(`Unsupported payment method: ${request.method}`)
      }

      // Validate payment request
      this.validatePaymentRequest(request)

      // Process payment through gateway
      const response = await gateway.processPayment(request)

      // Log payment attempt
      await AuditLogger.logOperation(
        'payment_processed',
        'payment',
        response.payment_id,
        request.client_id,
        {
          amount: request.amount,
          method: request.method,
          status: response.status,
          appointmentId: request.appointment_id,
        }
      )

      timer.stop({ success: 'true', method: request.method })
      monitor.counter('payment.processed', 1, {
        method: request.method,
        status: response.status,
      })

      return response
    } catch (error) {
      timer.stop({ success: 'false', method: request.method })
      logger.error('Payment processing failed', error as Error, {
        method: request.method,
        amount: request.amount,
        clientId: request.client_id,
      })
      throw error
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(paymentId: string, method: PaymentMethod): Promise<PaymentResponse> {
    const gateway = this.gateways.get(method)
    if (!gateway) {
      throw new Error(`Unsupported payment method: ${method}`)
    }

    return gateway.checkPaymentStatus(paymentId)
  }

  /**
   * Process refund
   */
  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    const timer = monitor.timer('payment.refund')
    
    try {
      // Get original payment details
      const payment = await this.getPaymentDetails(request.payment_id)
      const gateway = this.gateways.get(payment.method)
      
      if (!gateway) {
        throw new Error(`Gateway not found for payment method: ${payment.method}`)
      }

      // Process refund
      const refundResponse = await gateway.processRefund(request)

      // Log refund
      await AuditLogger.logOperation(
        'payment_refunded',
        'payment',
        request.payment_id,
        undefined,
        {
          refundId: refundResponse.refund_id,
          amount: refundResponse.amount,
          reason: request.reason,
        }
      )

      timer.stop({ success: 'true', method: payment.method })
      monitor.counter('payment.refunded', 1, {
        method: payment.method,
        amount: refundResponse.amount.toString(),
      })

      return refundResponse
    } catch (error) {
      timer.stop({ success: 'false' })
      logger.error('Refund processing failed', error as Error, {
        paymentId: request.payment_id,
        amount: request.amount,
      })
      throw error
    }
  }

  /**
   * Get supported payment methods
   */
  getSupportedMethods(): PaymentMethod[] {
    return Array.from(this.gateways.keys())
  }

  /**
   * Validate payment request
   */
  private validatePaymentRequest(request: PaymentRequest): void {
    if (request.amount <= 0) {
      throw new Error('Payment amount must be greater than 0')
    }

    if (!request.currency) {
      throw new Error('Currency is required')
    }

    if (!request.client_id) {
      throw new Error('Client ID is required')
    }

    if (!request.description) {
      throw new Error('Payment description is required')
    }
  }

  /**
   * Get payment details (mock implementation)
   */
  private async getPaymentDetails(paymentId: string): Promise<PaymentResponse> {
    // In production, this would query the database
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.ALIPAY,
      created_at: new Date(),
    }
  }
}

/**
 * Abstract payment gateway interface
 */
abstract class PaymentGateway {
  abstract processPayment(request: PaymentRequest): Promise<PaymentResponse>
  abstract checkPaymentStatus(paymentId: string): Promise<PaymentResponse>
  abstract processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }>
}

/**
 * Alipay Gateway Implementation
 */
class AlipayGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    // Mock Alipay integration
    const paymentId = `alipay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // In production, integrate with Alipay SDK
    const qrCode = await this.generateAlipayQRCode(request)
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.PENDING,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.ALIPAY,
      qr_code: qrCode,
      deep_link: `alipays://platformapi/startapp?saId=10000007&qrcode=${encodeURIComponent(qrCode)}`,
      expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    // Mock status check
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.ALIPAY,
      transaction_id: `alipay_txn_${Date.now()}`,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    // Mock refund processing
    return {
      refund_id: `alipay_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }

  private async generateAlipayQRCode(request: PaymentRequest): Promise<string> {
    // Mock QR code generation
    return `https://qr.alipay.com/bax08861qjhokmh6uisj00a7`
  }
}

/**
 * WeChat Pay Gateway Implementation
 */
class WeChatPayGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const paymentId = `wechat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Mock WeChat Pay integration
    const qrCode = await this.generateWeChatQRCode(request)
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.PENDING,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.WECHAT_PAY,
      qr_code: qrCode,
      deep_link: `weixin://wxpay/bizpayurl?pr=${qrCode}`,
      expires_at: new Date(Date.now() + 15 * 60 * 1000),
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.WECHAT_PAY,
      transaction_id: `wechat_txn_${Date.now()}`,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    return {
      refund_id: `wechat_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }

  private async generateWeChatQRCode(request: PaymentRequest): Promise<string> {
    return `weixin://wxpay/bizpayurl?pr=abc123def456`
  }
}

/**
 * UnionPay Gateway Implementation
 */
class UnionPayGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const paymentId = `unionpay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.PROCESSING,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.UNION_PAY,
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.UNION_PAY,
      transaction_id: `unionpay_txn_${Date.now()}`,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    return {
      refund_id: `unionpay_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }
}

/**
 * Credit Card Gateway Implementation
 */
class CreditCardGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const paymentId = `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.CREDIT_CARD,
      transaction_id: `card_txn_${Date.now()}`,
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.CREDIT_CARD,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    return {
      refund_id: `card_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }
}

/**
 * Cash Gateway Implementation
 */
class CashGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const paymentId = `cash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.CASH,
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.CASH,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    return {
      refund_id: `cash_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }
}

/**
 * Bank Transfer Gateway Implementation
 */
class BankTransferGateway extends PaymentGateway {
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const paymentId = `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      payment_id: paymentId,
      status: PaymentStatus.PENDING,
      amount: request.amount,
      currency: request.currency,
      method: PaymentMethod.BANK_TRANSFER,
      created_at: new Date(),
      metadata: request.metadata,
    }
  }

  async checkPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    return {
      payment_id: paymentId,
      status: PaymentStatus.COMPLETED,
      amount: 1000,
      currency: 'CNY',
      method: PaymentMethod.BANK_TRANSFER,
      created_at: new Date(),
    }
  }

  async processRefund(request: RefundRequest): Promise<{
    refund_id: string
    status: PaymentStatus
    amount: number
    processed_at: Date
  }> {
    return {
      refund_id: `transfer_refund_${Date.now()}`,
      status: PaymentStatus.COMPLETED,
      amount: request.amount ?? 1000,
      processed_at: new Date(),
    }
  }
}

/**
 * Global payment gateway manager instance
 */
export const paymentGateway = new PaymentGatewayManager()
