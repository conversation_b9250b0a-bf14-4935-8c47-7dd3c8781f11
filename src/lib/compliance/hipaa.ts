/**
import { logger } from '../logger';
import { monitor } from '../monitoring';
import { AuditLogger, Encryption } from '../security';
import { logger } from '@/lib/logger';

 * HIPAA Compliance Framework
 * Comprehensive implementation of HIPAA requirements for medical data protection
 */







/**
 * PHI (Protected Health Information) Classification
 */
export enum PHIClassification {
  // Direct Identifiers (18 HIPAA identifiers)
  NAMES = 'names',
  GEOGRAPHIC_SUBDIVISIONS = 'geographic_subdivisions',
  DATES = 'dates',
  TELEPHONE_NUMBERS = 'telephone_numbers',
  FAX_NUMBERS = 'fax_numbers',
  EMAIL_ADDRESSES = 'email_addresses',
  SSN = 'social_security_numbers',
  MEDICAL_RECORD_NUMBERS = 'medical_record_numbers',
  HEALTH_PLAN_NUMBERS = 'health_plan_numbers',
  ACCOUNT_NUMBERS = 'account_numbers',
  CERTIFICATE_NUMBERS = 'certificate_numbers',
  VEHICLE_IDENTIFIERS = 'vehicle_identifiers',
  DEVICE_IDENTIFIERS = 'device_identifiers',
  WEB_URLS = 'web_urls',
  IP_ADDRESSES = 'ip_addresses',
  BIOMETRIC_IDENTIFIERS = 'biometric_identifiers',
  PHOTOS = 'photos',
  OTHER_UNIQUE_IDENTIFIERS = 'other_unique_identifiers',
  
  // Medical Information
  MEDICAL_HISTORY = 'medical_history',
  TREATMENT_RECORDS = 'treatment_records',
  DIAGNOSIS = 'diagnosis',
  PRESCRIPTION_INFO = 'prescription_info',
  INSURANCE_INFO = 'insurance_info',
  PAYMENT_INFO = 'payment_info',
  
  // Non-PHI
  NON_PHI = 'non_phi',
}

/**
 * Data sensitivity levels
 */
export enum DataSensitivity {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted', // PHI data
}

/**
 * HIPAA user roles and permissions
 */
export enum HIPAARole {
  COVERED_ENTITY_ADMIN = 'covered_entity_admin',
  PRIVACY_OFFICER = 'privacy_officer',
  SECURITY_OFFICER = 'security_officer',
  HEALTHCARE_PROVIDER = 'healthcare_provider',
  ADMINISTRATIVE_STAFF = 'administrative_staff',
  BUSINESS_ASSOCIATE = 'business_associate',
  PATIENT = 'patient',
  AUDIT_REVIEWER = 'audit_reviewer',
}

/**
 * PHI access purposes (minimum necessary rule)
 */
export enum AccessPurpose {
  TREATMENT = 'treatment',
  PAYMENT = 'payment',
  HEALTHCARE_OPERATIONS = 'healthcare_operations',
  PATIENT_REQUEST = 'patient_request',
  LEGAL_REQUIREMENT = 'legal_requirement',
  EMERGENCY = 'emergency',
  RESEARCH = 'research',
  PUBLIC_HEALTH = 'public_health',
}

/**
 * PHI field metadata
 */
interface PHIFieldMetadata {
  classification: PHIClassification
  sensitivity: DataSensitivity
  encryptionRequired: boolean
  auditRequired: boolean
  retentionPeriod: number // in days
  minimumNecessary: boolean
}

/**
 * HIPAA Compliance Manager
 */
export class HIPAAComplianceManager {
  private phiFieldRegistry = new Map<string, PHIFieldMetadata>()
  private accessLog = new Map<string, Array<{
    userId: string
    timestamp: Date
    purpose: AccessPurpose
    dataAccessed: string[]
  }>>()

  constructor() {
    this.initializePHIRegistry()
  }

  /**
   * Initialize PHI field registry with metadata
   */
  private initializePHIRegistry(): void {
    // Client PHI fields
    this.registerPHIField('clients.first_name', {
      classification: PHIClassification.NAMES,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555, // 7 years
      minimumNecessary: true,
    })

    this.registerPHIField('clients.last_name', {
      classification: PHIClassification.NAMES,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    this.registerPHIField('clients.phone', {
      classification: PHIClassification.TELEPHONE_NUMBERS,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    this.registerPHIField('clients.email', {
      classification: PHIClassification.EMAIL_ADDRESSES,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    this.registerPHIField('clients.date_of_birth', {
      classification: PHIClassification.DATES,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    this.registerPHIField('clients.address_line_1', {
      classification: PHIClassification.GEOGRAPHIC_SUBDIVISIONS,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    // Treatment records
    this.registerPHIField('treatments.description', {
      classification: PHIClassification.TREATMENT_RECORDS,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    // Medical notes and records
    this.registerPHIField('appointments.notes', {
      classification: PHIClassification.MEDICAL_HISTORY,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    // Payment information
    this.registerPHIField('payments.reference_number', {
      classification: PHIClassification.ACCOUNT_NUMBERS,
      sensitivity: DataSensitivity.RESTRICTED,
      encryptionRequired: true,
      auditRequired: true,
      retentionPeriod: 2555,
      minimumNecessary: true,
    })

    logger.info('PHI field registry initialized', {
      totalFields: this.phiFieldRegistry.size,
      restrictedFields: Array.from(this.phiFieldRegistry.values())
        .filter(field => field.sensitivity === DataSensitivity.RESTRICTED).length,
    })
  }

  /**
   * Register a PHI field with its metadata
   */
  registerPHIField(fieldPath: string, metadata: PHIFieldMetadata): void {
    this.phiFieldRegistry.set(fieldPath, metadata)
    monitor.counter('hipaa.phi_field_registered', 1, {
      classification: metadata.classification,
      sensitivity: metadata.sensitivity,
    })
  }

  /**
   * Check if a field contains PHI
   */
  isPHIField(fieldPath: string): boolean {
    const metadata = this.phiFieldRegistry.get(fieldPath)
    return metadata?.sensitivity === DataSensitivity.RESTRICTED
  }

  /**
   * Get PHI field metadata
   */
  getPHIMetadata(fieldPath: string): PHIFieldMetadata | undefined {
    return this.phiFieldRegistry.get(fieldPath)
  }

  /**
   * Encrypt PHI data before storage
   */
  async encryptPHI(data: Record<string, any>, tableName: string): Promise<Record<string, any>> {
    const encryptedData = { ...data }
    
    for (const [key, value] of Object.entries(data)) {
      const fieldPath = `${tableName}.${key}`
      const metadata = this.phiFieldRegistry.get(fieldPath)
      
      if (metadata?.encryptionRequired && value !== null && value !== undefined) {
        try {
          encryptedData[key] = Encryption.encrypt(String(value))
          monitor.counter('hipaa.phi_encrypted', 1, {
            field: fieldPath,
            classification: metadata.classification,
          })
        } catch (error) {
          logger.error('PHI encryption failed', error as Error, { fieldPath })
          throw new Error(`Failed to encrypt PHI field: ${fieldPath}`)
        }
      }
    }
    
    return encryptedData
  }

  /**
   * Decrypt PHI data after retrieval
   */
  async decryptPHI(data: Record<string, any>, tableName: string): Promise<Record<string, any>> {
    const decryptedData = { ...data }
    
    for (const [key, value] of Object.entries(data)) {
      const fieldPath = `${tableName}.${key}`
      const metadata = this.phiFieldRegistry.get(fieldPath)
      
      if (metadata?.encryptionRequired && value !== null && value !== undefined) {
        try {
          decryptedData[key] = Encryption.decrypt(String(value))
          monitor.counter('hipaa.phi_decrypted', 1, {
            field: fieldPath,
            classification: metadata.classification,
          })
        } catch (error) {
          logger.error('PHI decryption failed', error as Error, { fieldPath })
          // Don't throw error for decryption failures, return encrypted value
          decryptedData[key] = value
        }
      }
    }
    
    return decryptedData
  }

  /**
   * Log PHI access for audit trail
   */
  async logPHIAccess(
    userId: string,
    purpose: AccessPurpose,
    dataAccessed: string[],
    metadata?: Record<string, unknown>
  ): Promise<void> {
    const accessRecord = {
      userId,
      timestamp: new Date(),
      purpose,
      dataAccessed,
    }

    // Store in memory log
    if (!this.accessLog.has(userId)) {
      this.accessLog.set(userId, [])
    }
    this.accessLog.get(userId)!.push(accessRecord)

    // Log to audit system
    await AuditLogger.logDataAccess(
      'phi_access',
      dataAccessed.join(','),
      'read',
      userId,
      {
        purpose,
        timestamp: accessRecord.timestamp.toISOString(),
        ...metadata,
      }
    )

    monitor.counter('hipaa.phi_access_logged', 1, {
      purpose,
      userId,
      fieldsAccessed: dataAccessed.length.toString(),
    })
  }

  /**
   * Check if user has permission to access PHI
   */
  async checkPHIAccess(
    userId: string,
    userRole: HIPAARole,
    purpose: AccessPurpose,
    requestedFields: string[]
  ): Promise<{
    allowed: boolean
    allowedFields: string[]
    deniedFields: string[]
    reason?: string
  }> {
    const allowedFields: string[] = []
    const deniedFields: string[] = []

    for (const field of requestedFields) {
      const metadata = this.phiFieldRegistry.get(field)
      
      if (!metadata ?? metadata.sensitivity !== DataSensitivity.RESTRICTED) {
        allowedFields.push(field)
        continue
      }

      // Check role-based access
      const hasRoleAccess = this.checkRoleAccess(userRole, purpose, metadata.classification)
      
      if (hasRoleAccess) {
        allowedFields.push(field)
      } else {
        deniedFields.push(field)
      }
    }

    const allowed = deniedFields.length === 0
    
    monitor.counter('hipaa.access_check', 1, {
      userId,
      userRole,
      purpose,
      allowed: allowed.toString(),
      fieldsRequested: requestedFields.length.toString(),
      fieldsAllowed: allowedFields.length.toString(),
    })

    return {
      allowed,
      allowedFields,
      deniedFields,
      reason: deniedFields.length > 0 ? 'Insufficient permissions for PHI access' : undefined,
    }
  }

  /**
   * Check role-based access permissions
   */
  private checkRoleAccess(
    role: HIPAARole,
    purpose: AccessPurpose,
    classification: PHIClassification
  ): boolean {
    // Define role-based access matrix
    const accessMatrix: Record<HIPAARole, {
      purposes: AccessPurpose[]
      classifications: PHIClassification[]
    }> = {
      [HIPAARole.COVERED_ENTITY_ADMIN]: {
        purposes: Object.values(AccessPurpose),
        classifications: Object.values(PHIClassification),
      },
      [HIPAARole.PRIVACY_OFFICER]: {
        purposes: Object.values(AccessPurpose),
        classifications: Object.values(PHIClassification),
      },
      [HIPAARole.SECURITY_OFFICER]: {
        purposes: [AccessPurpose.HEALTHCARE_OPERATIONS, AccessPurpose.LEGAL_REQUIREMENT],
        classifications: Object.values(PHIClassification),
      },
      [HIPAARole.HEALTHCARE_PROVIDER]: {
        purposes: [AccessPurpose.TREATMENT, AccessPurpose.HEALTHCARE_OPERATIONS, AccessPurpose.EMERGENCY],
        classifications: [
          PHIClassification.NAMES,
          PHIClassification.DATES,
          PHIClassification.MEDICAL_HISTORY,
          PHIClassification.TREATMENT_RECORDS,
          PHIClassification.DIAGNOSIS,
        ],
      },
      [HIPAARole.ADMINISTRATIVE_STAFF]: {
        purposes: [AccessPurpose.PAYMENT, AccessPurpose.HEALTHCARE_OPERATIONS],
        classifications: [
          PHIClassification.NAMES,
          PHIClassification.TELEPHONE_NUMBERS,
          PHIClassification.EMAIL_ADDRESSES,
          PHIClassification.ACCOUNT_NUMBERS,
          PHIClassification.INSURANCE_INFO,
          PHIClassification.PAYMENT_INFO,
        ],
      },
      [HIPAARole.BUSINESS_ASSOCIATE]: {
        purposes: [AccessPurpose.HEALTHCARE_OPERATIONS],
        classifications: [], // Specific to BAA agreement
      },
      [HIPAARole.PATIENT]: {
        purposes: [AccessPurpose.PATIENT_REQUEST],
        classifications: Object.values(PHIClassification), // Own data only
      },
      [HIPAARole.AUDIT_REVIEWER]: {
        purposes: [AccessPurpose.LEGAL_REQUIREMENT],
        classifications: Object.values(PHIClassification),
      },
    }

    const rolePermissions = accessMatrix[role]
    if (!rolePermissions) {
      return false
    }

    return rolePermissions.purposes.includes(purpose) &&
           rolePermissions.classifications.includes(classification)
  }

  /**
   * Generate HIPAA compliance report
   */
  async generateComplianceReport(): Promise<{
    phiFieldsCount: number
    encryptedFieldsCount: number
    accessLogsCount: number
    complianceScore: number
    recommendations: string[]
  }> {
    const phiFields = Array.from(this.phiFieldRegistry.values())
    const phiFieldsCount = phiFields.length
    const encryptedFieldsCount = phiFields.filter(field => field.encryptionRequired).length
    const accessLogsCount = Array.from(this.accessLog.values())
      .reduce((total, logs) => total + logs.length, 0)

    // Calculate compliance score
    const encryptionCompliance = encryptedFieldsCount / phiFieldsCount
    const auditCompliance = phiFields.filter(field => field.auditRequired).length / phiFieldsCount
    const complianceScore = Math.round((encryptionCompliance + auditCompliance) / 2 * 100)

    // Generate recommendations
    const recommendations: string[] = []
    if (encryptionCompliance < 1) {
      recommendations.push('Ensure all PHI fields are encrypted')
    }
    if (auditCompliance < 1) {
      recommendations.push('Enable audit logging for all PHI fields')
    }
    if (accessLogsCount === 0) {
      recommendations.push('Implement PHI access logging')
    }

    return {
      phiFieldsCount,
      encryptedFieldsCount,
      accessLogsCount,
      complianceScore,
      recommendations,
    }
  }

  /**
   * Get PHI access history for a user
   */
  getPHIAccessHistory(userId: string, days: number = 30): Array<{
    timestamp: Date
    purpose: AccessPurpose
    dataAccessed: string[]
  }> {
    const userLogs = this.accessLog.get(userId) || []
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    
    return userLogs.filter(log => log.timestamp >= cutoffDate)
  }
}

/**
 * Global HIPAA compliance manager instance
 */
export const hipaaManager = new HIPAAComplianceManager()

/**
 * HIPAA compliance utilities
 */
export const hipaaUtils = {
  /**
   * Sanitize data for logging (remove PHI)
   */
  sanitizeForLogging: (data: Record<string, any>, tableName: string): Record<string, any> => {
    const sanitized = { ...data }
    
    for (const [key, value] of Object.entries(data)) {
      const fieldPath = `${tableName}.${key}`
      if (hipaaManager.isPHIField(fieldPath)) {
        sanitized[key] = '***PHI***'
      }
    }
    
    return sanitized
  },

  /**
   * Check if data contains PHI
   */
  containsPHI: (data: Record<string, any>, tableName: string): boolean => {
    return Object.keys(data).some(key => 
      hipaaManager.isPHIField(`${tableName}.${key}`)
    )
  },

  /**
   * Get minimum necessary fields for a purpose
   */
  getMinimumNecessaryFields: (
    allFields: string[],
    tableName: string,
    purpose: AccessPurpose
  ): string[] => {
    // Implementation would depend on specific business rules
    // For now, return all requested fields
    return allFields
  },
}
