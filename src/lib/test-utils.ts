/**
 * Professional Testing Utilities
 * Provides comprehensive testing helpers for unit, integration, and E2E tests
 */

import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'

/**
 * Custom render function with providers
 */
export function renderWithProviders(
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  // TODO: Add providers like ThemeProvider, QueryClient, etc.
  return render(ui, options)
}

/**
 * Mock NextRequest for API testing
 */
export function createMockRequest(options: {
  method?: string
  url?: string
  body?: unknown
  headers?: Record<string, string>
  searchParams?: Record<string, string>
}): NextRequest {
  const {
    method = 'GET',
    url = 'http://localhost:3000/api/test',
    body,
    headers = {},
    searchParams = {},
  } = options

  const urlWithParams = new URL(url)
  Object.entries(searchParams).forEach(([key, value]) => {
    urlWithParams.searchParams.set(key, value)
  })

  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  }

  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestInit.body = JSON.stringify(body)
  }

  return new NextRequest(urlWithParams.toString(), requestInit)
}

/**
 * Test data factories using Faker
 */
export const testDataFactory = {
  client: (overrides?: Partial<any>) => ({
    id: faker.string.uuid(),
    first_name: faker.person.firstName(),
    last_name: faker.person.lastName(),
    phone: faker.phone.number(),
    email: faker.internet.email(),
    date_of_birth: faker.date.past({ years: 50 }).toISOString().split('T')[0],
    address_line_1: faker.location.streetAddress(),
    city: faker.location.city(),
    state_province: faker.location.state(),
    postal_code: faker.location.zipCode(),
    country: '美国',
    emergency_contact_name: faker.person.fullName(),
    emergency_contact_phone: faker.phone.number(),
    referral_source: faker.company.name(),
    notes: faker.lorem.paragraph(),
    preferred_language: 'zh-CN',
    status: 'active',
    created_at: faker.date.recent().toISOString(),
    updated_at: faker.date.recent().toISOString(),
    ...overrides,
  }),

  treatment: (overrides?: Partial<any>) => ({
    id: faker.string.uuid(),
    name_chinese: faker.lorem.words(2),
    name_english: faker.lorem.words(2),
    category: faker.lorem.word(),
    price: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
    duration_minutes: faker.number.int({ min: 30, max: 240 }),
    fixed_deposit_amount: faker.number.float({ min: 50, max: 1000, fractionDigits: 2 }),
    requires_consultation: faker.datatype.boolean(),
    description: faker.lorem.paragraph(),
    is_active: true,
    created_at: faker.date.recent().toISOString(),
    updated_at: faker.date.recent().toISOString(),
    ...overrides,
  }),

  appointment: (overrides?: Partial<any>) => ({
    id: faker.string.uuid(),
    client_id: faker.string.uuid(),
    treatment_id: faker.string.uuid(),
    appointment_date: faker.date.future().toISOString().split('T')[0],
    start_time: '10:00',
    end_time: '11:00',
    status: 'scheduled',
    custom_price: null,
    notes: faker.lorem.sentence(),
    created_at: faker.date.recent().toISOString(),
    updated_at: faker.date.recent().toISOString(),
    ...overrides,
  }),

  invoice: (overrides?: Partial<any>) => ({
    id: faker.string.uuid(),
    client_id: faker.string.uuid(),
    invoice_number: `INV-${faker.number.int({ min: 1000, max: 9999 })}`,
    total_amount: faker.number.float({ min: 100, max: 2000, fractionDigits: 2 }),
    deposit_amount: faker.number.float({ min: 50, max: 1000, fractionDigits: 2 }),
    status: 'draft',
    due_date: faker.date.future().toISOString().split('T')[0],
    notes: faker.lorem.sentence(),
    created_at: faker.date.recent().toISOString(),
    updated_at: faker.date.recent().toISOString(),
    ...overrides,
  }),

  payment: (overrides?: Partial<any>) => ({
    id: faker.string.uuid(),
    invoice_id: faker.string.uuid(),
    amount: faker.number.float({ min: 50, max: 1000, fractionDigits: 2 }),
    payment_method: faker.helpers.arrayElement(['cash', 'card', 'bank_transfer', 'alipay', 'wechat_pay']),
    reference_number: faker.string.alphanumeric(10),
    notes: faker.lorem.sentence(),
    created_at: faker.date.recent().toISOString(),
    ...overrides,
  }),
}

/**
 * Database test helpers
 */
export const dbTestHelpers = {
  /**
   * Clean up test data after tests
   */
  async cleanup(tables: string[]) {
    // TODO: Implement database cleanup for test data
    // This would connect to a test database and clean up
    console.log(`Cleaning up test data from tables: ${tables.join(', ')}`)
  },

  /**
   * Seed test data
   */
  async seed(data: Record<string, any[]>) {
    // TODO: Implement database seeding for tests
    // This would insert test data into the test database
    console.log('Seeding test data:', Object.keys(data))
  },
}

/**
 * API test helpers
 */
export const apiTestHelpers = {
  /**
   * Test API endpoint with various scenarios
   */
  async testEndpoint(
    handler: Function,
    scenarios: Array<{
      name: string
      request: Parameters<typeof createMockRequest>[0]
      expectedStatus: number
      expectedBody?: unknown
      setup?: () => Promise<void>
      cleanup?: () => Promise<void>
    }>
  ) {
    for (const scenario of scenarios) {
      if (scenario.setup) {
        await scenario.setup()
      }

      try {
        const request = createMockRequest(scenario.request)
        const response = await handler(request)
        
        expect(response.status).toBe(scenario.expectedStatus)
        
        if (scenario.expectedBody) {
          const body = await response.json()
          expect(body).toMatchObject(scenario.expectedBody)
        }
      } finally {
        if (scenario.cleanup) {
          await scenario.cleanup()
        }
      }
    }
  },
}

/**
 * Performance test helpers
 */
export const performanceTestHelpers = {
  /**
   * Measure function execution time
   */
  async measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    return { result, duration: end - start }
  },

  /**
   * Test function performance with multiple iterations
   */
  async benchmarkFunction<T>(
    fn: () => Promise<T>,
    iterations: number = 100
  ): Promise<{
    averageTime: number
    minTime: number
    maxTime: number
    totalTime: number
  }> {
    const times: number[] = []
    
    for (let i = 0; i < iterations; i++) {
      const { duration } = await this.measureTime(fn)
      times.push(duration)
    }
    
    return {
      averageTime: times.reduce((a, b) => a + b, 0) / times.length,
      minTime: Math.min(...times),
      maxTime: Math.max(...times),
      totalTime: times.reduce((a, b) => a + b, 0),
    }
  },
}

/**
 * Mock implementations for external services
 */
export const mockServices = {
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      order: jest.fn().mockReturnThis(),
    })),
  },

  clerk: {
    getUser: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
  },
}

/**
 * Test environment setup
 */
export const testSetup = {
  /**
   * Setup test environment before all tests
   */
  beforeAll: async () => {
    // Set test environment variables
    process.env.NODE_ENV = 'test'
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
  },

  /**
   * Cleanup after all tests
   */
  afterAll: async () => {
    // Cleanup test data and connections
    await dbTestHelpers.cleanup(['clients', 'treatments', 'appointments', 'invoices', 'payments'])
  },

  /**
   * Setup before each test
   */
  beforeEach: async () => {
    // Reset mocks
    jest.clearAllMocks()
  },

  /**
   * Cleanup after each test
   */
  afterEach: async () => {
    // Cleanup any test-specific data
  },
}

/**
 * Custom Jest matchers for API testing
 */
export const customMatchers = {
  toBeValidApiResponse: (received: unknown) => {
    const pass = received && 
                 typeof received.success === 'boolean' &&
                 received.timestamp &&
                 (received.data !== undefined || received.error !== undefined)
    
    return {
      message: () => `expected ${received} to be a valid API response`,
      pass,
    }
  },

  toHaveValidPagination: (received: unknown) => {
    const pass = received?.pagination &&
                 typeof received.pagination.page === 'number' &&
                 typeof received.pagination.limit === 'number' &&
                 typeof received.pagination.total === 'number' &&
                 typeof received.pagination.totalPages === 'number'
    
    return {
      message: () => `expected ${received} to have valid pagination`,
      pass,
    }
  },
}

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidApiResponse(): R
      toHaveValidPagination(): R
    }
  }
}
