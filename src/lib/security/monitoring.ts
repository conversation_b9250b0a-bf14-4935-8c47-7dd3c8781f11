/**
 * Security Monitoring and Threat Detection System
 * Real-time security monitoring, intrusion detection, and incident response
 */

import { logger } from '../logger'
import { monitor } from '../monitoring'
import { AuditLogger } from '../security'
import { logger } from '@/lib/logger';

/**
 * Security event types
 */
export enum SecurityEventType {
  // Authentication events
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  SESSION_EXPIRED = 'session_expired',
  MFA_CHALLENGE = 'mfa_challenge',
  MFA_SUCCESS = 'mfa_success',
  MFA_FAILURE = 'mfa_failure',
  
  // Authorization events
  ACCESS_GRANTED = 'access_granted',
  ACCESS_DENIED = 'access_denied',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  UNAUTHORIZED_ACCESS_ATTEMPT = 'unauthorized_access_attempt',
  
  // Data access events
  PHI_ACCESS = 'phi_access',
  PHI_EXPORT = 'phi_export',
  PHI_MODIFICATION = 'phi_modification',
  PHI_DELETION = 'phi_deletion',
  BULK_DATA_ACCESS = 'bulk_data_access',
  
  // System events
  CONFIGURATION_CHANGE = 'configuration_change',
  USER_CREATED = 'user_created',
  USER_MODIFIED = 'user_modified',
  USER_DELETED = 'user_deleted',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REMOVED = 'role_removed',
  
  // Security threats
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',
  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',
  XSS_ATTEMPT = 'xss_attempt',
  CSRF_ATTEMPT = 'csrf_attempt',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  MALWARE_DETECTED = 'malware_detected',
  
  // System security
  FIREWALL_BLOCK = 'firewall_block',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  ENCRYPTION_FAILURE = 'encryption_failure',
  CERTIFICATE_EXPIRY = 'certificate_expiry',
  BACKUP_FAILURE = 'backup_failure',
}

/**
 * Security event severity levels
 */
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Security event interface
 */
export interface SecurityEvent {
  id: string
  type: SecurityEventType
  severity: SecuritySeverity
  timestamp: Date
  userId?: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
  resource?: string
  action?: string
  details: Record<string, unknown>
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

/**
 * Security alert interface
 */
export interface SecurityAlert {
  id: string
  eventId: string
  title: string
  description: string
  severity: SecuritySeverity
  createdAt: Date
  acknowledged: boolean
  acknowledgedAt?: Date
  acknowledgedBy?: string
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

/**
 * Threat detection rule
 */
export interface ThreatDetectionRule {
  id: string
  name: string
  description: string
  eventTypes: SecurityEventType[]
  conditions: {
    timeWindow: number // minutes
    threshold: number
    field?: string
    value?: string
  }
  severity: SecuritySeverity
  enabled: boolean
  actions: string[] // alert, block, notify
}

/**
 * Security Monitoring System
 */
export class SecurityMonitor {
  private events: SecurityEvent[] = []
  private alerts: SecurityAlert[] = []
  private rules: ThreatDetectionRule[] = []
  private blockedIPs = new Set<string>()
  private suspiciousUsers = new Map<string, number>()

  constructor() {
    this.initializeDefaultRules()
    this.startMonitoring()
  }

  /**
   * Initialize default threat detection rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: ThreatDetectionRule[] = [
      {
        id: 'brute_force_login',
        name: '暴力破解登录检测',
        description: '检测短时间内多次登录失败',
        eventTypes: [SecurityEventType.LOGIN_FAILURE],
        conditions: {
          timeWindow: 15, // 15 minutes
          threshold: 5,
          field: 'userId',
        },
        severity: SecuritySeverity.HIGH,
        enabled: true,
        actions: ['alert', 'block'],
      },
      {
        id: 'suspicious_phi_access',
        name: '可疑PHI访问检测',
        description: '检测异常的PHI数据访问模式',
        eventTypes: [SecurityEventType.PHI_ACCESS],
        conditions: {
          timeWindow: 60, // 1 hour
          threshold: 50,
          field: 'userId',
        },
        severity: SecuritySeverity.MEDIUM,
        enabled: true,
        actions: ['alert', 'notify'],
      },
      {
        id: 'bulk_data_export',
        name: '批量数据导出检测',
        description: '检测大量数据导出操作',
        eventTypes: [SecurityEventType.PHI_EXPORT],
        conditions: {
          timeWindow: 30, // 30 minutes
          threshold: 3,
          field: 'userId',
        },
        severity: SecuritySeverity.HIGH,
        enabled: true,
        actions: ['alert', 'notify'],
      },
      {
        id: 'unauthorized_access',
        name: '未授权访问检测',
        description: '检测未授权的系统访问尝试',
        eventTypes: [SecurityEventType.ACCESS_DENIED],
        conditions: {
          timeWindow: 10, // 10 minutes
          threshold: 10,
          field: 'userId',
        },
        severity: SecuritySeverity.MEDIUM,
        enabled: true,
        actions: ['alert'],
      },
      {
        id: 'privilege_escalation',
        name: '权限提升检测',
        description: '检测权限提升尝试',
        eventTypes: [SecurityEventType.PRIVILEGE_ESCALATION],
        conditions: {
          timeWindow: 5, // 5 minutes
          threshold: 1,
        },
        severity: SecuritySeverity.CRITICAL,
        enabled: true,
        actions: ['alert', 'block', 'notify'],
      },
    ]

    this.rules = defaultRules
    logger.info('Security monitoring rules initialized', {
      rulesCount: this.rules.length,
      enabledRules: this.rules.filter(r => r.enabled).length,
    })
  }

  /**
   * Start monitoring processes
   */
  private startMonitoring(): void {
    // Run threat detection every minute
    setInterval(() => {
      this.runThreatDetection()
    }, 60 * 1000)

    // Clean up old events every hour
    setInterval(() => {
      this.cleanupOldEvents()
    }, 60 * 60 * 1000)

    logger.info('Security monitoring started')
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    type: SecurityEventType,
    severity: SecuritySeverity,
    details: Record<string, unknown>,
    userId?: string,
    sessionId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<SecurityEvent> {
    const event: SecurityEvent = {
      id: `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      timestamp: new Date(),
      userId,
      sessionId,
      ipAddress,
      userAgent,
      details,
      resolved: false,
    }

    this.events.push(event)

    // Log to audit system
    await AuditLogger.logOperation(
      'security_event',
      'security',
      event.id,
      userId,
      {
        eventType: type,
        severity,
        details,
        ipAddress,
        userAgent,
      }
    )

    // Monitor metrics
    monitor.counter('security.event_logged', 1, {
      type,
      severity,
      userId: userId ?? 'anonymous',
    })

    // Check for immediate threats
    await this.checkImmediateThreats(event)

    logger.info('Security event logged', {
      eventId: event.id,
      type,
      severity,
      userId,
    })

    return event
  }

  /**
   * Check for immediate threats that require instant response
   */
  private async checkImmediateThreats(event: SecurityEvent): Promise<void> {
    // Block IP for critical events
    if (event.severity === SecuritySeverity.CRITICAL && event.ipAddress) {
      await this.blockIP(event.ipAddress, `Critical security event: ${event.type}`)
    }

    // Mark user as suspicious for high severity events
    if (event.severity === SecuritySeverity.HIGH && event.userId) {
      const suspiciousCount = (this.suspiciousUsers.get(event.userId) || 0) + 1
      this.suspiciousUsers.set(event.userId, suspiciousCount)

      if (suspiciousCount >= 3) {
        await this.createAlert(
          event.id,
          '用户可疑活动',
          `用户 ${event.userId} 在短时间内触发多个高风险安全事件`,
          SecuritySeverity.HIGH
        )
      }
    }
  }

  /**
   * Run threat detection rules
   */
  private async runThreatDetection(): Promise<void> {
    const now = new Date()

    for (const rule of this.rules.filter(r => r.enabled)) {
      try {
        const windowStart = new Date(now.getTime() - rule.conditions.timeWindow * 60 * 1000)
        
        // Get events in time window
        const relevantEvents = this.events.filter(event =>
          rule.eventTypes.includes(event.type) &&
          event.timestamp >= windowStart &&
          event.timestamp <= now
        )

        // Group by field if specified
        if (rule.conditions.field) {
          const groupedEvents = new Map<string, SecurityEvent[]>()
          
          for (const event of relevantEvents) {
            const fieldValue = event[rule.conditions.field as keyof SecurityEvent] as string ?? 'unknown'
            if (!groupedEvents.has(fieldValue)) {
              groupedEvents.set(fieldValue, [])
            }
            groupedEvents.get(fieldValue)!.push(event)
          }

          // Check threshold for each group
          for (const [fieldValue, events] of groupedEvents) {
            if (events.length >= rule.conditions.threshold) {
              await this.triggerRuleAction(rule, events, fieldValue)
            }
          }
        } else {
          // Check overall threshold
          if (relevantEvents.length >= rule.conditions.threshold) {
            await this.triggerRuleAction(rule, relevantEvents)
          }
        }
      } catch (error) {
        logger.error('Threat detection rule failed', error as Error, {
          ruleId: rule.id,
          ruleName: rule.name,
        })
      }
    }
  }

  /**
   * Trigger rule actions
   */
  private async triggerRuleAction(
    rule: ThreatDetectionRule,
    events: SecurityEvent[],
    fieldValue?: string
  ): Promise<void> {
    const latestEvent = events[events.length - 1]

    // Create alert
    if (rule.actions.includes('alert')) {
      await this.createAlert(
        latestEvent.id,
        rule.name,
        `${rule.description}${fieldValue ? ` (${fieldValue})` : ''}`,
        rule.severity
      )
    }

    // Block IP or user
    if (rule.actions.includes('block')) {
      if (latestEvent.ipAddress) {
        await this.blockIP(latestEvent.ipAddress, `Triggered rule: ${rule.name}`)
      }
      if (latestEvent.userId) {
        await this.blockUser(latestEvent.userId, `Triggered rule: ${rule.name}`)
      }
    }

    // Send notification
    if (rule.actions.includes('notify')) {
      await this.sendSecurityNotification(rule, events, fieldValue)
    }

    monitor.counter('security.rule_triggered', 1, {
      ruleId: rule.id,
      severity: rule.severity,
      eventsCount: events.length.toString(),
    })
  }

  /**
   * Create security alert
   */
  private async createAlert(
    eventId: string,
    title: string,
    description: string,
    severity: SecuritySeverity
  ): Promise<SecurityAlert> {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      eventId,
      title,
      description,
      severity,
      createdAt: new Date(),
      acknowledged: false,
      resolved: false,
    }

    this.alerts.push(alert)

    logger.warn('Security alert created', {
      alertId: alert.id,
      title,
      severity,
      eventId,
    })

    monitor.counter('security.alert_created', 1, { severity })

    return alert
  }

  /**
   * Block IP address
   */
  private async blockIP(ipAddress: string, reason: string): Promise<void> {
    this.blockedIPs.add(ipAddress)

    await AuditLogger.logOperation(
      'ip_blocked',
      'security',
      ipAddress,
      undefined,
      { reason, blockedAt: new Date().toISOString() }
    )

    logger.warn('IP address blocked', { ipAddress, reason })
    monitor.counter('security.ip_blocked', 1, { reason })
  }

  /**
   * Block user
   */
  private async blockUser(userId: string, reason: string): Promise<void> {
    // TODO: Implement user blocking logic
    // This would typically involve updating user status in database

    await AuditLogger.logOperation(
      'user_blocked',
      'security',
      userId,
      undefined,
      { reason, blockedAt: new Date().toISOString() }
    )

    logger.warn('User blocked', { userId, reason })
    monitor.counter('security.user_blocked', 1, { reason })
  }

  /**
   * Send security notification
   */
  private async sendSecurityNotification(
    rule: ThreatDetectionRule,
    events: SecurityEvent[],
    fieldValue?: string
  ): Promise<void> {
    // TODO: Implement notification system (email, SMS, Slack, etc.)
    logger.info('Security notification sent', {
      ruleId: rule.id,
      ruleName: rule.name,
      eventsCount: events.length,
      fieldValue,
    })

    monitor.counter('security.notification_sent', 1, {
      ruleId: rule.id,
      severity: rule.severity,
    })
  }

  /**
   * Clean up old events
   */
  private cleanupOldEvents(): void {
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days
    const initialCount = this.events.length

    this.events = this.events.filter(event => event.timestamp >= cutoffDate)
    this.alerts = this.alerts.filter(alert => alert.createdAt >= cutoffDate)

    const cleanedCount = initialCount - this.events.length
    if (cleanedCount > 0) {
      logger.info('Security events cleaned up', {
        cleanedEvents: cleanedCount,
        remainingEvents: this.events.length,
      })
    }
  }

  /**
   * Get security events
   */
  getSecurityEvents(
    limit: number = 100,
    severity?: SecuritySeverity,
    type?: SecurityEventType
  ): SecurityEvent[] {
    let filteredEvents = this.events

    if (severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === severity)
    }

    if (type) {
      filteredEvents = filteredEvents.filter(event => event.type === type)
    }

    return filteredEvents
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit)
  }

  /**
   * Get security alerts
   */
  getSecurityAlerts(limit: number = 50, unacknowledged: boolean = false): SecurityAlert[] {
    let filteredAlerts = this.alerts

    if (unacknowledged) {
      filteredAlerts = filteredAlerts.filter(alert => !alert.acknowledged)
    }

    return filteredAlerts
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit)
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<boolean> {
    const alert = this.alerts.find(a => a.id === alertId)
    if (!alert) {
      return false
    }

    alert.acknowledged = true
    alert.acknowledgedAt = new Date()
    alert.acknowledgedBy = acknowledgedBy

    await AuditLogger.logOperation(
      'alert_acknowledged',
      'security',
      alertId,
      acknowledgedBy
    )

    monitor.counter('security.alert_acknowledged', 1, { severity: alert.severity })
    return true
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ipAddress: string): boolean {
    return this.blockedIPs.has(ipAddress)
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics(): {
    totalEvents: number
    eventsBySeverity: Record<SecuritySeverity, number>
    totalAlerts: number
    unacknowledgedAlerts: number
    blockedIPs: number
    suspiciousUsers: number
  } {
    const eventsBySeverity = {
      [SecuritySeverity.LOW]: 0,
      [SecuritySeverity.MEDIUM]: 0,
      [SecuritySeverity.HIGH]: 0,
      [SecuritySeverity.CRITICAL]: 0,
    }

    for (const event of this.events) {
      eventsBySeverity[event.severity]++
    }

    return {
      totalEvents: this.events.length,
      eventsBySeverity,
      totalAlerts: this.alerts.length,
      unacknowledgedAlerts: this.alerts.filter(a => !a.acknowledged).length,
      blockedIPs: this.blockedIPs.size,
      suspiciousUsers: this.suspiciousUsers.size,
    }
  }
}

/**
 * Global security monitor instance
 */
export const securityMonitor = new SecurityMonitor()
