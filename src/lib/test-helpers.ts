/**
 * Enhanced Test Helpers
 * Provides comprehensive testing utilities for the application
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { logger } from '@/lib/logger';

// Mock data generators
export const mockDataGenerators = {
  // Generate mock client data
  client: (overrides: Partial<any> = {}) => ({
    id: `client-${Math.random().toString(36).substr(2, 9)}`,
    first_name: '张',
    last_name: '三',
    email: '<EMAIL>',
    phone: '**********',
    date_of_birth: '1990-01-01',
    gender: 'male',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    notes: '',
    ...overrides
  }),

  // Generate mock appointment data
  appointment: (overrides: Partial<any> = {}) => ({
    id: `appointment-${Math.random().toString(36).substr(2, 9)}`,
    client_id: `client-${Math.random().toString(36).substr(2, 9)}`,
    treatment_id: `treatment-${Math.random().toString(36).substr(2, 9)}`,
    appointment_date: new Date().toISOString().split('T')[0],
    start_time: '10:00',
    end_time: '11:00',
    appointment_type: 'treatment',
    status: 'scheduled',
    notes: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    clients: mockDataGenerators.client(),
    treatments: mockDataGenerators.treatment(),
    ...overrides
  }),

  // Generate mock treatment data
  treatment: (overrides: Partial<any> = {}) => ({
    id: `treatment-${Math.random().toString(36).substr(2, 9)}`,
    name_chinese: '面部护理',
    name_english: 'Facial Treatment',
    category: 'facial',
    default_price: 299.99,
    duration_minutes: 60,
    description: '专业面部护理服务',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  }),

  // Generate mock payment data
  payment: (overrides: Partial<any> = {}) => ({
    id: `payment-${Math.random().toString(36).substr(2, 9)}`,
    invoice_id: `invoice-${Math.random().toString(36).substr(2, 9)}`,
    amount: 299.99,
    payment_method: 'card',
    payment_date: new Date().toISOString().split('T')[0],
    notes: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  }),

  // Generate mock invoice data
  invoice: (overrides: Partial<any> = {}) => ({
    id: `invoice-${Math.random().toString(36).substr(2, 9)}`,
    client_id: `client-${Math.random().toString(36).substr(2, 9)}`,
    treatment_id: `treatment-${Math.random().toString(36).substr(2, 9)}`,
    total_amount: 299.99,
    deposit_amount: 100.00,
    status: 'pending',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    notes: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    clients: mockDataGenerators.client(),
    treatments: mockDataGenerators.treatment(),
    ...overrides
  })
};

// API mocking utilities
export const apiMocks = {
  // Mock successful API response
  success: <T>(data: T, delay = 100) => {
    return new Promise<T>((resolve) => {
      setTimeout(() => resolve(data), delay);
    });
  },

  // Mock API error
  error: (message = 'API Error', status = 500, delay = 100) => {
    return new Promise((_, reject) => {
      setTimeout(() => {
        const error = new Error(message);
        (error as any).status = status;
        reject(error);
      }, delay);
    });
  },

  // Mock fetch function
  mockFetch: (responses: Record<string, any>) => {
    global.fetch = jest.fn((url: string, options?: RequestInit) => {
      const method = options?.method ?? 'GET';
      const key = `${method} ${url}`;
      
      if (responses[key]) {
        const response = responses[key];
        return Promise.resolve({
          ok: response.status < 400,
          status: response.status ?? 200,
          json: () => Promise.resolve(response.data),
          text: () => Promise.resolve(JSON.stringify(response.data))
        } as Response);
      }

      return Promise.reject(new Error(`No mock response for ${key}`));
    });
  },

  // Reset fetch mock
  resetFetch: () => {
    if (jest.isMockFunction(global.fetch)) {
      (global.fetch as jest.Mock).mockRestore();
    }
  }
};

// Test wrapper components
export const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
};

// Enhanced render function with providers
export const renderWithProviders = (ui: React.ReactElement, options = {}) => {
  return render(ui, {
    wrapper: TestWrapper,
    ...options,
  });
};

// Form testing utilities
export const formTestUtils = {
  // Fill form field
  fillField: async (labelText: string, value: string) => {
    const field = screen.getByLabelText(labelText);
    await userEvent.clear(field);
    await userEvent.type(field, value);
  },

  // Select option from dropdown
  selectOption: async (labelText: string, optionText: string) => {
    const select = screen.getByLabelText(labelText);
    await userEvent.click(select);
    const option = screen.getByText(optionText);
    await userEvent.click(option);
  },

  // Submit form
  submitForm: async (buttonText = '提交') => {
    const submitButton = screen.getByRole('button', { name: buttonText });
    await userEvent.click(submitButton);
  },

  // Check form validation
  expectValidationError: async (message: string) => {
    await waitFor(() => {
      expect(screen.getByText(message)).toBeInTheDocument();
    });
  },

  // Check form success
  expectFormSuccess: async (message: string) => {
    await waitFor(() => {
      expect(screen.getByText(message)).toBeInTheDocument();
    });
  }
};

// Table testing utilities
export const tableTestUtils = {
  // Get table rows
  getTableRows: () => {
    const table = screen.getByRole('table');
    return table.querySelectorAll('tbody tr');
  },

  // Get table cell by row and column
  getTableCell: (rowIndex: number, columnIndex: number) => {
    const rows = tableTestUtils.getTableRows();
    const row = rows[rowIndex];
    return row?.querySelectorAll('td')[columnIndex];
  },

  // Check table data
  expectTableData: (rowIndex: number, columnIndex: number, expectedText: string) => {
    const cell = tableTestUtils.getTableCell(rowIndex, columnIndex);
    expect(cell).toHaveTextContent(expectedText);
  },

  // Sort table by column
  sortByColumn: async (columnName: string) => {
    const header = screen.getByText(columnName);
    await userEvent.click(header);
  },

  // Filter table
  filterTable: async (filterValue: string) => {
    const filterInput = screen.getByPlaceholderText(/搜索|筛选/i);
    await userEvent.clear(filterInput);
    await userEvent.type(filterInput, filterValue);
  }
};

// Modal testing utilities
export const modalTestUtils = {
  // Open modal
  openModal: async (triggerText: string) => {
    const trigger = screen.getByText(triggerText);
    await userEvent.click(trigger);
  },

  // Close modal
  closeModal: async () => {
    const closeButton = screen.getByRole('button', { name: /关闭|取消/i });
    await userEvent.click(closeButton);
  },

  // Check modal is open
  expectModalOpen: (title: string) => {
    expect(screen.getByText(title)).toBeInTheDocument();
  },

  // Check modal is closed
  expectModalClosed: (title: string) => {
    expect(screen.queryByText(title)).not.toBeInTheDocument();
  }
};

// Loading state testing utilities
export const loadingTestUtils = {
  // Check loading state
  expectLoading: () => {
    expect(screen.getByText(/加载中|Loading/i)).toBeInTheDocument();
  },

  // Check loading completed
  expectLoadingComplete: () => {
    expect(screen.queryByText(/加载中|Loading/i)).not.toBeInTheDocument();
  },

  // Wait for loading to complete
  waitForLoadingComplete: async () => {
    await waitFor(() => {
      expect(screen.queryByText(/加载中|Loading/i)).not.toBeInTheDocument();
    });
  }
};

// Error state testing utilities
export const errorTestUtils = {
  // Check error message
  expectError: (message: string) => {
    expect(screen.getByText(message)).toBeInTheDocument();
  },

  // Check error toast
  expectErrorToast: async (message: string) => {
    await waitFor(() => {
      expect(screen.getByText(message)).toBeInTheDocument();
    });
  },

  // Check success toast
  expectSuccessToast: async (message: string) => {
    await waitFor(() => {
      expect(screen.getByText(message)).toBeInTheDocument();
    });
  }
};

// Performance testing utilities
export const performanceTestUtils = {
  // Measure render time
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    await waitFor(() => {
      // Wait for component to be fully rendered
    });
    const end = performance.now();
    return end - start;
  },

  // Check component renders within time limit
  expectRenderWithinTime: async (renderFn: () => void, maxTime: number) => {
    const renderTime = await performanceTestUtils.measureRenderTime(renderFn);
    expect(renderTime).toBeLessThan(maxTime);
  }
};

// Accessibility testing utilities
export const a11yTestUtils = {
  // Check ARIA labels
  expectAriaLabel: (element: HTMLElement, label: string) => {
    expect(element).toHaveAttribute('aria-label', label);
  },

  // Check keyboard navigation
  expectKeyboardNavigation: async (element: HTMLElement) => {
    element.focus();
    expect(element).toHaveFocus();
    
    fireEvent.keyDown(element, { key: 'Tab' });
    // Check that focus moves to next element
  },

  // Check screen reader text
  expectScreenReaderText: (text: string) => {
    const srElement = screen.getByText(text);
    expect(srElement).toHaveClass('sr-only');
  }
};

// Test data cleanup utilities
export const cleanupUtils = {
  // Clear all mocks
  clearAllMocks: () => {
    jest.clearAllMocks();
    apiMocks.resetFetch();
  },

  // Reset test environment
  resetTestEnvironment: () => {
    cleanupUtils.clearAllMocks();
    // Clear any global state
    localStorage.clear();
    sessionStorage.clear();
  }
};

// Test suite helpers
export const testSuiteHelpers = {
  // Setup test suite
  setupTestSuite: (suiteName: string) => {
    logger.info(`Setting up test suite: ${suiteName}`);
    
    beforeEach(() => {
      cleanupUtils.resetTestEnvironment();
    });

    afterEach(() => {
      cleanupUtils.clearAllMocks();
    });
  },

  // Create test group
  createTestGroup: (groupName: string, tests: () => void) => {
    describe(groupName, () => {
      testSuiteHelpers.setupTestSuite(groupName);
      tests();
    });
  }
};
