/**
 * Business Intelligence & Analytics Engine
 * Advanced analytics, predictive modeling, and business insights for medical aesthetics clinic
 */

import { logger } from '../logger'
import { monitor } from '../monitoring'
import { db } from '../database/performance'
import { logger } from '@/lib/logger';

/**
 * Business metrics interface
 */
export interface BusinessMetrics {
  revenue: {
    total: number
    monthly: number
    daily: number
    growth: number
    forecast: number[]
  }
  clients: {
    total: number
    new: number
    returning: number
    retention: number
    lifetime_value: number
  }
  appointments: {
    total: number
    completed: number
    cancelled: number
    no_show: number
    utilization: number
  }
  treatments: {
    popular: Array<{ name: string; count: number; revenue: number }>
    effectiveness: Array<{ name: string; satisfaction: number; repeat_rate: number }>
    seasonal_trends: Array<{ month: string; treatments: number }>
  }
  staff: {
    productivity: Array<{ name: string; appointments: number; revenue: number }>
    utilization: number
    satisfaction: number
  }
}

/**
 * Predictive analytics interface
 */
export interface PredictiveAnalytics {
  client_behavior: {
    churn_risk: Array<{ client_id: string; risk_score: number; factors: string[] }>
    next_treatment: Array<{ client_id: string; treatment: string; probability: number }>
    lifetime_value: Array<{ client_id: string; predicted_value: number }>
  }
  revenue_forecast: {
    next_month: number
    next_quarter: number
    confidence_interval: [number, number]
    factors: string[]
  }
  appointment_demand: {
    peak_hours: Array<{ hour: number; demand: number }>
    seasonal_patterns: Array<{ month: string; demand: number }>
    treatment_trends: Array<{ treatment: string; trend: 'increasing' | 'decreasing' | 'stable' }>
  }
}

/**
 * Business Intelligence Engine
 */
export class BusinessIntelligenceEngine {
  private metricsCache = new Map<string, { data: Record<string, unknown>; timestamp: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  /**
   * Get comprehensive business metrics
   */
  async getBusinessMetrics(): Promise<BusinessMetrics> {
    const cacheKey = 'business_metrics'
    const cached = this.metricsCache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }

    const timer = monitor.timer('analytics.business_metrics')
    
    try {
      const metrics = await this.calculateBusinessMetrics()
      
      // Cache the results
      this.metricsCache.set(cacheKey, {
        data: metrics,
        timestamp: Date.now(),
      })

      timer.stop({ success: 'true' })
      monitor.counter('analytics.metrics_calculated', 1)
      
      return metrics
    } catch (error) {
      timer.stop({ success: 'false' })
      logger.error('Failed to calculate business metrics', error as Error)
      throw error
    }
  }

  /**
   * Calculate comprehensive business metrics
   */
  private async calculateBusinessMetrics(): Promise<BusinessMetrics> {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)

    // Revenue metrics
    const revenueData = await this.calculateRevenueMetrics(startOfMonth, startOfDay, lastMonth, lastMonthEnd)
    
    // Client metrics
    const clientData = await this.calculateClientMetrics(startOfMonth, lastMonth)
    
    // Appointment metrics
    const appointmentData = await this.calculateAppointmentMetrics(startOfMonth)
    
    // Treatment metrics
    const treatmentData = await this.calculateTreatmentMetrics()
    
    // Staff metrics
    const staffData = await this.calculateStaffMetrics(startOfMonth)

    return {
      revenue: revenueData,
      clients: clientData,
      appointments: appointmentData,
      treatments: treatmentData,
      staff: staffData,
    }
  }

  /**
   * Calculate revenue metrics
   */
  private async calculateRevenueMetrics(
    startOfMonth: Date,
    startOfDay: Date,
    lastMonth: Date,
    lastMonthEnd: Date
  ) {
    // Current month revenue
    const monthlyRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('SUM(amount) as total'))
        .where('payment_date', '>=', startOfMonth.toISOString())
        .where('status', 'completed')
    )

    // Daily revenue
    const dailyRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('SUM(amount) as total'))
        .where('payment_date', '>=', startOfDay.toISOString())
        .where('status', 'completed')
    )

    // Last month revenue for growth calculation
    const lastMonthRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('SUM(amount) as total'))
        .where('payment_date', '>=', lastMonth.toISOString())
        .where('payment_date', '<=', lastMonthEnd.toISOString())
        .where('status', 'completed')
    )

    // Total revenue
    const totalRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('SUM(amount) as total'))
        .where('status', 'completed')
    )

    const monthly = monthlyRevenue.data[0]?.total ?? 0
    const daily = dailyRevenue.data[0]?.total ?? 0
    const lastMonthTotal = lastMonthRevenue.data[0]?.total ?? 0
    const total = totalRevenue.data[0]?.total ?? 0

    // Calculate growth rate
    const growth = lastMonthTotal > 0 ? ((monthly - lastMonthTotal) / lastMonthTotal) * 100 : 0

    // Generate revenue forecast (simplified linear regression)
    const forecast = await this.generateRevenueForecast()

    return {
      total,
      monthly,
      daily,
      growth,
      forecast,
    }
  }

  /**
   * Calculate client metrics
   */
  private async calculateClientMetrics(startOfMonth: Date, lastMonth: Date) {
    // Total clients
    const totalClients = await db.query(
      'clients',
      (client) => client.select(db.raw('COUNT(*) as count'))
    )

    // New clients this month
    const newClients = await db.query(
      'clients',
      (client) => client
        .select(db.raw('COUNT(*) as count'))
        .where('created_at', '>=', startOfMonth.toISOString())
    )

    // Returning clients (clients with multiple appointments)
    const returningClients = await db.query(
      'appointments',
      (appointment) => appointment
        .select(db.raw('COUNT(DISTINCT client_id) as count'))
        .groupBy('client_id')
        .having(db.raw('COUNT(*)'), '>', 1)
    )

    // Calculate retention rate and lifetime value
    const retentionRate = await this.calculateRetentionRate()
    const lifetimeValue = await this.calculateCustomerLifetimeValue()

    return {
      total: totalClients.data[0]?.count ?? 0,
      new: newClients.data[0]?.count ?? 0,
      returning: returningClients.data.length ?? 0,
      retention: retentionRate,
      lifetime_value: lifetimeValue,
    }
  }

  /**
   * Calculate appointment metrics
   */
  private async calculateAppointmentMetrics(startOfMonth: Date) {
    const appointments = await db.query(
      'appointments',
      (appointment) => appointment
        .select('status')
        .where('appointment_date', '>=', startOfMonth.toISOString())
    )

    const total = appointments.data.length
    const completed = appointments.data.filter(a => a.status === 'completed').length
    const cancelled = appointments.data.filter(a => a.status === 'cancelled').length
    const noShow = appointments.data.filter(a => a.status === 'no_show').length

    // Calculate utilization rate (completed / total scheduled)
    const utilization = total > 0 ? (completed / total) * 100 : 0

    return {
      total,
      completed,
      cancelled,
      no_show: noShow,
      utilization,
    }
  }

  /**
   * Calculate treatment metrics
   */
  private async calculateTreatmentMetrics() {
    // Popular treatments
    const popularTreatments = await db.query(
      'appointment_treatments',
      (at) => at
        .join('treatments', 'treatments.id', 'appointment_treatments.treatment_id')
        .join('appointments', 'appointments.id', 'appointment_treatments.appointment_id')
        .join('payments', 'payments.appointment_id', 'appointments.id')
        .select('treatments.name_chinese as name')
        .select(db.raw('COUNT(*) as count'))
        .select(db.raw('SUM(payments.amount) as revenue'))
        .where('appointments.status', 'completed')
        .groupBy('treatments.id', 'treatments.name_chinese')
        .orderBy('count', 'desc')
        .limit(10)
    )

    // Treatment effectiveness (satisfaction and repeat rates)
    const treatmentEffectiveness = await this.calculateTreatmentEffectiveness()

    // Seasonal trends
    const seasonalTrends = await this.calculateSeasonalTrends()

    return {
      popular: popularTreatments.data ?? [],
      effectiveness: treatmentEffectiveness,
      seasonal_trends: seasonalTrends,
    }
  }

  /**
   * Calculate staff metrics
   */
  private async calculateStaffMetrics(startOfMonth: Date) {
    // Staff productivity (appointments and revenue per staff member)
    const staffProductivity = await db.query(
      'appointments',
      (appointment) => appointment
        .join('users', 'users.id', 'appointments.staff_id')
        .join('payments', 'payments.appointment_id', 'appointments.id')
        .select('users.first_name', 'users.last_name')
        .select(db.raw('COUNT(appointments.id) as appointments'))
        .select(db.raw('SUM(payments.amount) as revenue'))
        .where('appointments.appointment_date', '>=', startOfMonth.toISOString())
        .where('appointments.status', 'completed')
        .groupBy('users.id', 'users.first_name', 'users.last_name')
        .orderBy('revenue', 'desc')
    )

    // Overall staff utilization
    const utilization = await this.calculateStaffUtilization()

    // Staff satisfaction (placeholder - would come from surveys)
    const satisfaction = 4.2 // Mock data

    return {
      productivity: staffProductivity.data.map(staff => ({
        name: `${staff.first_name} ${staff.last_name}`,
        appointments: staff.appointments,
        revenue: staff.revenue,
      })) || [],
      utilization,
      satisfaction,
    }
  }

  /**
   * Generate predictive analytics
   */
  async getPredictiveAnalytics(): Promise<PredictiveAnalytics> {
    const timer = monitor.timer('analytics.predictive_analytics')
    
    try {
      const analytics = await this.calculatePredictiveAnalytics()
      
      timer.stop({ success: 'true' })
      monitor.counter('analytics.predictions_generated', 1)
      
      return analytics
    } catch (error) {
      timer.stop({ success: 'false' })
      logger.error('Failed to generate predictive analytics', error as Error)
      throw error
    }
  }

  /**
   * Calculate predictive analytics
   */
  private async calculatePredictiveAnalytics(): Promise<PredictiveAnalytics> {
    // Client behavior predictions
    const clientBehavior = await this.predictClientBehavior()
    
    // Revenue forecasting
    const revenueForecast = await this.predictRevenueForecast()
    
    // Appointment demand prediction
    const appointmentDemand = await this.predictAppointmentDemand()

    return {
      client_behavior: clientBehavior,
      revenue_forecast: revenueForecast,
      appointment_demand: appointmentDemand,
    }
  }

  /**
   * Helper methods for calculations
   */
  private async generateRevenueForecast(): Promise<number[]> {
    // Simplified forecast - in production, use more sophisticated models
    const recentRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('DATE_TRUNC(\'month\', payment_date) as month'))
        .select(db.raw('SUM(amount) as total'))
        .where('status', 'completed')
        .where('payment_date', '>=', db.raw('NOW() - INTERVAL \'12 months\''))
        .groupBy(db.raw('DATE_TRUNC(\'month\', payment_date)'))
        .orderBy('month')
    )

    // Simple linear trend projection
    const data = recentRevenue.data ?? []
    if (data.length < 2) return [0, 0, 0, 0, 0, 0]

    const trend = this.calculateLinearTrend(data.map(d => d.total))
    const lastValue = data[data.length - 1]?.total ?? 0
    
    return Array.from({ length: 6 }, (_, i) => 
      Math.max(0, lastValue + trend * (i + 1))
    )
  }

  private calculateLinearTrend(values: number[]): number {
    if (values.length < 2) return 0
    
    const n = values.length
    const sumX = (n * (n - 1)) / 2
    const sumY = values.reduce((sum, val) => sum + val, 0)
    const sumXY = values.reduce((sum, val, i) => sum + val * i, 0)
    const sumX2 = values.reduce((sum, _, i) => sum + i * i, 0)
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
  }

  private async calculateRetentionRate(): Promise<number> {
    // Simplified retention calculation
    const sixMonthsAgo = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000)
    
    const clientsWithMultipleVisits = await db.query(
      'appointments',
      (appointment) => appointment
        .select(db.raw('COUNT(DISTINCT client_id) as count'))
        .where('appointment_date', '>=', sixMonthsAgo.toISOString())
        .groupBy('client_id')
        .having(db.raw('COUNT(*)'), '>', 1)
    )

    const totalClients = await db.query(
      'appointments',
      (appointment) => appointment
        .select(db.raw('COUNT(DISTINCT client_id) as count'))
        .where('appointment_date', '>=', sixMonthsAgo.toISOString())
    )

    const returning = clientsWithMultipleVisits.data.length
    const total = totalClients.data[0]?.count ?? 0
    
    return total > 0 ? (returning / total) * 100 : 0
  }

  private async calculateCustomerLifetimeValue(): Promise<number> {
    // Simplified CLV calculation
    const avgRevenue = await db.query(
      'payments',
      (payment) => payment
        .select(db.raw('AVG(amount) as avg_amount'))
        .where('status', 'completed')
    )

    const avgFrequency = await db.query(
      'appointments',
      (appointment) => appointment
        .select(db.raw('AVG(visit_count) as avg_frequency'))
        .from(
          db.raw(`(
            SELECT client_id, COUNT(*) as visit_count 
            FROM appointments 
            WHERE status = 'completed' 
            GROUP BY client_id
          ) as client_visits`)
        )
    )

    const avgAmount = avgRevenue.data[0]?.avg_amount ?? 0
    const avgFreq = avgFrequency.data[0]?.avg_frequency ?? 1
    const avgLifespan = 24 // months - could be calculated from data

    return avgAmount * avgFreq * avgLifespan
  }

  private async calculateTreatmentEffectiveness() {
    // Mock data - in production, would analyze client feedback and repeat bookings
    return [
      { name: '面部护理', satisfaction: 4.5, repeat_rate: 75 },
      { name: '激光治疗', satisfaction: 4.3, repeat_rate: 68 },
      { name: '注射美容', satisfaction: 4.7, repeat_rate: 82 },
      { name: '皮肤检测', satisfaction: 4.1, repeat_rate: 45 },
    ]
  }

  private async calculateSeasonalTrends() {
    // Mock seasonal data - in production, would analyze historical patterns
    return [
      { month: '1月', treatments: 120 },
      { month: '2月', treatments: 95 },
      { month: '3月', treatments: 140 },
      { month: '4月', treatments: 165 },
      { month: '5月', treatments: 180 },
      { month: '6月', treatments: 195 },
      { month: '7月', treatments: 210 },
      { month: '8月', treatments: 205 },
      { month: '9月', treatments: 175 },
      { month: '10月', treatments: 160 },
      { month: '11月', treatments: 145 },
      { month: '12月', treatments: 130 },
    ]
  }

  private async calculateStaffUtilization(): Promise<number> {
    // Mock data - in production, would calculate based on working hours vs appointment hours
    return 78.5
  }

  private async predictClientBehavior() {
    // Mock predictive data - in production, would use ML models
    return {
      churn_risk: [
        { client_id: 'client_1', risk_score: 0.75, factors: ['长时间未预约', '取消率高'] },
        { client_id: 'client_2', risk_score: 0.65, factors: ['价格敏感', '服务满意度下降'] },
      ],
      next_treatment: [
        { client_id: 'client_3', treatment: '面部护理', probability: 0.85 },
        { client_id: 'client_4', treatment: '激光治疗', probability: 0.72 },
      ],
      lifetime_value: [
        { client_id: 'client_5', predicted_value: 15000 },
        { client_id: 'client_6', predicted_value: 8500 },
      ],
    }
  }

  private async predictRevenueForecast() {
    const forecast = await this.generateRevenueForecast()
    const avgForecast = forecast.reduce((sum, val) => sum + val, 0) / forecast.length
    
    return {
      next_month: forecast[0] || 0,
      next_quarter: forecast.slice(0, 3).reduce((sum, val) => sum + val, 0),
      confidence_interval: [avgForecast * 0.85, avgForecast * 1.15] as [number, number],
      factors: ['季节性趋势', '客户增长', '服务价格调整', '市场竞争'],
    }
  }

  private async predictAppointmentDemand() {
    return {
      peak_hours: [
        { hour: 10, demand: 85 },
        { hour: 14, demand: 92 },
        { hour: 16, demand: 88 },
        { hour: 19, demand: 75 },
      ],
      seasonal_patterns: [
        { month: '春季', demand: 120 },
        { month: '夏季', demand: 150 },
        { month: '秋季', demand: 110 },
        { month: '冬季', demand: 95 },
      ],
      treatment_trends: [
        { treatment: '面部护理', trend: 'increasing' as const },
        { treatment: '激光治疗', trend: 'stable' as const },
        { treatment: '注射美容', trend: 'increasing' as const },
      ],
    }
  }
}

/**
 * Global business intelligence instance
 */
export const businessIntelligence = new BusinessIntelligenceEngine()
