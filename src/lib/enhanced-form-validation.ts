/**
 * Enhanced Form Validation System
 * Provides comprehensive form validation with real-time feedback
 */

import { z } from 'zod';
import { useState, useCallback, useEffect } from 'react';
import { logger } from '@/lib/logger';

// Enhanced validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
  touched: Record<string, boolean>;
  isDirty: boolean;
}

// Field validation state
export interface FieldState {
  value: unknown;
  error: string | null;
  warning: string | null;
  touched: boolean;
  validating: boolean;
}

// Form validation options
export interface ValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
  debounceMs?: number;
  showWarnings?: boolean;
}

// Async validation function type
export type AsyncValidator<T = unknown> = (value: T) => Promise<string | null>;

// Enhanced validation schemas using Zod
export const enhancedSchemas = {
  // Client validation schemas
  client: z.object({
    first_name: z.string()
      .min(1, '姓名不能为空')
      .min(2, '姓名至少需要2个字符')
      .max(50, '姓名不能超过50个字符')
      .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓名只能包含中文、英文和空格'),
    
    last_name: z.string()
      .min(1, '姓氏不能为空')
      .min(1, '姓氏至少需要1个字符')
      .max(50, '姓氏不能超过50个字符')
      .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓氏只能包含中文、英文和空格'),
    
    email: z.string()
      .email('请输入有效的邮箱地址')
      .max(255, '邮箱地址不能超过255个字符')
      .optional()
      .or(z.literal('')),
    
    phone: z.string()
      .regex(/^\d{10,11}$/, '请输入10-11位数字的电话号码')
      .optional()
      .or(z.literal('')),
    
    date_of_birth: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, '请输入有效的日期格式')
      .refine((date) => {
        const birthDate = new Date(date);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 0 && age <= 150;
      }, '请输入有效的出生日期')
      .optional()
      .or(z.literal('')),
    
    gender: z.enum(['male', 'female', 'other'], {
      errorMap: () => ({ message: '请选择有效的性别' })
    }).optional(),
    
    notes: z.string()
      .max(1000, '备注不能超过1000个字符')
      .optional()
      .or(z.literal(''))
  }),

  // Appointment validation schema
  appointment: z.object({
    client_id: z.string()
      .uuid('请选择有效的客户')
      .min(1, '请选择客户'),
    
    treatment_id: z.string()
      .uuid('请选择有效的治疗项目')
      .min(1, '请选择治疗项目'),
    
    appointment_date: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, '请输入有效的日期格式')
      .refine((date) => {
        const appointmentDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return appointmentDate >= today;
      }, '预约日期不能早于今天'),
    
    start_time: z.string()
      .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式（HH:MM）'),
    
    end_time: z.string()
      .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式（HH:MM）'),
    
    appointment_type: z.enum(['consultation', 'treatment', 'follow_up'], {
      errorMap: () => ({ message: '请选择有效的预约类型' })
    }),
    
    notes: z.string()
      .max(500, '备注不能超过500个字符')
      .optional()
      .or(z.literal(''))
  }).refine((data) => {
    const startTime = new Date(`2000-01-01T${data.start_time}`);
    const endTime = new Date(`2000-01-01T${data.end_time}`);
    return endTime > startTime;
  }, {
    message: '结束时间必须晚于开始时间',
    path: ['end_time']
  }),

  // Treatment validation schema
  treatment: z.object({
    name_chinese: z.string()
      .min(1, '治疗项目名称不能为空')
      .min(2, '治疗项目名称至少需要2个字符')
      .max(100, '治疗项目名称不能超过100个字符'),
    
    name_english: z.string()
      .min(1, '英文名称不能为空')
      .max(100, '英文名称不能超过100个字符')
      .regex(/^[a-zA-Z\s\-_]+$/, '英文名称只能包含英文字母、空格、连字符和下划线')
      .optional()
      .or(z.literal('')),
    
    category: z.string()
      .min(1, '请选择治疗分类'),
    
    default_price: z.number()
      .min(0, '价格不能为负数')
      .max(999999.99, '价格不能超过999,999.99')
      .multipleOf(0.01, '价格最多保留两位小数'),
    
    duration_minutes: z.number()
      .int('持续时间必须是整数')
      .min(1, '持续时间至少为1分钟')
      .max(1440, '持续时间不能超过24小时'),
    
    description: z.string()
      .max(1000, '描述不能超过1000个字符')
      .optional()
      .or(z.literal(''))
  }),

  // Payment validation schema
  payment: z.object({
    invoice_id: z.string()
      .uuid('请选择有效的账单')
      .min(1, '请选择账单'),
    
    amount: z.number()
      .min(0.01, '付款金额必须大于0')
      .max(999999.99, '付款金额不能超过999,999.99')
      .multipleOf(0.01, '付款金额最多保留两位小数'),
    
    payment_method: z.enum(['cash', 'card', 'transfer', 'other'], {
      errorMap: () => ({ message: '请选择有效的付款方式' })
    }),
    
    payment_date: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, '请输入有效的日期格式')
      .refine((date) => {
        const paymentDate = new Date(date);
        const today = new Date();
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        return paymentDate >= oneYearAgo && paymentDate <= today;
      }, '付款日期必须在过去一年内'),
    
    notes: z.string()
      .max(500, '备注不能超过500个字符')
      .optional()
      .or(z.literal(''))
  })
};

// Enhanced form validation hook
export function useEnhancedFormValidation<T extends Record<string, unknown>>(
  schema: z.ZodSchema<T>,
  initialValues: Partial<T> = {},
  options: ValidationOptions = {}
) {
  const {
    validateOnChange = true,
    validateOnBlur = true,
    validateOnSubmit = true,
    debounceMs = 300,
    showWarnings = true
  } = options;

  const [values, setValues] = useState<Partial<T>>(initialValues);
  const [] = useState<Record<string, string[]>>({});
  const [warnings, setWarnings] = useState<Record<string, string[]>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Debounced validation
  const validateField = useCallback(
    async (fieldName: string, value: unknown, showWarning = showWarnings) => {
      try {
        setIsValidating(true);
        
        // Validate single field
        const fieldSchema = schema.shape[fieldName as keyof typeof schema.shape];
        if (fieldSchema) {
          await fieldSchema.parseAsync(value);
          
          // Clear errors for this field
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
          });
          
          // Clear warnings for this field
          if (showWarning) {
            setWarnings(prev => {
              const newWarnings = { ...prev };
              delete newWarnings[fieldName];
              return newWarnings;
            });
          }
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldErrors = error.errors
            .filter(err => err.path.includes(fieldName))
            .map(err => err.message);
          
          if (fieldErrors.length > 0) {
            setErrors(prev => ({
              ...prev,
              [fieldName]: fieldErrors
            }));
          }
        }
      } finally {
        setIsValidating(false);
      }
    },
    [schema, showWarnings]
  );

  // Validate entire form
  const validateForm = useCallback(async (): Promise<ValidationResult> => {
    try {
      setIsValidating(true);
      await schema.parseAsync(values);
      
      setErrors({});
      setWarnings({});
      
      return {
        isValid: true,
        errors: {},
        warnings: {},
        touched,
        isDirty: Object.keys(touched).length > 0
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Record<string, string[]> = {};
        
        error.errors.forEach(err => {
          const fieldName = err.path[0] as string;
          if (!formErrors[fieldName]) {
            formErrors[fieldName] = [];
          }
          formErrors[fieldName].push(err.message);
        });
        
        setErrors(formErrors);
        
        return {
          isValid: false,
          errors: formErrors,
          warnings,
          touched,
          isDirty: Object.keys(touched).length > 0
        };
      }
      
      logger.error(error, error as Error);
      return {
        isValid: false,
        errors: { _form: ['验证过程中发生错误'] },
        warnings,
        touched,
        isDirty: Object.keys(touched).length > 0
      };
    } finally {
      setIsValidating(false);
    }
  }, [schema, values, warnings, touched]);

  // Set field value
  const setValue = useCallback((fieldName: string, value: unknown) => {
    setValues(prev => ({ ...prev, [fieldName]: value }));
    
    if (validateOnChange) {
      const timeoutId = setTimeout(() => {
        validateField(fieldName, value);
      }, debounceMs);
      
      return () => clearTimeout(timeoutId);
    }
  }, [validateOnChange, debounceMs, validateField]);

  // Set field as touched
  const setFieldTouched = useCallback((fieldName: string, isTouched = true) => {
    setTouched(prev => ({ ...prev, [fieldName]: isTouched }));
    
    if (validateOnBlur && isTouched) {
      validateField(fieldName, values[fieldName as keyof T]);
    }
  }, [validateOnBlur, validateField, values]);

  // Handle form submission
  const handleSubmit = useCallback(async (
    onSubmit: (values: T) => Promise<void> | void
  ) => {
    if (!validateOnSubmit) {
      await onSubmit(values as T);
      return;
    }

    setIsSubmitting(true);
    
    try {
      const validationResult = await validateForm();
      
      if (validationResult.isValid) {
        await onSubmit(values as T);
      } else {
        logger.warn('Form submission failed validation:', validationResult.errors);
      }
    } catch (error) {
      logger.error(error, error as Error);
      setErrors(prev => ({
        ...prev,
        _form: ['提交过程中发生错误，请重试']
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [validateOnSubmit, validateForm, values]);

  // Reset form
  const reset = useCallback((newValues: Partial<T> = {}) => {
    setValues(newValues);
    setErrors({});
    setWarnings({});
    setTouched({});
    setIsValidating(false);
    setIsSubmitting(false);
  }, []);

  return {
    values,
    errors,
    warnings,
    touched,
    isValidating,
    isSubmitting,
    setValue,
    setFieldTouched,
    validateField,
    validateForm,
    handleSubmit,
    reset,
    isValid: Object.keys(errors).length === 0,
    isDirty: Object.keys(touched).length > 0
  };
}
