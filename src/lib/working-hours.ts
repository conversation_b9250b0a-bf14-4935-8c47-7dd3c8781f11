import { logger } from '@/lib/logger';
// 工作时间配置
export interface WorkingHours {
  day: number // 0 = 周日, 1 = 周一, ..., 6 = 周六
  isWorking: boolean
  startTime: string // HH:mm 格式
  endTime: string // HH:mm 格式
  breakStart?: string // 午休开始时间
  breakEnd?: string // 午休结束时间
}

// 默认工作时间配置
export const DEFAULT_WORKING_HOURS: WorkingHours[] = [
  { day: 0, isWorking: false, startTime: '09:00', endTime: '18:00' }, // 周日
  { day: 1, isWorking: true, startTime: '09:00', endTime: '18:00', breakStart: '12:00', breakEnd: '13:00' }, // 周一
  { day: 2, isWorking: true, startTime: '09:00', endTime: '18:00', breakStart: '12:00', breakEnd: '13:00' }, // 周二
  { day: 3, isWorking: true, startTime: '09:00', endTime: '18:00', breakStart: '12:00', breakEnd: '13:00' }, // 周三
  { day: 4, isWorking: true, startTime: '09:00', endTime: '18:00', breakStart: '12:00', breakEnd: '13:00' }, // 周四
  { day: 5, isWorking: true, startTime: '09:00', endTime: '18:00', breakStart: '12:00', breakEnd: '13:00' }, // 周五
  { day: 6, isWorking: true, startTime: '09:00', endTime: '17:00' } // 周六
]

// 星期名称映射
export const DAY_NAMES = [
  '周日', '周一', '周二', '周三', '周四', '周五', '周六'
]

// 获取工作时间配置
export function getWorkingHours(): WorkingHours[] {
  try {
    const saved = typeof window !== "undefined" && localStorage.getItem('working_hours')
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    logger.error('Error loading working hours:', error as Error)
  }
  return DEFAULT_WORKING_HOURS
}

// 保存工作时间配置
export function saveWorkingHours(workingHours: WorkingHours[]): void {
  try {
    typeof window !== "undefined" && localStorage.setItem('working_hours', JSON.stringify(workingHours))
  } catch (error) {
    logger.error('Error saving working hours:', error as Error)
  }
}

// 检查指定日期是否为工作日
export function isWorkingDay(date: Date): boolean {
  const workingHours = getWorkingHours()
  const dayOfWeek = date.getDay()
  const dayConfig = workingHours.find(wh => wh.day === dayOfWeek)
  return dayConfig?.isWorking ?? false
}

// 获取指定日期的工作时间
export function getWorkingHoursForDate(date: Date): WorkingHours | null {
  const workingHours = getWorkingHours()
  const dayOfWeek = date.getDay()
  const dayConfig = workingHours.find(wh => wh.day === dayOfWeek)
  return dayConfig?.isWorking ? dayConfig : null
}

// 检查指定时间是否在工作时间内
export function isWorkingTime(date: Date, time: string): boolean {
  const dayConfig = getWorkingHoursForDate(date)
  if (!dayConfig) return false

  const timeMinutes = timeToMinutes(time)
  const startMinutes = timeToMinutes(dayConfig.startTime)
  const endMinutes = timeToMinutes(dayConfig.endTime)

  // 检查是否在工作时间内
  if (timeMinutes < startMinutes ?? timeMinutes >= endMinutes) {
    return false
  }

  // 检查是否在午休时间内
  if (dayConfig.breakStart && dayConfig.breakEnd) {
    const breakStartMinutes = timeToMinutes(dayConfig.breakStart)
    const breakEndMinutes = timeToMinutes(dayConfig.breakEnd)
    if (timeMinutes >= breakStartMinutes && timeMinutes < breakEndMinutes) {
      return false
    }
  }

  return true
}

// 获取指定日期的可用时间段
export function getAvailableTimeSlots(date: Date, slotDuration: number = 30): string[] {
  const dayConfig = getWorkingHoursForDate(date)
  if (!dayConfig) return []

  const slots: string[] = []
  const startMinutes = timeToMinutes(dayConfig.startTime)
  const endMinutes = timeToMinutes(dayConfig.endTime)
  
  let currentMinutes = startMinutes
  
  while (currentMinutes + slotDuration <= endMinutes) {
    const timeString = minutesToTime(currentMinutes)
    
    // 检查是否在午休时间内
    if (dayConfig.breakStart && dayConfig.breakEnd) {
      const breakStartMinutes = timeToMinutes(dayConfig.breakStart)
      const breakEndMinutes = timeToMinutes(dayConfig.breakEnd)
      
      if (currentMinutes >= breakStartMinutes && currentMinutes < breakEndMinutes) {
        currentMinutes = breakEndMinutes
        continue
      }
      
      // 如果时间段跨越午休时间，跳过
      if (currentMinutes < breakStartMinutes && currentMinutes + slotDuration > breakStartMinutes) {
        currentMinutes = breakEndMinutes
        continue
      }
    }
    
    slots.push(timeString)
    currentMinutes += slotDuration
  }
  
  return slots
}

// 时间字符串转换为分钟数
function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

// 分钟数转换为时间字符串
function minutesToTime(minutes: number): string {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

// 验证时间格式
export function validateTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

// 比较两个时间字符串
export function compareTime(time1: string, time2: string): number {
  const minutes1 = timeToMinutes(time1)
  const minutes2 = timeToMinutes(time2)
  return minutes1 - minutes2
}

// 验证预约时间是否有效
export function validateAppointmentTime(
  date: Date,
  startTime: string,
  endTime: string
): { isValid: boolean; error?: string } {
  // 检查是否为工作日
  if (!isWorkingDay(date)) {
    return { isValid: false, error: '选择的日期不是工作日' }
  }

  // 检查开始时间是否在工作时间内
  if (!isWorkingTime(date, startTime)) {
    return { isValid: false, error: '开始时间不在工作时间内' }
  }

  // 检查结束时间是否在工作时间内
  if (!isWorkingTime(date, endTime)) {
    return { isValid: false, error: '结束时间不在工作时间内' }
  }

  const startMinutes = timeToMinutes(startTime)
  const endMinutes = timeToMinutes(endTime)

  // 检查时间顺序
  if (startMinutes >= endMinutes) {
    return { isValid: false, error: '结束时间必须晚于开始时间' }
  }

  const workingHours = getWorkingHoursForDate(date)!

  // 检查是否跨越午休时间
  if (workingHours.breakStart && workingHours.breakEnd) {
    const breakStartMinutes = timeToMinutes(workingHours.breakStart)
    const breakEndMinutes = timeToMinutes(workingHours.breakEnd)

    if (startMinutes < breakStartMinutes && endMinutes > breakStartMinutes) {
      return { isValid: false, error: '预约时间不能跨越午休时间' }
    }
  }

  return { isValid: true }
}

// 获取下一个工作日
export function getNextWorkingDay(fromDate: Date = new Date()): Date {
  const date = new Date(fromDate)
  date.setDate(date.getDate() + 1)

  // 最多查找14天
  for (let i = 0; i < 14; i++) {
    if (isWorkingDay(date)) {
      return date
    }
    date.setDate(date.getDate() + 1)
  }

  // 如果14天内都没有工作日，返回明天
  return new Date(fromDate.getTime() + 24 * 60 * 60 * 1000)
}

// 格式化工作时间显示
export function formatWorkingHours(workingHours: WorkingHours): string {
  if (!workingHours.isWorking) return '休息日'

  let timeStr = `${workingHours.startTime} - ${workingHours.endTime}`

  if (workingHours.breakStart && workingHours.breakEnd) {
    timeStr += ` (午休: ${workingHours.breakStart} - ${workingHours.breakEnd})`
  }

  return timeStr
}
