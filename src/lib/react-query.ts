/**
 * Professional React Query Configuration
 * Advanced client-side caching, state management, and performance optimization
 */

import { QueryClient, QueryClientProvider, useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import React, { useState, useEffect } from 'react'
import { logger } from './logger'
import { monitor } from './monitoring'
import { env, isDevelopment } from './env'
import { logger } from '@/lib/logger';

/**
 * Query configuration options
 */
interface QueryConfig {
  staleTime?: number
  cacheTime?: number
  refetchOnWindowFocus?: boolean
  refetchOnReconnect?: boolean
  retry?: number | boolean
  retryDelay?: number
  enabled?: boolean
}

/**
 * Mutation configuration options
 */
interface MutationConfig {
  onSuccess?: (data: unknown) => void
  onError?: (error: Error) => void
  onSettled?: () => void
  retry?: number | boolean
  retryDelay?: number
}

/**
 * Professional Query Client Configuration
 */
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes by default
        staleTime: 5 * 60 * 1000,
        // Keep data in cache for 10 minutes
        cacheTime: 10 * 60 * 1000,
        // Don't refetch on window focus in production
        refetchOnWindowFocus: isDevelopment,
        // Always refetch on reconnect
        refetchOnReconnect: true,
        // Retry failed requests 3 times
        retry: 3,
        // Exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Error handling
        onError: (error: Error) => {
          logger.error('Query error', error, { context: 'react-query' })
          monitor.counter('client.query.error', 1, { error: error.message })
        },
        // Success monitoring
        onSuccess: () => {
          monitor.counter('client.query.success', 1)
        },
      },
      mutations: {
        // Retry mutations once
        retry: 1,
        // Error handling
        onError: (error: Error) => {
          logger.error('Mutation error', error, { context: 'react-query' })
          monitor.counter('client.mutation.error', 1, { error: error.message })
        },
        // Success monitoring
        onSuccess: () => {
          monitor.counter('client.mutation.success', 1)
        },
      },
    },
  })
}

/**
 * Global query client instance
 */
export const queryClient = createQueryClient()

/**
 * Query key factories for consistent caching
 */
export const queryKeys = {
  // Client queries
  clients: {
    all: ['clients'] as const,
    lists: () => [...queryKeys.clients.all, 'list'] as const,
    list: (filters: Record<string, any>) => [] as const,
    details: () => [...queryKeys.clients.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.clients.details(), id] as const,
    search: (query: string) => [...queryKeys.clients.all, 'search', query] as const,
  },
  
  // Treatment queries
  treatments: {
    all: ['treatments'] as const,
    lists: () => [...queryKeys.treatments.all, 'list'] as const,
    list: (filters: Record<string, any>) => [] as const,
    details: () => [...queryKeys.treatments.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.treatments.details(), id] as const,
    categories: () => [...queryKeys.treatments.all, 'categories'] as const,
  },
  
  // Appointment queries
  appointments: {
    all: ['appointments'] as const,
    lists: () => [...queryKeys.appointments.all, 'list'] as const,
    list: (filters: Record<string, any>) => [] as const,
    details: () => [...queryKeys.appointments.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.appointments.details(), id] as const,
    calendar: (date: string) => [...queryKeys.appointments.all, 'calendar', date] as const,
    conflicts: (date: string, time: string) => [...queryKeys.appointments.all, 'conflicts', date, time] as const,
  },
  
  // Invoice queries
  invoices: {
    all: ['invoices'] as const,
    lists: () => [...queryKeys.invoices.all, 'list'] as const,
    list: (filters: Record<string, any>) => [] as const,
    details: () => [...queryKeys.invoices.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.invoices.details(), id] as const,
    client: (clientId: string) => [...queryKeys.invoices.all, 'client', clientId] as const,
  },
  
  // Payment queries
  payments: {
    all: ['payments'] as const,
    lists: () => [...queryKeys.payments.all, 'list'] as const,
    list: (filters: Record<string, any>) => [] as const,
    details: () => [...queryKeys.payments.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.payments.details(), id] as const,
    invoice: (invoiceId: string) => [...queryKeys.payments.all, 'invoice', invoiceId] as const,
  },
}

/**
 * API client with React Query integration
 */
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    const startTime = Date.now()
    
    try {
      const response = await fetch(url, config)
      const duration = Date.now() - startTime
      
      monitor.histogram('client.api.request_duration', duration, {
        method: config.method ?? 'GET',
        endpoint,
        status: response.status.toString(),
      })

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Request failed' }))
        throw new Error(error.message ?? `HTTP ${response.status}`)
      }

      const data = await response.json()
      monitor.counter('client.api.request_success', 1, {
        method: config.method ?? 'GET',
        endpoint,
      })
      
      return data
    } catch (error) {
      const duration = Date.now() - startTime
      monitor.counter('client.api.request_error', 1, {
        method: config.method ?? 'GET',
        endpoint,
        error: (error as Error).message,
      })
      
      logger.error('API request failed', error as Error, {
        url,
        method: config.method,
        duration,
      })
      
      throw error
    }
  }

  // Client API methods
  clients = {
    getAll: (params?: Record<string, any>) => {
      const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
      return this.request<any>(`/clients${searchParams}`)
    },
    
    getById: (id: string) => this.request<any>(`/clients/${id}`),
    
    create: (data: unknown) => this.request<any>('/clients', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    
    update: (id: string, data: unknown) => this.request<any>(`/clients/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
    
    delete: (id: string) => this.request<any>(`/clients/${id}`, {
      method: 'DELETE',
    }),
    
    search: (query: string) => this.request<any>(`/clients?q=${encodeURIComponent(query)}`),
  }

  // Treatment API methods
  treatments = {
    getAll: (params?: Record<string, any>) => {
      const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
      return this.request<any>(`/treatments${searchParams}`)
    },
    
    getById: (id: string) => this.request<any>(`/treatments/${id}`),
    
    create: (data: unknown) => this.request<any>('/treatments', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    
    update: (id: string, data: unknown) => this.request<any>(`/treatments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
    
    delete: (id: string) => this.request<any>(`/treatments/${id}`, {
      method: 'DELETE',
    }),
  }

  // Appointment API methods
  appointments = {
    getAll: (params?: Record<string, any>) => {
      const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
      return this.request<any>(`/appointments${searchParams}`)
    },
    
    getById: (id: string) => this.request<any>(`/appointments/${id}`),
    
    create: (data: unknown) => this.request<any>('/appointments', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
    
    update: (id: string, data: unknown) => this.request<any>(`/appointments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
    
    delete: (id: string) => this.request<any>(`/appointments/${id}`, {
      method: 'DELETE',
    }),
    
    checkConflicts: (date: string, startTime: string, endTime: string) => 
      this.request<any>(`/appointments/conflicts?date=${date}&start_time=${startTime}&end_time=${endTime}`),
  }
}

/**
 * Global API client instance
 */
export const apiClient = new ApiClient()

/**
 * Custom hooks for data fetching
 */
export const useClients = (filters?: Record<string, any>, config?: QueryConfig) => {
  return useQuery({
    queryKey: queryKeys.clients.list(filters ?? {}),
    queryFn: () => apiClient.clients.getAll(filters),
    ...config,
  })
}

export const useClient = (id: string, config?: QueryConfig) => {
  return useQuery({
    queryKey: queryKeys.clients.detail(id),
    queryFn: () => apiClient.clients.getById(id),
    enabled: !!id,
    ...config,
  })
}

export const useClientSearch = (query: string, config?: QueryConfig) => {
  return useQuery({
    queryKey: queryKeys.clients.search(query),
    queryFn: () => apiClient.clients.search(query),
    enabled: query.length > 2, // Only search with 3+ characters
    staleTime: 30 * 1000, // Search results stale after 30 seconds
    ...config,
  })
}

/**
 * Custom hooks for mutations
 */
export const useCreateClient = (config?: MutationConfig) => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: apiClient.clients.create,
    onSuccess: (data) => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: queryKeys.clients.lists() })
      // Add the new client to the cache
      queryClient.setQueryData(queryKeys.clients.detail(data.id), data)
      config?.onSuccess?.(data)
    },
    onError: config?.onError,
    onSettled: config?.onSettled,
  })
}

export const useUpdateClient = (config?: MutationConfig) => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Record<string, unknown> }) => 
      apiClient.clients.update(id, data),
    onSuccess: (data, variables) => {
      // Update the client in cache
      queryClient.setQueryData(queryKeys.clients.detail(variables.id), data)
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.clients.lists() })
      config?.onSuccess?.(data)
    },
    onError: config?.onError,
    onSettled: config?.onSettled,
  })
}

export const useDeleteClient = (config?: MutationConfig) => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: apiClient.clients.delete,
    onSuccess: (data, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.clients.detail(id) })
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.clients.lists() })
      config?.onSuccess?.(data)
    },
    onError: config?.onError,
    onSettled: config?.onSettled,
  })
}

/**
 * Cache management utilities
 */
export const cacheManager = {
  /**
   * Prefetch data for better UX
   */
  prefetch: {
    client: (id: string) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.clients.detail(id),
        queryFn: () => apiClient.clients.getById(id),
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    },
    
    clients: (filters?: Record<string, any>) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.clients.list(filters ?? {}),
        queryFn: () => apiClient.clients.getAll(filters),
        staleTime: 5 * 60 * 1000,
      })
    },
  },

  /**
   * Invalidate cache for data consistency
   */
  invalidate: {
    clients: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clients.all })
    },
    
    client: (id: string) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clients.detail(id) })
    },
    
    all: () => {
      queryClient.invalidateQueries()
    },
  },

  /**
   * Get cache statistics
   */
  getStats: () => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()
    
    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      fetchingQueries: queries.filter(q => q.isFetching()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
    }
  },
}

/**
 * Performance monitoring hook
 */
export const useQueryPerformance = () => {
  const [stats, setStats] = useState(cacheManager.getStats())

  useEffect(() => {
    const interval = setInterval(() => {
      const newStats = cacheManager.getStats()
      setStats(newStats)
      
      // Report metrics
      monitor.gauge('client.cache.total_queries', newStats.totalQueries)
      monitor.gauge('client.cache.stale_queries', newStats.staleQueries)
      monitor.gauge('client.cache.fetching_queries', newStats.fetchingQueries)
      monitor.gauge('client.cache.error_queries', newStats.errorQueries)
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return stats
}

/**
 * React Query Provider component
 */
export const QueryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {isDevelopment && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};
