import { logger } from '@/lib/logger';
/**
 * 数据导出工具库
 * 支持CSV、Excel等格式的数据导出
 */

export interface ExportColumn {
  key: string
  label: string
  formatter?: (value: unknown, row: unknown) => string
  width?: number
}

export interface ExportOptions {
  filename?: string
  columns: ExportColumn[]
  data: unknown[]
  format?: 'csv' | 'excel'
  includeHeaders?: boolean
}

/**
 * 格式化数据值
 */
function formatValue(value: unknown, formatter?: (value: unknown, row: unknown) => string, row?: unknown): string {
  if (formatter && row) {
    return formatter(value, row)
  }
  
  if (value === null ?? value === undefined) {
    return ''
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  if (value instanceof Date) {
    return value.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })
  }
  
  return String(value)
}

/**
 * 导出为CSV格式
 */
export function exportToCSV(options: ExportOptions): void {
  const { filename = 'export.csv', columns, data, includeHeaders = true } = options
  
  let csvContent = ''
  
  // 添加表头
  if (includeHeaders) {
    const headers = columns.map(col => `"${col.label}"`).join(',')
    csvContent += `${headers  }\n`
  }
  
  // 添加数据行
  data.forEach(row => {
    const values = columns.map(col => {
      const value = row[col.key]
      const formattedValue = formatValue(value, col.formatter, row)
      return `"${formattedValue.replace(/"/g, '""')}"`
    }).join(',')
    csvContent += `${values  }\n`
  })
  
  // 创建并下载文件
  const blob = new Blob([`\ufeff${  csvContent}`], { type: 'text/csv;charset=utf-8;' })
  const link = typeof document !== "undefined" && document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  typeof document !== "undefined" && document.body.appendChild(link)
  link.click()
  typeof document !== "undefined" && document.body.removeChild(link)
}

/**
 * 导出为Excel格式（需要安装xlsx库）
 */
export async function exportToExcel(options: ExportOptions): Promise<void> {
  // 直接降级到CSV导出，因为xlsx库未安装
  logger.warn('Excel export not available, falling back to CSV export')
  const csvFilename = options.filename?.replace('.xlsx', '.csv') || 'export.csv'
  exportToCSV({ ...options, filename: csvFilename })
}

/**
 * 通用导出函数
 */
export function exportData(options: ExportOptions): void {
  const { format = 'csv' } = options
  
  if (format === 'excel') {
    exportToExcel(options)
  } else {
    exportToCSV(options)
  }
}

/**
 * 常用的数据格式化器
 */
export const formatters = {
  // 货币格式
  currency: (value: number) => {
    if (typeof value !== 'number') return ''
    return `$${value.toFixed(2)}`
  },
  
  // 日期格式
  date: (value: string | Date) => {
    if (!value) return ''
    const date = typeof value === 'string' ? new Date(value) : value
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })
  },
  
  // 日期时间格式
  datetime: (value: string | Date) => {
    if (!value) return ''
    const date = typeof value === 'string' ? new Date(value) : value
    return date.toLocaleString('zh-CN')
  },
  
  // 状态格式
  status: (value: string, statusMap: Record<string, string>) => {
    return statusMap[value] || value
  },
  
  // 电话号码格式
  phone: (value: string) => {
    if (!value) return ''
    // 格式化为 xxx-xxxx-xxxx
    return value.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
  },
  
  // 百分比格式
  percentage: (value: number) => {
    if (typeof value !== 'number') return ''
    return `${value}%`
  },
  
  // 是否格式
  boolean: (value: boolean) => {
    return value ? '是' : '否'
  }
}

/**
 * 预定义的导出配置
 */
export const exportConfigs = {
  // 客户数据导出
  clients: {
    columns: [
      { key: 'last_name', label: '姓' },
      { key: 'first_name', label: '名' },
      { key: 'phone', label: '电话', formatter: formatters.phone },
      { key: 'email', label: '邮箱' },
      { key: 'date_of_birth', label: '出生日期', formatter: formatters.date },
      { key: 'address_line_1', label: '地址' },
      { key: 'city', label: '城市' },
      { key: 'state_province', label: '省份' },
      { key: 'postal_code', label: '邮编' },
      { key: 'status', label: '状态', formatter: (value: string) => formatters.status(value, {
        active: '活跃',
        inactive: '非活跃',
        archived: '已归档'
      })},
      { key: 'created_at', label: '创建时间', formatter: formatters.datetime }
    ] as ExportColumn[]
  },
  
  // 预约数据导出
  appointments: {
    columns: [
      { key: 'client_name', label: '客户姓名' },
      { key: 'client_phone', label: '客户电话', formatter: formatters.phone },
      { key: 'treatment_name', label: '治疗项目' },
      { key: 'appointment_date', label: '预约日期', formatter: formatters.date },
      { key: 'start_time', label: '开始时间' },
      { key: 'end_time', label: '结束时间' },
      { key: 'status', label: '状态', formatter: (value: string) => formatters.status(value, {
        scheduled: '已预约',
        confirmed: '已确认',
        completed: '已完成',
        cancelled: '已取消',
        no_show: '未到场'
      })},
      { key: 'notes', label: '备注' },
      { key: 'created_at', label: '创建时间', formatter: formatters.datetime }
    ] as ExportColumn[]
  },
  
  // 账单数据导出
  invoices: {
    columns: [
      { key: 'invoice_number', label: '账单号' },
      { key: 'client_name', label: '客户姓名' },
      { key: 'treatment_date', label: '治疗日期', formatter: formatters.date },
      { key: 'total_amount', label: '总金额', formatter: formatters.currency },
      { key: 'deposit_amount', label: '定金金额', formatter: formatters.currency },
      { key: 'paid_amount', label: '已付金额', formatter: formatters.currency },
      { key: 'status', label: '状态', formatter: (value: string) => formatters.status(value, {
        draft: '草稿',
        pending: '待付款',
        partial: '部分付款',
        paid: '已付款',
        overdue: '逾期',
        cancelled: '已取消'
      })},
      { key: 'due_date', label: '到期日期', formatter: formatters.date },
      { key: 'created_at', label: '创建时间', formatter: formatters.datetime }
    ] as ExportColumn[]
  },
  
  // 付款记录导出
  payments: {
    columns: [
      { key: 'invoice_number', label: '账单号' },
      { key: 'client_name', label: '客户姓名' },
      { key: 'amount', label: '付款金额', formatter: formatters.currency },
      { key: 'payment_date', label: '付款日期', formatter: formatters.date },
      { key: 'payment_method', label: '付款方式', formatter: (value: string) => formatters.status(value, {
        cash: '现金',
        credit_card: '信用卡',
        debit_card: '借记卡',
        bank_transfer: '银行转账',
        check: '支票',
        other: '其他'
      })},
      { key: 'payment_type', label: '付款类型', formatter: (value: string) => formatters.status(value, {
        deposit: '定金',
        partial: '部分付款',
        full: '全额付款',
        refund: '退款'
      })},
      { key: 'reference_number', label: '参考号' },
      { key: 'created_at', label: '创建时间', formatter: formatters.datetime }
    ] as ExportColumn[]
  }
}

/**
 * 快速导出函数
 */
export function quickExport(type: keyof typeof exportConfigs, data: unknown[], filename?: string, format: 'csv' | 'excel' = 'csv') {
  const config = exportConfigs[type]
  if (!config) {
    throw new Error(`Unknown export type: ${type}`)
  }

  const defaultFilename = `${type}_${new Date().toISOString().split('T')[0]}.${format}`

  exportData({
    filename: filename ?? defaultFilename,
    columns: config.columns,
    data,
    format
  })
}

/**
 * 数据导入相关功能
 */

export interface ImportColumn {
  key: string
  label: string
  required?: boolean
  validator?: (value: unknown) => string | null
  transformer?: (value: unknown) => any
}

export interface ImportResult {
  success: boolean
  data: unknown[]
  errors: Array<{
    row: number
    column: string
    message: string
  }>
  summary: {
    total: number
    valid: number
    invalid: number
  }
}

/**
 * 解析CSV文件
 */
export function parseCSV(file: File): Promise<string[][]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string
        const lines = text.split('\n').filter(line => line.trim())
        const data = lines.map(line => {
          const values: string[] = []
          let current = ''
          let inQuotes = false

          for (let i = 0; i < line.length; i++) {
            const char = line[i]
            if (char === '"') {
              if (inQuotes && line[i + 1] === '"') {
                current += '"'
                i++
              } else {
                inQuotes = !inQuotes
              }
            } else if (char === ',' && !inQuotes) {
              values.push(current.trim())
              current = ''
            } else {
              current += char
            }
          }
          values.push(current.trim())
          return values
        })
        resolve(data)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'utf-8')
  })
}

/**
 * 验证和转换导入数据
 */
export function validateImportData(rawData: string[][], columns: ImportColumn[]): ImportResult {
  const errors: ImportResult[] = []
  const validData: unknown[] = []

  // 跳过表头行
  const dataRows = rawData.slice(1)

  dataRows.forEach((row, rowIndex) => {
    const rowData: unknown = {}
    let hasErrors = false

    columns.forEach((column, colIndex) => {
      const value = row[colIndex]?.trim() || ''

      // 必填验证
      if (column.required && !value) {
        errors.push({
          row: rowIndex + 2, // +2 因为跳过了表头且从1开始计数
          column: column.label,
          message: '此字段为必填项'
        })
        hasErrors = true
        return
      }

      // 自定义验证
      if (column.validator && value) {
        const validationError = column.validator(value)
        if (validationError) {
          errors.push({
            row: rowIndex + 2,
            column: column.label,
            message: validationError
          })
          hasErrors = true
          return
        }
      }

      // 数据转换
      let transformedValue = value
      if (column.transformer && value) {
        try {
          transformedValue = column.transformer(value)
        } catch (error) {
          errors.push({
            row: rowIndex + 2,
            column: column.label,
            message: '数据格式转换失败'
          })
          hasErrors = true
          return
        }
      }

      rowData[column.key] = transformedValue
    })

    if (!hasErrors) {
      validData.push(rowData)
    }
  })

  return {
    success: errors.length === 0,
    data: validData,
    errors,
    summary: {
      total: dataRows.length,
      valid: validData.length,
      invalid: dataRows.length - validData.length
    }
  }
}

/**
 * 预定义的导入配置
 */
export const importConfigs = {
  clients: {
    columns: [
      { key: 'last_name', label: '姓', required: true },
      { key: 'first_name', label: '名', required: true },
      { key: 'phone', label: '电话', required: true, validator: (value: string) => {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          return '请输入有效的手机号码'
        }
        return null
      }},
      { key: 'email', label: '邮箱', validator: (value: string) => {
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return '请输入有效的邮箱地址'
        }
        return null
      }},
      { key: 'date_of_birth', label: '出生日期', transformer: (value: string) => {
        if (!value) return null
        const date = new Date(value)
        return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0]
      }},
      { key: 'address_line_1', label: '地址' },
      { key: 'city', label: '城市' },
      { key: 'state_province', label: '省份' },
      { key: 'postal_code', label: '邮编' }
    ] as ImportColumn[]
  }
}
