/**
 * Database Performance Optimization Layer
 * Advanced query optimization, connection pooling, and performance monitoring
 */

import { createServerClient } from '../supabase/client'
import { cache, cacheUtils } from '../cache'
import { logger } from '@/lib/logger';
import { monitor } from '../monitoring'

/**
 * Query performance configuration
 */
interface QueryConfig {
  cache?: {
    enabled: boolean
    ttl: number
    tags: string[]
    namespace: string
  }
  timeout?: number
  retries?: number
  monitor?: boolean
}

/**
 * Pagination configuration
 */
interface PaginationConfig {
  page: number
  limit: number
  offset?: number
}

/**
 * Query result with metadata
 */
interface QueryResult<T> {
  data: T[]
  count?: number
  page?: number
  limit?: number
  totalPages?: number
  fromCache: boolean
  executionTime: number
}

/**
 * Database connection pool manager
 */
class ConnectionPool {
  private connections: Map<string, any> = new Map()
  private maxConnections = 10
  private activeConnections = 0

  async getConnection(key: string = 'default') {
    if (this.connections.has(key)) {
      return this.connections.get(key)
    }

    if (this.activeConnections >= this.maxConnections) {
      throw new Error('Connection pool exhausted')
    }

    const connection = createServerClient()
    this.connections.set(key, connection)
    this.activeConnections++

    logger.debug('Database connection created', { key, active: this.activeConnections })
    monitor.gauge('db.connections.active', this.activeConnections)

    return connection
  }

  async releaseConnection(key: string = 'default') {
    if (this.connections.has(key)) {
      this.connections.delete(key)
      this.activeConnections--
      monitor.gauge('db.connections.active', this.activeConnections)
    }
  }

  getStats() {
    return {
      active: this.activeConnections,
      max: this.maxConnections,
      utilization: this.activeConnections / this.maxConnections,
    }
  }
}

/**
 * Query optimizer and executor
 */
class QueryOptimizer {
  private connectionPool = new ConnectionPool()

  /**
   * Execute optimized query with caching and monitoring
   */
  async executeQuery<T>(
    table: string,
    queryBuilder: (client: unknown) => any,
    config: QueryConfig = {}
  ): Promise<QueryResult<T>> {
    const {
      cache: cacheConfig = { enabled: false, ttl: 300, tags: [], namespace: 'db' },
      timeout = 30000,
      retries = 2,
      monitor: shouldMonitor = true,
    } = config

    const dbLogger = createDbLogger('query', table)
    const timer = createTimer(dbLogger, `${table} query`)

    // Generate cache key if caching is enabled
    const cacheKey = cacheConfig.enabled
      ? cacheUtils.key(cacheConfig.namespace, table, JSON.stringify(queryBuilder.toString()))
      : null

    // Try cache first
    if (cacheKey && cacheConfig.enabled) {
      const cached = await cache.get<QueryResult<T>>(cacheKey)
      if (cached) {
        timer.end({ fromCache: true })
        monitor.counter('db.query.cache_hit', 1, { table })
        return { ...cached, fromCache: true }
      }
    }

    let lastError: Error | null = null
    let attempt = 0

    while (attempt <= retries) {
      try {
        const client = await this.connectionPool.getConnection()
        const startTime = Date.now()

        // Execute query with timeout
        const queryPromise = queryBuilder(client.from(table))
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query timeout')), timeout)
        )

        const { data, error, count } = await Promise.race([queryPromise, timeoutPromise])
        const executionTime = Date.now() - startTime

        if (error) {
          throw error
        }

        const result: QueryResult<T> = {
          data: data ?? [],
          count,
          fromCache: false,
          executionTime,
        }

        // Cache the result if caching is enabled
        if (cacheKey && cacheConfig.enabled) {
          await cache.set(cacheKey, result, {
            ttl: cacheConfig.ttl,
            tags: [...cacheConfig.tags, table],
            namespace: cacheConfig.namespace,
          })
          monitor.counter('db.query.cache_set', 1, { table })
        }

        // Monitor performance
        if (shouldMonitor) {
          monitor.histogram('db.query.duration', executionTime, { table })
          monitor.counter('db.query.success', 1, { table })
          
          if (executionTime > 1000) {
            dbLogger.warn('Slow query detected', { table, executionTime, attempt })
          }
        }

        timer.end({ success: true, executionTime, fromCache: false })
        return result

      } catch (error) {
        lastError = error as Error
        attempt++
        
        if (attempt <= retries) {
          const delay = Math.pow(2, attempt) * 100 // Exponential backoff
          dbLogger.warn('Query failed, retrying', { table, attempt, delay, error: lastError.message })
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // All retries failed
    timer.error(lastError!, { table, attempts: attempt })
    monitor.counter('db.query.error', 1, { table, error: lastError!.message })
    throw lastError
  }

  /**
   * Optimized pagination query
   */
  async paginatedQuery<T>(
    table: string,
    queryBuilder: (client: unknown) => any,
    pagination: PaginationConfig,
    config: QueryConfig = {}
  ): Promise<QueryResult<T>> {
    const { page, limit } = pagination
    const offset = (page - 1) * limit

    // Enhanced query builder with pagination
    const paginatedQueryBuilder = (client: unknown) => {
      const query = queryBuilder(client)
      return query.range(offset, offset + limit - 1)
    }

    // Add pagination info to cache key
    const enhancedConfig = {
      ...config,
      cache: config.cache ? {
        ...config.cache,
        namespace: `${config.cache.namespace}_paginated`,
        tags: [...(config.cache.tags ?? []), `page_${page}`, `limit_${limit}`],
      } : undefined,
    }

    const result = await this.executeQuery<T>(table, paginatedQueryBuilder, enhancedConfig)

    // Calculate pagination metadata
    if (result.count !== undefined) {
      const totalPages = Math.ceil(result.count / limit)
      return {
        ...result,
        page,
        limit,
        totalPages,
      }
    }

    return { ...result, page, limit }
  }

  /**
   * Batch query execution
   */
  async batchQuery<T>(
    queries: Array<{
      table: string
      queryBuilder: (client: unknown) => any
      config?: QueryConfig
    }>
  ): Promise<QueryResult<T>[]> {
    const batchTimer = createTimer(logger, 'batch query')
    
    try {
      const results = await Promise.all(
        queries.map(({ table, queryBuilder, config }) =>
          this.executeQuery<T>(table, queryBuilder, config)
        )
      )

      batchTimer.end({ queries: queries.length, success: true })
      monitor.counter('db.batch_query.success', 1, { size: queries.length })
      
      return results
    } catch (error) {
      batchTimer.error(error as Error, { queries: queries.length })
      monitor.counter('db.batch_query.error', 1, { size: queries.length })
      throw error
    }
  }

  /**
   * Get connection pool statistics
   */
  getConnectionStats() {
    return this.connectionPool.getStats()
  }
}

/**
 * Database performance monitoring
 */
class DatabaseMonitor {
  private slowQueries: Array<{
    table: string
    duration: number
    timestamp: number
    query: string
  }> = []

  recordSlowQuery(table: string, duration: number, query: string) {
    this.slowQueries.push({
      table,
      duration,
      timestamp: Date.now(),
      query,
    })

    // Keep only last 100 slow queries
    if (this.slowQueries.length > 100) {
      this.slowQueries = this.slowQueries.slice(-100)
    }

    logger.warn('Slow query recorded', { table, duration })
    monitor.counter('db.slow_query', 1, { table })
  }

  getSlowQueries(limit: number = 10) {
    return this.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  getPerformanceMetrics() {
    const now = Date.now()
    const recentQueries = this.slowQueries.filter(q => now - q.timestamp < 3600000) // Last hour

    return {
      slowQueriesLastHour: recentQueries.length,
      averageSlowQueryDuration: recentQueries.length > 0
        ? recentQueries.reduce((sum, q) => sum + q.duration, 0) / recentQueries.length
        : 0,
      slowestQuery: recentQueries.length > 0
        ? Math.max(...recentQueries.map(q => q.duration))
        : 0,
    }
  }
}

/**
 * Cache invalidation strategies
 */
export const cacheInvalidation = {
  /**
   * Invalidate cache for specific table
   */
  invalidateTable: async (table: string): Promise<void> => {
    await cache.invalidateByTags([table])
    logger.debug('Cache invalidated for table', { table })
  },

  /**
   * Invalidate cache for specific entity
   */
  invalidateEntity: async (table: string, id: string): Promise<void> => {
    await cache.invalidateByTags([table, `${table}_${id}`])
    logger.debug('Cache invalidated for entity', { table, id })
  },

  /**
   * Invalidate related caches
   */
  invalidateRelated: async (relationships: Record<string, string[]>): Promise<void> => {
    const tags = Object.entries(relationships).flatMap(([table, ids]) => [])
    
    await cache.invalidateByTags(tags)
    logger.debug('Related caches invalidated', { relationships })
  },
}

/**
 * Global instances
 */
export const queryOptimizer = new QueryOptimizer()
export const dbMonitor = new DatabaseMonitor()

/**
 * High-level database utilities
 */
export const db = {
  /**
   * Execute optimized query
   */
  query: <T>(
    table: string,
    queryBuilder: (client: unknown) => any,
    config?: QueryConfig
  ) => queryOptimizer.executeQuery<T>(table, queryBuilder, config),

  /**
   * Execute paginated query
   */
  paginate: <T>(
    table: string,
    queryBuilder: (client: unknown) => any,
    pagination: PaginationConfig,
    config?: QueryConfig
  ) => queryOptimizer.paginatedQuery<T>(table, queryBuilder, pagination, config),

  /**
   * Execute batch queries
   */
  batch: <T>(
    queries: Array<{
      table: string
      queryBuilder: (client: unknown) => any
      config?: QueryConfig
    }>
  ) => queryOptimizer.batchQuery<T>(queries),

  /**
   * Get performance statistics
   */
  getStats: () => ({
    connections: queryOptimizer.getConnectionStats(),
    performance: dbMonitor.getPerformanceMetrics(),
    slowQueries: dbMonitor.getSlowQueries(),
  }),

  /**
   * Cache management
   */
  cache: cacheInvalidation,
}

/**
 * Database health check
 */
export const dbHealth = {
  check: async () => {
    try {
      const client = await queryOptimizer['connectionPool'].getConnection('health')
      const startTime = Date.now()
      
      // Simple health check query
      const { data, error } = await client.from('clients').select('id').limit(1)
      const duration = Date.now() - startTime
      
      if (error) {
        throw error
      }

      const stats = queryOptimizer.getConnectionStats()
      
      return {
        status: 'healthy' as const,
        responseTime: duration,
        connections: stats,
      }
    } catch (error) {
      logger.error('Database health check failed', error as Error)
      return {
        status: 'unhealthy' as const,
        error: (error as Error).message,
      }
    }
  },
}
