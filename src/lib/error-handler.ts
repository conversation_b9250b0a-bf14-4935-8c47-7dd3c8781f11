/**
 * Enhanced Error Handling System
 * Provides comprehensive error handling for the application
 */

import { logger } from '@/lib/logger';
import { showErrorToast, showSuccessToast } from '@/lib/toast-utils';

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly code?: string;
  public readonly details?: Record<string, unknown>;
  public readonly timestamp: Date;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    code?: string,
    details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
  }
}

// Error context interface
interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, unknown>;
}

// Error handler options
interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  reportError?: boolean;
  fallbackMessage?: string;
  context?: ErrorContext;
}

// Default error messages
const DEFAULT_ERROR_MESSAGES = {
  [ErrorType.NETWORK]: '网络连接失败，请检查网络连接后重试',
  [ErrorType.VALIDATION]: '输入数据有误，请检查后重试',
  [ErrorType.AUTHENTICATION]: '身份验证失败，请重新登录',
  [ErrorType.AUTHORIZATION]: '权限不足，无法执行此操作',
  [ErrorType.NOT_FOUND]: '请求的资源不存在',
  [ErrorType.SERVER]: '服务器内部错误，请稍后重试',
  [ErrorType.CLIENT]: '客户端请求错误',
  [ErrorType.UNKNOWN]: '发生未知错误，请稍后重试'
};

// Error classification function
export function classifyError(error: unknown): AppError {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Error) {
    // Network errors
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return new AppError(
        error.message,
        ErrorType.NETWORK,
        ErrorSeverity.MEDIUM,
        'NETWORK_ERROR'
      );
    }

    // Validation errors
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return new AppError(
        error.message,
        ErrorType.VALIDATION,
        ErrorSeverity.LOW,
        'VALIDATION_ERROR'
      );
    }

    // Authentication errors
    if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
      return new AppError(
        error.message,
        ErrorType.AUTHENTICATION,
        ErrorSeverity.HIGH,
        'AUTH_ERROR'
      );
    }

    // Generic error
    return new AppError(
      error.message,
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      'GENERIC_ERROR'
    );
  }

  // Unknown error type
  return new AppError(
    String(error),
    ErrorType.UNKNOWN,
    ErrorSeverity.MEDIUM,
    'UNKNOWN_ERROR'
  );
}

// Main error handler function
export function handleError(
  error: unknown,
  options: ErrorHandlerOptions = {}
): AppError {
  const {
    showToast = true,
    logError = true,
    reportError = false,
    fallbackMessage,
    context
  } = options;

  const appError = classifyError(error);

  // Log error
  if (logError) {
    const logContext = {
      type: appError.type,
      severity: appError.severity,
      code: appError.code,
      details: appError.details,
      context,
      timestamp: appError.timestamp
    };

    switch (appError.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error(`CRITICAL ERROR: ${appError.message}`, logContext);
        break;
      case ErrorSeverity.HIGH:
        logger.error(`HIGH SEVERITY: ${appError.message}`, logContext);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(`MEDIUM SEVERITY: ${appError.message}`, logContext);
        break;
      case ErrorSeverity.LOW:
        logger.info(`LOW SEVERITY: ${appError.message}`, logContext);
        break;
    }
  }

  // Show toast notification
  if (showToast) {
    const message = fallbackMessage ?? DEFAULT_ERROR_MESSAGES[appError.type] ?? appError.message;
    showErrorToast('操作失败', message);
  }

  // Report error to monitoring service (if enabled)
  if (reportError && appError.severity === ErrorSeverity.CRITICAL) {
    // TODO: Implement error reporting to external service
    logger.error('Critical error reported to monitoring service', {
      error: appError,
      context
    });
  }

  return appError;
}

// Async operation wrapper with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    handleError(error, options);
    return null;
  }
}

// API call wrapper with error handling
export async function apiCall<T>(
  url: string,
  options: RequestInit = {},
  errorOptions: ErrorHandlerOptions = {}
): Promise<T | null> {
  return withErrorHandling(async () => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      let errorType = ErrorType.SERVER;
      let severity = ErrorSeverity.MEDIUM;

      switch (response.status) {
        case 400:
          errorType = ErrorType.VALIDATION;
          severity = ErrorSeverity.LOW;
          break;
        case 401:
          errorType = ErrorType.AUTHENTICATION;
          severity = ErrorSeverity.HIGH;
          break;
        case 403:
          errorType = ErrorType.AUTHORIZATION;
          severity = ErrorSeverity.HIGH;
          break;
        case 404:
          errorType = ErrorType.NOT_FOUND;
          severity = ErrorSeverity.LOW;
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorType = ErrorType.SERVER;
          severity = ErrorSeverity.HIGH;
          break;
      }

      throw new AppError(
        errorData.message ?? `HTTP ${response.status}: ${response.statusText}`,
        errorType,
        severity,
        `HTTP_${response.status}`,
        { status: response.status, url, errorData }
      );
    }

    return response.json();
  }, errorOptions);
}

// Form submission wrapper with error handling
export async function handleFormSubmission<T>(
  formData: FormData | Record<string, unknown>,
  submitFunction: (data: FormData | Record<string, unknown>) => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  const context = {
    component: 'Form',
    action: 'Submit',
    ...options.context
  };

  return withErrorHandling(async () => {
    const result = await submitFunction(formData);
    
    // Show success message if operation completed successfully
    if (options.showToast !== false) {
      showSuccessToast('操作成功', '数据已成功保存');
    }
    
    return result;
  }, { ...options, context });
}

// Retry mechanism for failed operations
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
      
      logger.warn(`Operation failed, retrying (${attempt}/${maxRetries})`, {
        error: classifyError(error),
        attempt
      });
    }
  }

  // Handle final error after all retries failed
  return withErrorHandling(
    async () => {
      throw lastError;
    },
    {
      ...options,
      context: {
        ...options.context,
        retriesAttempted: maxRetries
      }
    }
  );
}
