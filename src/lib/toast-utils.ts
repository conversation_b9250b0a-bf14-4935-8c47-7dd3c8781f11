import { toast } from 'sonner'
import { CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react'
import React from 'react'

// 成功提示
export const showSuccessToast = (message: string, description?: string) => {
  toast.success(message, {
    description,
    icon: React.createElement(CheckCircle, { size: 16 }),
    duration: 4000,
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--border))',
      color: 'hsl(var(--foreground))',
    },
  })
}

// 错误提示
export const showErrorToast = (message: string, description?: string) => {
  toast.error(message, {
    description,
    icon: React.createElement(XCircle, { size: 16 }),
    duration: 6000,
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--destructive))',
      color: 'hsl(var(--foreground))',
    },
  })
}

// 警告提示
export const showWarningToast = (message: string, description?: string) => {
  toast.warning(message, {
    description,
    icon: React.createElement(AlertCircle, { size: 16 }),
    duration: 5000,
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--border))',
      color: 'hsl(var(--foreground))',
    },
  })
}

// 信息提示
export const showInfoToast = (message: string, description?: string) => {
  toast.info(message, {
    description,
    icon: React.createElement(Info, { size: 16 }),
    duration: 4000,
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--border))',
      color: 'hsl(var(--foreground))',
    },
  })
}

// 加载提示
export const showLoadingToast = (message: string) => {
  return toast.loading(message, {
    style: {
      background: 'hsl(var(--background))',
      border: '1px solid hsl(var(--border))',
      color: 'hsl(var(--foreground))',
    },
  })
}

// 操作成功的通用提示
export const showOperationSuccess = (operation: string, item?: string) => {
  const message = item ? `${operation}${item}成功` : `${operation}成功`
  showSuccessToast(message)
}

// 操作失败的通用提示
export const showOperationError = (operation: string, item?: string, error?: string) => {
  const message = item ? `${operation}${item}失败` : `${operation}失败`
  const description = error ?? '请稍后重试或联系管理员'
  showErrorToast(message, description)
}
