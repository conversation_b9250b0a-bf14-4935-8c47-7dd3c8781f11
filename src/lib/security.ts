/**
 * Professional Security Utilities
 * Provides comprehensive security features including encryption, rate limiting, and audit logging
 */

import crypto from 'crypto'
import { NextRequest } from 'next/server'
import { logger } from './logger'
import { env, serverEnv } from './env'
import { RateLimitError, AuthenticationError } from './errors'
import { logger } from '@/lib/logger';

/**
 * Encryption utilities for sensitive data
 */
export class Encryption {
  private static readonly ALGORITHM = 'aes-256-gcm'
  private static readonly KEY_LENGTH = 32
  private static readonly IV_LENGTH = 16
  private static readonly TAG_LENGTH = 16

  /**
   * Get encryption key from environment or generate one
   */
  private static getKey(): Buffer {
    if (serverEnv.DATABASE_ENCRYPTION_KEY) {
      return Buffer.from(serverEnv.DATABASE_ENCRYPTION_KEY, 'hex')
    }
    
    // In development, use a fixed key (NOT for production)
    if (env.NODE_ENV === 'development') {
      return Buffer.from('a'.repeat(64), 'hex')
    }
    
    throw new Error('DATABASE_ENCRYPTION_KEY is required for encryption')
  }

  /**
   * Encrypt sensitive data
   */
  static encrypt(text: string): string {
    try {
      const key = this.getKey()
      const iv = crypto.randomBytes(this.IV_LENGTH)
      const cipher = crypto.createCipher(this.ALGORITHM, key)
      cipher.setAAD(Buffer.from('medical-crm', 'utf8'))

      let encrypted = cipher.update(text, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      
      const tag = cipher.getAuthTag()
      
      // Combine IV, tag, and encrypted data
      return `${iv.toString('hex')  }:${  tag.toString('hex')  }:${  encrypted}`
    } catch (error) {
      logger.error('Encryption failed', error as Error)
      throw new Error('Failed to encrypt data')
    }
  }

  /**
   * Decrypt sensitive data
   */
  static decrypt(encryptedData: string): string {
    try {
      const key = this.getKey()
      const parts = encryptedData.split(':')
      
      if (parts.length !== 3) {
        throw new Error('Invalid encrypted data format')
      }
      
      const iv = Buffer.from(parts[0], 'hex')
      const tag = Buffer.from(parts[1], 'hex')
      const encrypted = parts[2]
      
      const decipher = crypto.createDecipher(this.ALGORITHM, key)
      decipher.setAAD(Buffer.from('medical-crm', 'utf8'))
      decipher.setAuthTag(tag)
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      
      return decrypted
    } catch (error) {
      logger.error('Decryption failed', error as Error)
      throw new Error('Failed to decrypt data')
    }
  }

  /**
   * Hash password with salt
   */
  static hashPassword(password: string): string {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
    return `${salt  }:${  hash}`
  }

  /**
   * Verify password against hash
   */
  static verifyPassword(password: string, hashedPassword: string): boolean {
    const parts = hashedPassword.split(':')
    if (parts.length !== 2) return false
    
    const salt = parts[0]
    const hash = parts[1]
    const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
    
    return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(verifyHash, 'hex'))
  }
}

/**
 * Rate limiting implementation
 */
export class RateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>()

  /**
   * Check if request is within rate limit
   */
  static async checkRateLimit(
    identifier: string,
    limit: number,
    windowMs: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const now = Date.now()
    const windowStart = now - windowMs
    
    // Clean up old entries
    for (const [key, value] of this.requests.entries()) {
      if (value.resetTime < now) {
        this.requests.delete(key)
      }
    }
    
    const current = this.requests.get(identifier)
    
    if (!current ?? current.resetTime < now) {
      // First request in window or window expired
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + windowMs,
      })
      
      return {
        allowed: true,
        remaining: limit - 1,
        resetTime: now + windowMs,
      }
    }
    
    if (current.count >= limit) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: current.resetTime,
      }
    }
    
    // Increment count
    current.count++
    this.requests.set(identifier, current)
    
    return {
      allowed: true,
      remaining: limit - current.count,
      resetTime: current.resetTime,
    }
  }

  /**
   * Rate limit middleware for API routes
   */
  static async middleware(
    request: NextRequest,
    limit: number = 100,
    windowMs: number = 15 * 60 * 1000 // 15 minutes
  ): Promise<void> {
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'
    
    const { allowed, remaining, resetTime } = await this.checkRateLimit(ip, limit, windowMs)
    
    if (!allowed) {
      logger.warn('Rate limit exceeded', { ip, limit, windowMs })
      throw new RateLimitError('请求过于频繁，请稍后再试')
    }
    
    // Log rate limit info for monitoring
    logger.debug('Rate limit check', { ip, remaining, resetTime })
  }
}

/**
 * Audit logging for sensitive operations
 */
export class AuditLogger {
  /**
   * Log sensitive operation
   */
  static async logOperation(
    operation: string,
    resource: string,
    resourceId: string,
    userId?: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    const auditLog = {
      operation,
      resource,
      resourceId,
      userId,
      metadata,
      timestamp: new Date().toISOString(),
      ip: metadata?.ip as string,
      userAgent: metadata?.userAgent as string,
    }

    // Log to application logger
    logger.info('Audit log', auditLog)

    // TODO: In production, also store in dedicated audit log table
    // await supabase.from('audit_logs').insert(auditLog)
  }

  /**
   * Log authentication event
   */
  static async logAuth(
    event: 'login' | 'logout' | 'failed_login' | 'password_change',
    userId?: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.logOperation('auth', event, userId ?? 'unknown', userId, metadata)
  }

  /**
   * Log data access
   */
  static async logDataAccess(
    resource: string,
    resourceId: string,
    operation: 'read' | 'create' | 'update' | 'delete',
    userId?: string,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.logOperation(`data_${operation}`, resource, resourceId, userId, metadata)
  }
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML to prevent XSS
   */
  static sanitizeHtml(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  /**
   * Sanitize SQL to prevent injection (basic)
   */
  static sanitizeSql(input: string): string {
    return input
      .replace(/'/g, "''")
      .replace(/;/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '')
  }

  /**
   * Validate and sanitize phone number
   */
  static sanitizePhone(phone: string): string {
    return phone.replace(/[^\d+\-\s()]/g, '')
  }

  /**
   * Validate and sanitize email
   */
  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim()
  }
}

/**
 * Security headers utilities
 */
export class SecurityHeaders {
  /**
   * Get security headers for responses
   */
  static getHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self' https://api.clerk.dev https://*.supabase.co",
        "frame-ancestors 'none'",
      ].join('; '),
    }
  }
}

/**
 * CSRF protection utilities
 */
export class CSRFProtection {
  private static readonly SECRET = 'medical-crm-csrf-secret'

  /**
   * Generate CSRF token
   */
  static generateToken(sessionId: string): string {
    const timestamp = Date.now().toString()
    const data = sessionId + timestamp
    const hash = crypto.createHmac('sha256', this.SECRET).update(data).digest('hex')
    return Buffer.from(`${timestamp  }:${  hash}`).toString('base64')
  }

  /**
   * Verify CSRF token
   */
  static verifyToken(token: string, sessionId: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString('utf8')
      const [timestamp, hash] = decoded.split(':')
      
      // Check if token is not too old (1 hour)
      const tokenAge = Date.now() - parseInt(timestamp)
      if (tokenAge > 60 * 60 * 1000) {
        return false
      }
      
      const data = sessionId + timestamp
      const expectedHash = crypto.createHmac('sha256', this.SECRET).update(data).digest('hex')
      
      return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(expectedHash, 'hex'))
    } catch {
      return false
    }
  }
}

/**
 * Session security utilities
 */
export class SessionSecurity {
  /**
   * Generate secure session ID
   */
  static generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Validate session format
   */
  static isValidSessionId(sessionId: string): boolean {
    return /^[a-f0-9]{64}$/.test(sessionId)
  }
}

/**
 * Data masking utilities for logging
 */
export class DataMasking {
  /**
   * Mask sensitive data for logging
   */
  static maskSensitiveData(data: unknown): unknown {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    const masked = { ...data }
    const sensitiveFields = ['password', 'ssn', 'credit_card', 'phone', 'email']

    for (const field of sensitiveFields) {
      if (masked[field]) {
        if (field === 'phone' || field === 'email') {
          masked[field] = this.maskPartial(masked[field])
        } else {
          masked[field] = '***MASKED***'
        }
      }
    }

    return masked
  }

  /**
   * Partially mask data (show first and last characters)
   */
  private static maskPartial(value: string): string {
    if (value.length <= 4) {
      return '*'.repeat(value.length)
    }
    
    const start = value.substring(0, 2)
    const end = value.substring(value.length - 2)
    const middle = '*'.repeat(value.length - 4)
    
    return start + middle + end
  }
}
