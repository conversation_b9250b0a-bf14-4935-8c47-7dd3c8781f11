import { supabase, createServerClient } from './client'
import type { Database } from './types'
import type { SupabaseClient } from '@supabase/supabase-js'

// Type aliases for easier use
type Client = Database['public']['Tables']['clients']['Row']
type ClientInsert = Database['public']['Tables']['clients']['Insert']
type ClientUpdate = Database['public']['Tables']['clients']['Update']

type Treatment = Database['public']['Tables']['treatments']['Row']
type TreatmentInsert = Database['public']['Tables']['treatments']['Insert']

type Appointment = Database['public']['Tables']['appointments']['Row']
type AppointmentInsert = Database['public']['Tables']['appointments']['Insert']
type AppointmentUpdate = Database['public']['Tables']['appointments']['Update']

type Invoice = Database['public']['Tables']['invoices']['Row']
type InvoiceInsert = Database['public']['Tables']['invoices']['Insert']
type InvoiceUpdate = Database['public']['Tables']['invoices']['Update']

type Payment = Database['public']['Tables']['payments']['Row']
type PaymentInsert = Database['public']['Tables']['payments']['Insert']
type PaymentUpdate = Database['public']['Tables']['payments']['Update']

type InvoiceStatus = Database['public']['Tables']['invoices']['Row']['status']

type ContactLog = Database['public']['Tables']['contact_logs']['Row']
type ContactLogInsert = Database['public']['Tables']['contact_logs']['Insert']

// Helper function to create queries with a specific Supabase client
const createClientQueries = (client: SupabaseClient) => ({
  // Get all clients
  getAll: async () => {
    const { data, error } = await client
      .from('clients')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get client by ID
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Search clients by name or phone
  search: async (query: string) => {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,phone.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Create new client
  create: async (client: ClientInsert) => {
    const { data, error } = await supabase
      .from('clients')
      .insert(client)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update client
  update: async (id: string, updates: ClientUpdate) => {
    const { data, error } = await supabase
      .from('clients')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete client
  delete: async (id: string) => {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
})

// Client queries for client-side use
export const clientQueries = createClientQueries(supabase)

// Server queries for API routes - lazy initialization
export const getServerClientQueries = () => createClientQueries(createServerClient())

// Treatment queries
export const treatmentQueries = {
  // Get all active treatments
  getActive: async () => {
    const { data, error } = await supabase
      .from('treatments')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true })

    if (error) throw error
    return data ?? []
  },

  // Get all treatments
  getAll: async () => {
    const { data, error } = await supabase
      .from('treatments')
      .select('*')
      .order('category', { ascending: true })

    if (error) throw error
    return data ?? []
  },

  // Get treatment by ID
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('treatments')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Create new treatment
  create: async (treatment: TreatmentInsert) => {
    const { data, error } = await supabase
      .from('treatments')
      .insert(treatment)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update treatment
  update: async (id: string, updates: Partial<TreatmentInsert>) => {
    const { data, error } = await supabase
      .from('treatments')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete treatment
  delete: async (id: string) => {
    const { error } = await supabase
      .from('treatments')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Appointment queries
export const appointmentQueries = {
  // Get appointments by date range
  getByDateRange: async (startDate: string, endDate: string) => {
    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        clients (
          id,
          first_name,
          last_name,
          phone
        ),
        treatments (
          id,
          name,
          name_chinese,
          default_price,
          fixed_deposit_amount,
          consultation_fee,
          duration_minutes,
          requires_consultation
        )
      `)
      .gte('appointment_date', startDate)
      .lte('appointment_date', endDate)
      .order('appointment_date', { ascending: true })
      .order('start_time', { ascending: true })

    if (error) throw error
    return data ?? []
  },

  // Get appointments for a specific client
  getByClientId: async (clientId: string) => {
    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        treatments (
          id,
          name,
          name_chinese,
          default_price,
          fixed_deposit_amount,
          consultation_fee,
          requires_consultation
        )
      `)
      .eq('client_id', clientId)
      .order('appointment_date', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Create new appointment
  create: async (appointment: AppointmentInsert) => {
    const { data, error } = await supabase
      .from('appointments')
      .insert(appointment)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get appointment by ID
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        clients (
          id,
          first_name,
          last_name,
          phone
        ),
        treatments (
          id,
          name,
          name_chinese,
          default_price,
          fixed_deposit_amount,
          consultation_fee,
          duration_minutes,
          requires_consultation
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  // Update appointment
  update: async (id: string, updates: AppointmentUpdate) => {
    const { data, error } = await supabase
      .from('appointments')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete appointment
  delete: async (id: string) => {
    const { error } = await supabase
      .from('appointments')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Contact log queries
export const contactLogQueries = {
  // Get contact logs for a client
  getByClientId: async (clientId: string) => {
    const { data, error } = await supabase
      .from('contact_logs')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Create new contact log
  create: async (contactLog: ContactLogInsert) => {
    const { data, error } = await supabase
      .from('contact_logs')
      .insert(contactLog)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Invoice queries
export const invoiceQueries = {
  // Get all invoices with client and payment info
  getAll: async () => {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        clients (
          id,
          first_name,
          last_name,
          phone
        ),
        payments (
          id,
          amount,
          payment_date,
          payment_method,
          payment_type
        )
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get invoices by client ID
  getByClientId: async (clientId: string) => {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        payments (
          id,
          amount,
          payment_date,
          payment_method,
          payment_type
        ),
        invoice_items (
          id,
          treatment_name,
          treatment_name_chinese,
          quantity,
          unit_price,
          total_price
        )
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get invoice by ID
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        clients (
          id,
          first_name,
          last_name,
          phone,
          email
        ),
        payments (
          id,
          amount,
          payment_date,
          payment_method,
          payment_type,
          reference_number,
          notes
        ),
        invoice_items (
          id,
          appointment_id,
          treatment_name,
          treatment_name_chinese,
          quantity,
          unit_price,
          total_price
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  // Create new invoice
  create: async (invoice: InvoiceInsert) => {
    const { data, error } = await supabase
      .from('invoices')
      .insert(invoice)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update invoice
  update: async (id: string, updates: InvoiceUpdate) => {
    const { data, error } = await supabase
      .from('invoices')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete invoice
  delete: async (id: string) => {
    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Payment queries
export const paymentQueries = {
  // Get all payments with invoice and client info
  getAll: async () => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        invoices (
          id,
          invoice_number,
          treatment_date,
          total_amount
        ),
        clients (
          id,
          first_name,
          last_name,
          phone
        )
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get payments by invoice ID
  getByInvoiceId: async (invoiceId: string) => {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get payments by client ID
  getByClientId: async (clientId: string) => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        invoices (
          id,
          invoice_number,
          treatment_date
        )
      `)
      .eq('client_id', clientId)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data ?? []
  },

  // Get payment by ID
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        invoices (
          id,
          invoice_number,
          treatment_date,
          total_amount
        ),
        clients (
          id,
          first_name,
          last_name,
          phone
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  // Create new payment
  create: async (payment: PaymentInsert) => {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update payment
  update: async (id: string, updates: PaymentUpdate) => {
    const { data, error } = await supabase
      .from('payments')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete payment
  delete: async (id: string) => {
    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Utility functions for business logic
export const businessLogic = {
  // Calculate deposit amount based on fixed amounts from treatments (new business rule)
  calculateFixedDeposit: async (appointments: Appointment[]) => {
    let maxDeposit = 0

    // Get the highest fixed deposit amount for the day (one deposit per day rule)
    for (const appointment of appointments) {
      const { data: treatment } = await supabase
        .from('treatments')
        .select('fixed_deposit_amount')
        .eq('id', appointment.treatment_id)
        .single()

      if (treatment && treatment.fixed_deposit_amount > maxDeposit) {
        maxDeposit = treatment.fixed_deposit_amount
      }
    }

    return maxDeposit
  },

  // Legacy function for backward compatibility (deprecated)
  calculateDeposit: (totalAmount: number, depositPercentage: number = 50) => {
    return Math.round((totalAmount * depositPercentage) / 100 * 100) / 100
  },

  // Generate invoice number
  generateInvoiceNumber: () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    return `INV-${year}${month}${day}-${timestamp}`
  },

  // Calculate consultation fees for appointments
  calculateConsultationFees: async (appointments: Appointment[]) => {
    let totalConsultationFee = 0

    for (const appointment of appointments) {
      if (appointment.appointment_type === 'consultation') {
        const { data: treatment } = await supabase
          .from('treatments')
          .select('consultation_fee, requires_consultation')
          .eq('id', appointment.treatment_id)
          .single()

        if (treatment?.requires_consultation) {
          totalConsultationFee += treatment.consultation_fee ?? 0
        }
      }
    }

    return totalConsultationFee
  },

  // Check if consultation fee should be waived (if client has treatment appointments after consultation)
  shouldWaiveConsultationFee: async (clientId: string, consultationDate: string) => {
    const { data: treatmentAppointments } = await supabase
      .from('appointments')
      .select('*')
      .eq('client_id', clientId)
      .eq('appointment_type', 'treatment')
      .gte('appointment_date', consultationDate)
      .limit(1)

    return treatmentAppointments && treatmentAppointments.length > 0
  },

  // Format client full name
  formatClientName: (client: Client) => {
    return `${client.last_name}${client.first_name}`
  },

  // Check if appointment conflicts with existing ones
  checkAppointmentConflict: async (
    appointmentDate: string,
    startTime: string,
    endTime: string,
    excludeId?: string
  ) => {
    let query = supabase
      .from('appointments')
      .select('id, start_time, end_time')
      .eq('appointment_date', appointmentDate)
      .neq('status', 'cancelled')
      .or(`start_time.lt.${endTime},end_time.gt.${startTime}`)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error
    return data && data.length > 0
  },

  // Get detailed conflicting appointments
  getConflictingAppointments: async (
    appointmentDate: string,
    startTime: string,
    endTime: string,
    excludeId?: string
  ) => {
    let query = supabase
      .from('appointments')
      .select(`
        id,
        start_time,
        end_time,
        status,
        clients (
          first_name,
          last_name
        ),
        treatments (
          name_chinese
        )
      `)
      .eq('appointment_date', appointmentDate)
      .neq('status', 'cancelled')
      .or(`start_time.lt.${endTime},end_time.gt.${startTime}`)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error

    return (data ?? []).map((appointment: unknown) => ({
      id: appointment.id,
      client_name: `${appointment.clients?.last_name ?? ''}${appointment.clients?.first_name ?? ''}`,
      treatment_name: appointment.treatments?.name_chinese ?? '',
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      status: appointment.status
    }))
  },

  // Generate invoice from completed appointments (core business logic)
  generateInvoiceFromAppointments: async (appointments: Appointment[]) => {
    // Group appointments by treatment_date (same day = one deposit rule)
    const appointmentsByDate = appointments.reduce((acc, appointment) => {
      const date = appointment.appointment_date
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(appointment)
      return acc
    }, {} as Record<string, Appointment[]>)

    const invoices = []

    // Create one invoice per treatment date
    for (const [treatmentDate, dateAppointments] of Object.entries(appointmentsByDate)) {
      const totalAmount = dateAppointments.reduce((sum, apt) => {
        return sum + (apt.custom_price ?? 0)
      }, 0)

      // Use new fixed deposit calculation
      const depositAmount = await businessLogic.calculateFixedDeposit(dateAppointments)

      // Calculate consultation fees
      const consultationFee = await businessLogic.calculateConsultationFees(dateAppointments)

      // Check if consultation fee should be waived
      const shouldWaive = await businessLogic.shouldWaiveConsultationFee(
        dateAppointments[0].client_id,
        treatmentDate
      ) || false

      const invoiceNumber = businessLogic.generateInvoiceNumber()

      // Create invoice
      const invoice: InvoiceInsert = {
        client_id: dateAppointments[0].client_id,
        invoice_number: invoiceNumber,
        invoice_date: new Date().toISOString().split('T')[0],
        treatment_date: treatmentDate,
        total_amount: totalAmount,
        deposit_amount: depositAmount,
        deposit_percentage: 50, // Keep for backward compatibility
        consultation_fee_waived: shouldWaive,
        original_consultation_fee: consultationFee,
        status: 'deposit_pending',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        notes: `账单包含 ${dateAppointments.length} 项治疗${consultationFee > 0 ? `，咨询费 ${shouldWaive ? '已减免' : `${consultationFee  }元`}` : ''}`
      }

      const createdInvoice = await invoiceQueries.create(invoice)

      // Create invoice items for each appointment
      for (const appointment of dateAppointments) {
        // Fetch treatment details for this appointment
        const { data: treatment } = await supabase
          .from('treatments')
          .select('name, name_chinese, default_price, fixed_deposit_amount, consultation_fee')
          .eq('id', appointment.treatment_id)
          .single()

        const invoiceItem = {
          invoice_id: createdInvoice.id,
          appointment_id: appointment.id,
          treatment_name: treatment?.name ?? '',
          treatment_name_chinese: treatment?.name_chinese ?? '',
          quantity: 1,
          unit_price: appointment.custom_price ?? (treatment?.default_price ?? 0),
          total_price: appointment.custom_price ?? (treatment?.default_price ?? 0)
        }

        await supabase.from('invoice_items').insert(invoiceItem)
      }

      invoices.push(createdInvoice)
    }

    return invoices
  },

  // Update invoice status based on payments
  updateInvoiceStatus: async (invoiceId: string) => {
    const invoice = await invoiceQueries.getById(invoiceId)
    const payments = await paymentQueries.getByInvoiceId(invoiceId)

    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0)

    let newStatus: InvoiceStatus
    if (totalPaid === 0) {
      newStatus = 'deposit_pending'
    } else if (totalPaid >= invoice.deposit_amount && totalPaid < invoice.total_amount) {
      newStatus = 'deposit_paid'
    } else if (totalPaid >= invoice.total_amount) {
      newStatus = 'paid_in_full'
    } else {
      newStatus = 'deposit_pending'
    }

    if (newStatus !== invoice.status) {
      await invoiceQueries.update(invoiceId, { status: newStatus })
    }

    return newStatus
  },

  // Create consultation invoice for consultation appointments
  createConsultationInvoice: async (appointment: Appointment) => {
    // Get treatment details to check consultation fee
    const { data: treatment } = await supabase
      .from('treatments')
      .select('consultation_fee, name, name_chinese')
      .eq('id', appointment.treatment_id)
      .single()

    if (!treatment?.consultation_fee ?? treatment.consultation_fee <= 0) {
      return null // No consultation fee required
    }

    const invoiceNumber = businessLogic.generateInvoiceNumber()

    // Create consultation invoice
    const invoice: InvoiceInsert = {
      client_id: appointment.client_id,
      invoice_number: invoiceNumber,
      invoice_date: new Date().toISOString().split('T')[0],
      treatment_date: appointment.appointment_date,
      total_amount: treatment.consultation_fee,
      deposit_amount: treatment.consultation_fee, // Full consultation fee as deposit
      deposit_percentage: 100,
      consultation_fee_waived: false,
      original_consultation_fee: treatment.consultation_fee,
      status: 'deposit_pending',
      due_date: appointment.appointment_date, // Due on appointment date
      notes: `咨询费账单 - ${treatment.name_chinese}`
    }

    const createdInvoice = await invoiceQueries.create(invoice)

    // Create invoice item for consultation
    const invoiceItem = {
      invoice_id: createdInvoice.id,
      appointment_id: appointment.id,
      treatment_name: treatment.name,
      treatment_name_chinese: `${treatment.name_chinese  } (咨询)`,
      quantity: 1,
      unit_price: treatment.consultation_fee,
      total_price: treatment.consultation_fee
    }

    await supabase.from('invoice_items').insert(invoiceItem)

    return createdInvoice
  },

  // Process consultation fee waiver when client books treatment
  processConsultationFeeWaiver: async (clientId: string, treatmentDate: string) => {
    // Find recent consultation invoices for this client that haven't been waived
    const { data: consultationInvoices } = await supabase
      .from('invoices')
      .select('*')
      .eq('client_id', clientId)
      .eq('consultation_fee_waived', false)
      .gt('original_consultation_fee', 0)
      .gte('treatment_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]) // Within last 30 days
      .order('treatment_date', { ascending: false })

    if (!consultationInvoices ?? consultationInvoices.length === 0) {
      return null
    }

    // Waive the most recent consultation fee
    const consultationInvoice = consultationInvoices[0]

    await invoiceQueries.update(consultationInvoice.id, {
      consultation_fee_waived: true,
      notes: `${consultationInvoice.notes ?? ''  } [咨询费已于${treatmentDate}减免]`
    })

    return consultationInvoice
  }
}
