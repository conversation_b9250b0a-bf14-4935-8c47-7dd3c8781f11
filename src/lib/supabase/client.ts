import { createClient } from '@supabase/supabase-js'
import type { Database } from './types'
import { env, serverEnv } from '../env'
// import { logger } from '@/lib/logger';
import { DatabaseError } from '../errors'

// Client-side Supabase client with enhanced configuration
export const supabase = createClient<Database>(
  env.NEXT_PUBLIC_SUPABASE_URL,
  env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: 'pkce',
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'medical-crm@1.0.0',
      },
    },
  }
)

// Server-side Supabase client (for API routes) with enhanced error handling
export const createServerClient = () => {
  try {
    const client = createClient<Database>(
      env.NEXT_PUBLIC_SUPABASE_URL,
      serverEnv.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        global: {
          headers: {
            'X-Client-Info': 'medical-crm-server@1.0.0',
          },
        },
      }
    )

    // Add error logging for database operations
    // Temporarily disabled to avoid circular dependency
    // const originalFrom = client.from.bind(client)
    // client.from = (table: string) => {
    //   const query = originalFrom(table)
    //   const dbLogger = logger.child({ table, service: 'database' })

    //   // Log database operations in development
    //   if (process.env.NODE_ENV === 'development') {
    //     dbLogger.debug(`Database operation on table: ${table}`)
    //   }

    //   return query
    // }

    return client
  } catch (error) {
    console.error('Failed to create Supabase server client', error)
    throw new DatabaseError('数据库连接失败')
  }
}

export type SupabaseClient = typeof supabase
