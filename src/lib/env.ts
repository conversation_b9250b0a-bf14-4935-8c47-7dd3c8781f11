import { z } from 'zod';

/**
 * Client-side environment variable validation schema
 * Only includes NEXT_PUBLIC_ variables that are safe to expose to the client
 */
const clientEnvSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Supabase configuration (client-side)
  NEXT_PUBLIC_SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),

  // Clerk authentication (client-side)
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1, 'Clerk publishable key is required'),

  // Sentry monitoring (optional)
  NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),
  NEXT_PUBLIC_SENTRY_ORG: z.string().optional(),
  NEXT_PUBLIC_SENTRY_PROJECT: z.string().optional(),
  NEXT_PUBLIC_SENTRY_DISABLED: z.string().optional(),

  // Application configuration
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),

  // File upload
  NEXT_PUBLIC_MAX_FILE_SIZE: z.string().transform(Number).default('5242880'), // 5MB

  // Feature flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(Boolean).default('true'),
  NEXT_PUBLIC_ENABLE_NOTIFICATIONS: z.string().transform(Boolean).default('true'),
});

/**
 * Server-side environment variable validation schema
 * Includes all environment variables, including server-only secrets
 */
const serverEnvSchema = z.object({
  // Include all client-side variables
  ...clientEnvSchema.shape,

  // Server-only variables
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Supabase service role key is required'),
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key is required'),

  // Security
  NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters').optional(),

  // Database encryption key (for sensitive data)
  DATABASE_ENCRYPTION_KEY: z.string().min(32, 'Database encryption key must be at least 32 characters').optional(),

  // Rate limiting
  REDIS_URL: z.string().url().optional(),

  // Email service (optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
});

/**
 * Determine if we're running on the server or client
 */
const isServer = typeof window === 'undefined';

/**
 * Validated environment variables for client-side use
 * Only includes NEXT_PUBLIC_ variables that are safe to expose
 */
export const clientEnv = (() => {
  try {
    return clientEnvSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError && typeof window !== 'undefined') {
      // On client side, provide safe fallbacks to prevent crashes
      console.warn('⚠️ Client environment validation failed, using fallbacks:', error.errors);
      return {
        NODE_ENV: (process.env.NODE_ENV as any) || 'development',
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '',
        NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
        NEXT_PUBLIC_SENTRY_ORG: process.env.NEXT_PUBLIC_SENTRY_ORG,
        NEXT_PUBLIC_SENTRY_PROJECT: process.env.NEXT_PUBLIC_SENTRY_PROJECT,
        NEXT_PUBLIC_SENTRY_DISABLED: process.env.NEXT_PUBLIC_SENTRY_DISABLED,
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        NEXT_PUBLIC_MAX_FILE_SIZE: Number(process.env.NEXT_PUBLIC_MAX_FILE_SIZE) || 5242880,
        NEXT_PUBLIC_ENABLE_ANALYTICS: Boolean(process.env.NEXT_PUBLIC_ENABLE_ANALYTICS !== 'false'),
        NEXT_PUBLIC_ENABLE_NOTIFICATIONS: Boolean(process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS !== 'false'),
      } as z.infer<typeof clientEnvSchema>;
    }
    // On server side, throw the error
    throw error;
  }
})();

/**
 * Validated environment variables for server-side use
 * Includes all environment variables, including server-only secrets
 * Only available on the server side
 */
export const serverEnv = (() => {
  if (isServer) {
    try {
      return serverEnvSchema.parse(process.env);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('❌ Server environment validation failed:', error.errors);
        throw error;
      } else {
        throw error;
      }
    }
  } else {
    return {} as z.infer<typeof serverEnvSchema>;
  }
})();

/**
 * Main environment export - uses client env for safety
 * For server-only variables, use serverEnv directly
 */
export const env = clientEnv;

// Remove the old serverEnv definition since it's now defined above

/**
 * Utility function to check if we're in development mode
 */
export const isDevelopment = env.NODE_ENV === 'development';

/**
 * Utility function to check if we're in production mode
 */
export const isProduction = env.NODE_ENV === 'production';

/**
 * Utility function to check if we're in test mode
 */
export const isTest = env.NODE_ENV === 'test';

/**
 * Environment variables are validated when accessed through clientEnv and serverEnv
 * This prevents client-side crashes when server-only variables are not available
 */

export type ClientEnv = z.infer<typeof clientEnvSchema>;
export type ServerEnv = z.infer<typeof serverEnvSchema>;
export type Env = ClientEnv;
