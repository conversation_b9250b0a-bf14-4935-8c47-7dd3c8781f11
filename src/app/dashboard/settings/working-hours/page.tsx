'use client'


import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Clock, Save, RotateCcw } from 'lucide-react'
import { 
  WorkingHours, 
  DEFAULT_WORKING_HOURS, 
  DAY_NAMES,
  getWorkingHours,
  saveWorkingHours,
  validateTimeFormat,
  compareTime
} from '@/lib/working-hours'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import PageContainer from '@/components/layout/page-container'

export default function WorkingHoursPage() {
  const [workingHours, setWorkingHours] = useState<WorkingHours[]>(DEFAULT_WORKING_HOURS)
  const [loading, setLoading] = useState(false)
  const [] = useState<Record<string, string>>({})

  useEffect(() => {
    const savedHours = getWorkingHours()
    setWorkingHours(savedHours)
  }, [getWorkingHours])

  // 更新工作日状态
  const updateWorkingDay = (dayIndex: number, isWorking: boolean) => {
    setWorkingHours(prev => prev.map(wh => 
      wh.day === dayIndex ? { ...wh, isWorking } : wh
    ))
  }

  // 更新时间
  const updateTime = (dayIndex: number, field: keyof WorkingHours, value: string) => {
    setWorkingHours(prev => prev.map(wh => 
      wh.day === dayIndex ? { ...wh, [field]: value } : wh
    ))
    
    // 清除相关错误
    const errorKey = `${dayIndex}_${field}`
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: '' }))
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    workingHours.forEach(wh => {
      if (!wh.isWorking) return

      // 验证时间格式
      if (!validateTimeFormat(wh.startTime)) {
        newErrors[`${wh.day}_startTime`] = '请输入有效的时间格式 (HH:mm)'
      }
      if (!validateTimeFormat(wh.endTime)) {
        newErrors[`${wh.day}_endTime`] = '请输入有效的时间格式 (HH:mm)'
      }

      // 验证开始时间小于结束时间
      if (validateTimeFormat(wh.startTime) && validateTimeFormat(wh.endTime)) {
        if (compareTime(wh.startTime, wh.endTime) >= 0) {
          newErrors[`${wh.day}_endTime`] = '结束时间必须晚于开始时间'
        }
      }

      // 验证午休时间
      if (wh.breakStart && wh.breakEnd) {
        if (!validateTimeFormat(wh.breakStart)) {
          newErrors[`${wh.day}_breakStart`] = '请输入有效的时间格式 (HH:mm)'
        }
        if (!validateTimeFormat(wh.breakEnd)) {
          newErrors[`${wh.day}_breakEnd`] = '请输入有效的时间格式 (HH:mm)'
        }

        if (validateTimeFormat(wh.breakStart) && validateTimeFormat(wh.breakEnd)) {
          if (compareTime(wh.breakStart, wh.breakEnd) >= 0) {
            newErrors[`${wh.day}_breakEnd`] = '午休结束时间必须晚于开始时间'
          }

          // 验证午休时间在工作时间内
          if (validateTimeFormat(wh.startTime) && validateTimeFormat(wh.endTime)) {
            if (compareTime(wh.breakStart, wh.startTime) < 0 || compareTime(wh.breakStart, wh.endTime) >= 0) {
              newErrors[`${wh.day}_breakStart`] = '午休时间必须在工作时间内'
            }
            if (compareTime(wh.breakEnd, wh.startTime) <= 0 || compareTime(wh.breakEnd, wh.endTime) > 0) {
              newErrors[`${wh.day}_breakEnd`] = '午休时间必须在工作时间内'
            }
          }
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 保存设置
  const handleSave = async () => {
    if (!validateForm()) {
      showErrorToast('请检查输入信息', '请修正标红的字段后重试')
      return
    }

    setLoading(true)
    try {
      saveWorkingHours(workingHours)
      showSuccessToast('工作时间设置已保存', '新的工作时间配置将立即生效')
    } catch (error) {
      showErrorToast('保存失败', '请稍后重试或联系管理员')
    } finally {
      setLoading(false)
    }
  }

  // 重置为默认设置
  const handleReset = () => {
    setWorkingHours(DEFAULT_WORKING_HOURS)
    setErrors({})
    showSuccessToast('已重置为默认设置')
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">工作时间设置</h1>
          <p className="text-muted-foreground">
            配置诊所的营业时间，非工作时间将在日历中显示为不可用
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleReset} className="btn-hover-scale">
            <RotateCcw className="mr-2 h-4 w-4" />
            重置默认
          </Button>
          <Button onClick={handleSave} disabled={loading} className="btn-hover-scale">
            <Save className="mr-2 h-4 w-4" />
            {loading ? '保存中...' : '保存设置'}
          </Button>
        </div>
      </div>

      <div className="grid gap-4">
        {workingHours.map((wh) => (
          <Card key={wh.day} className="card-hover">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  {DAY_NAMES[wh.day]}
                </span>
                <Switch
                  checked={wh.isWorking}
                  onCheckedChange={(checked) => updateWorkingDay(wh.day, checked)}
                />
              </CardTitle>
            </CardHeader>
            {wh.isWorking && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>开始时间</Label>
                    <Input
                      type="time"
                      value={wh.startTime}
                      onChange={(e) => updateTime(wh.day, 'startTime', e.target.value)}
                      className={errors[`${wh.day}_startTime`] ? 'border-red-500' : ''}
                    />
                    {errors[`${wh.day}_startTime`] && (
                      <p className="text-sm text-red-500">{errors[`${wh.day}_startTime`]}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>结束时间</Label>
                    <Input
                      type="time"
                      value={wh.endTime}
                      onChange={(e) => updateTime(wh.day, 'endTime', e.target.value)}
                      className={errors[`${wh.day}_endTime`] ? 'border-red-500' : ''}
                    />
                    {errors[`${wh.day}_endTime`] && (
                      <p className="text-sm text-red-500">{errors[`${wh.day}_endTime`]}</p>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>午休开始 (可选)</Label>
                    <Input
                      type="time"
                      value={wh.breakStart ?? ''}
                      onChange={(e) => updateTime(wh.day, 'breakStart', e.target.value)}
                      className={errors[`${wh.day}_breakStart`] ? 'border-red-500' : ''}
                    />
                    {errors[`${wh.day}_breakStart`] && (
                      <p className="text-sm text-red-500">{errors[`${wh.day}_breakStart`]}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>午休结束 (可选)</Label>
                    <Input
                      type="time"
                      value={wh.breakEnd ?? ''}
                      onChange={(e) => updateTime(wh.day, 'breakEnd', e.target.value)}
                      className={errors[`${wh.day}_breakEnd`] ? 'border-red-500' : ''}
                    />
                    {errors[`${wh.day}_breakEnd`] && (
                      <p className="text-sm text-red-500">{errors[`${wh.day}_breakEnd`]}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
      </div>
    </PageContainer>
  )
}
