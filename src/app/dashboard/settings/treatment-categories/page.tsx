'use client'


import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { logger } from '@/lib/logger';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Plus, Edit, Trash2, Stethoscope } from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import PageContainer from '@/components/layout/page-container'

interface TreatmentCategory {
  id: string
  name: string
  description?: string
  color?: string
  order: number
  created_at: string
}

// 默认分类
const DEFAULT_CATEGORIES: TreatmentCategory[] = []

export default function TreatmentCategoriesPage() {
  const [categories, setCategories] = useState<TreatmentCategory[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<TreatmentCategory | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3b82f6'
  })
  const [loading, setLoading] = useState(false)

  // 加载分类
  useEffect(() => {
    loadCategories()
  }, [loadCategories])

  const loadCategories = () => {
    try {
      const saved = typeof window !== "undefined" && localStorage.getItem('treatment_categories')
      if (saved) {
        setCategories(JSON.parse(saved))
      } else {
        setCategories(DEFAULT_CATEGORIES)
        typeof window !== "undefined" && localStorage.setItem('treatment_categories', JSON.stringify(DEFAULT_CATEGORIES))
      }
    } catch (error) {
      logger.error('Error loading categories:', error as Error)
      setCategories(DEFAULT_CATEGORIES)
    }
  }

  const saveCategories = (newCategories: TreatmentCategory[]) => {
    try {
      typeof window !== "undefined" && localStorage.setItem('treatment_categories', JSON.stringify(newCategories))
      setCategories(newCategories)
    } catch (error) {
      logger.error('Error saving categories:', error as Error)
      showErrorToast('保存失败', '请稍后重试')
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      showErrorToast('请输入分类名称')
      return
    }

    // 检查名称是否重复
    const isDuplicate = categories.some(cat => 
      cat.name === formData.name.trim() && cat.id !== editingCategory?.id
    )
    
    if (isDuplicate) {
      showErrorToast('分类名称已存在')
      return
    }

    setLoading(true)

    try {
      if (editingCategory) {
        // 更新分类
        const updatedCategories = categories.map(cat =>
          cat.id === editingCategory.id
            ? { ...cat, ...formData, name: formData.name.trim() }
            : cat
        )
        saveCategories(updatedCategories)
        showSuccessToast('分类更新成功')
      } else {
        // 新建分类
        const newCategory: TreatmentCategory = {
          id: `custom_${Date.now()}`,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          order: categories.length + 1,
          created_at: new Date().toISOString()
        }
        saveCategories([...categories, newCategory])
        showSuccessToast('分类创建成功')
      }

      handleCloseDialog()
    } catch (error) {
      showErrorToast('操作失败', '请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (category: TreatmentCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description ?? '',
      color: category.color ?? '#3b82f6'
    })
    setIsDialogOpen(true)
  }

  const handleDelete = (categoryId: string) => {
    if (!confirm('确定要删除这个分类吗？删除后无法恢复。')) {
      return
    }

    const updatedCategories = categories.filter(cat => cat.id !== categoryId)
    saveCategories(updatedCategories)
    showSuccessToast('分类删除成功')
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingCategory(null)
    setFormData({
      name: '',
      description: '',
      color: '#3b82f6'
    })
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">治疗项目分类管理</h1>
          <p className="text-muted-foreground">
            管理治疗项目的分类，便于组织和查找项目
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="btn-hover-scale">
              <Plus className="mr-2 h-4 w-4" />
              新建分类
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingCategory ? '编辑分类' : '新建分类'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label>分类名称 *</Label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入分类名称"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label>描述</Label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入分类描述（可选）"
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label>颜色</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.color}
                    onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                    placeholder="#3b82f6"
                    className="flex-1"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  取消
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? '保存中...' : '保存'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card className="card-hover">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5" />
            分类列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="table-container">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>颜色</TableHead>
                <TableHead>名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell>
                    <div 
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: category.color }}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{category.name}</TableCell>
                  <TableCell className="text-muted-foreground">
                    {category.description ?? '无描述'}
                  </TableCell>
                  <TableCell>
                    {new Date(category.created_at).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
      })}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      {category.id.startsWith('custom_') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(category.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        </CardContent>
      </Card>
      </div>
    </PageContainer>
  )
}
