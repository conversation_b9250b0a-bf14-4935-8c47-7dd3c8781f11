'use client'


import { useEffect, useState } from 'react';
import { Calendar, dateFnsLocalizer, View } from 'react-big-calendar'
import { format, parse, startOfWeek, getDay } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { logger } from '@/lib/logger';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Plus, Calendar as CalendarIcon, Clock } from 'lucide-react'
import AppointmentModal from '@/components/modals/AppointmentModal'
import { getStatusColor, getStatusText, statusColors } from '@/lib/colors'
import PageContainer from '@/components/layout/page-container'
import PageHeader from '@/components/layout/page-header'

// Setup the localizer for react-big-calendar
const locales = {
  'zh-CN': zhCN,
}

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
})

// Define appointment event type
interface AppointmentEvent {
  id: string
  title: string
  start: Date
  end: Date
  resource: {
    id: string
    client_name: string
    treatment_name: string
    status: string
    appointment_type: string
    phone: string
  }
}

export default function CalendarPage() {
  const [events, setEvents] = useState<AppointmentEvent[]>([])
  const [selectedEvent, setSelectedEvent] = useState<AppointmentEvent | null>(null)
  const [view, setView] = useState<View>('month')
  const [date, setDate] = useState(new Date())
  const [loading, setLoading] = useState(true)

  // Modal states
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false)
  const [selectedSlotTime, setSelectedSlotTime] = useState<{ start: Date; end: Date } | null>(null)
  const [editingAppointment, setEditingAppointment] = useState<any>(null)

  // Fetch appointments for the current view
  const fetchAppointments = async () => {
    try {
      setLoading(true)

      // Calculate date range based on current view
      let startDate: Date
      let endDate: Date

      if (view === 'month') {
        // Get first day of current month
        startDate = new Date(date.getFullYear(), date.getMonth(), 1)
        // Get last day of current month
        endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      } else if (view === 'week') {
        // Get start of week (Sunday)
        const dayOfWeek = date.getDay()
        startDate = new Date(date)
        startDate.setDate(date.getDate() - dayOfWeek)
        startDate.setHours(0, 0, 0, 0)

        // Get end of week (Saturday)
        endDate = new Date(startDate)
        endDate.setDate(startDate.getDate() + 6)
        endDate.setHours(23, 59, 59, 999)
      } else {
        // Day view
        startDate = new Date(date)
        startDate.setHours(0, 0, 0, 0)
        endDate = new Date(date)
        endDate.setHours(23, 59, 59, 999)
      }

      const response = await fetch(
        `/api/appointments?start_date=${startDate.toISOString().split('T')[0]}&end_date=${endDate.toISOString().split('T')[0]}`
      )
      
      if (!response.ok) throw new Error('Failed to fetch appointments')
      
      const data = await response.json()

      // Transform appointments to calendar events
      const appointmentsData = Array.isArray(data.appointments) ? data.appointments : []
      const calendarEvents: AppointmentEvent[] = appointmentsData.map((apt: Record<string, unknown>) => {
        const client = apt.clients as Record<string, unknown>;
        const treatment = apt.treatments as Record<string, unknown>;
        return {
          id: apt.id as string,
          title: `${client.last_name}${client.first_name} - ${treatment.name_chinese}`,
          start: new Date(`${apt.appointment_date}T${apt.start_time}`),
          end: new Date(`${apt.appointment_date}T${apt.end_time}`),
          resource: {
            id: apt.id as string,
            client_name: `${client.last_name}${client.first_name}`,
            treatment_name: treatment.name_chinese as string,
            status: apt.status as string,
            appointment_type: apt.appointment_type as string,
            phone: client.phone as string
          }
        };
      });

      setEvents(calendarEvents)
    } catch (error) {
      logger.error('Error fetching appointments:', error as Error)
      // Set empty array on error to prevent undefined issues
      setEvents([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAppointments()
  }, [])

  // Event style getter for different appointment types and statuses
  const eventStyleGetter = (event: AppointmentEvent) => {
    let backgroundColor = statusColors.appointment.scheduled.hex

    // 优先检查是否为咨询类型
    if (event.resource.appointment_type === 'consultation') {
      backgroundColor = statusColors.appointment.consultation.hex
    } else {
      // 根据状态设置颜色
      const statusColor = statusColors.appointment[event.resource.status as keyof typeof statusColors.appointment]
      if (statusColor) {
        backgroundColor = statusColor.hex
      }
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '6px',
        opacity: 0.9,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '12px',
        fontWeight: '500'
      }
    }
  }

  const handleSelectEvent = (event: AppointmentEvent) => {
    setSelectedEvent(event)
    // Open edit modal for existing appointment
    setEditingAppointment(event.resource)
    setIsAppointmentModalOpen(true)
  }

  const handleSelectSlot = ({ start, end }: { start: Date; end: Date }) => {
    // Open appointment creation modal with selected time
    setSelectedSlotTime({ start, end })
    setEditingAppointment(null)
    setIsAppointmentModalOpen(true)
  }

  const handleNewAppointment = () => {
    setSelectedSlotTime(null)
    setEditingAppointment(null)
    setIsAppointmentModalOpen(true)
  }

  const handleAppointmentSuccess = () => {
    // Refresh appointments after successful creation/update
    fetchAppointments()
  }

  const getStatusBadgeColor = (status: string) => {
    const colors = getStatusColor('appointment', status)
    return `${colors.bg} ${colors.text} ${colors.border} border`
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        {/* Header */}
        <PageHeader
          title="预约日历"
          description="管理客户预约和治疗安排"
          action={{
            label: "新建预约",
            onClick: handleNewAppointment,
            icon: Plus
          }}
        />

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 lg:gap-6">
          {/* Calendar */}
          <Card className="xl:col-span-3 shadow-sm border-0 bg-card">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                <CalendarIcon className="h-5 w-5 text-primary" />
                预约日历
              </CardTitle>
              <div className="flex gap-1 w-full sm:w-auto">
                <Button
                  variant={view === 'month' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('month')}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  月视图
                </Button>
                <Button
                  variant={view === 'week' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('week')}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  周视图
                </Button>
                <Button
                  variant={view === 'day' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('day')}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  日视图
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex items-center justify-center h-[400px] sm:h-[500px] lg:h-[600px]">
                <div className="flex flex-col items-center gap-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <p className="text-sm text-muted-foreground">加载预约数据...</p>
                </div>
              </div>
            ) : (
              <div className="calendar-container h-[400px] sm:h-[500px] lg:h-[600px] overflow-hidden rounded-b-lg">
                <Calendar
                  localizer={localizer}
                  events={events}
                  startAccessor="start"
                  endAccessor="end"
                  style={{ height: '100%' }}
                  view={view}
                  onView={setView}
                  date={date}
                  onNavigate={setDate}
                  onSelectEvent={handleSelectEvent}
                  onSelectSlot={handleSelectSlot}
                  selectable
                  eventPropGetter={eventStyleGetter}
                  culture="zh-CN"
                  step={30}
                  timeslots={2}
                  min={new Date(2024, 0, 1, 8, 0, 0)}
                  max={new Date(2024, 0, 1, 20, 0, 0)}
                  formats={{
                    timeGutterFormat: 'HH:mm',
                    eventTimeRangeFormat: ({ start, end }) =>
                      `${format(start, 'HH:mm')} - ${format(end, 'HH:mm')}`,
                    dayFormat: 'MM/dd',
                    dayHeaderFormat: 'MM月dd日 dddd',
                    monthHeaderFormat: 'yyyy年MM月',
                    dayRangeHeaderFormat: ({ start, end }) =>
                      `${format(start, 'MM月dd日')} - ${format(end, 'MM月dd日')}`
                  }}
                  messages={{
                    next: '下一个',
                    previous: '上一个',
                    today: '今天',
                    month: '月',
                    week: '周',
                    day: '日',
                    agenda: '议程',
                    date: '日期',
                    time: '时间',
                    event: '事件',
                    noEventsInRange: '此时间范围内没有预约',
                    showMore: (total) => `+${total} 更多`,
                    allDay: '全天',
                    work_week: '工作日',
                    yesterday: '昨天',
                    tomorrow: '明天'
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Event Details Sidebar */}
        <Card className="shadow-sm border-0">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold">
              <Clock className="h-5 w-5 text-primary" />
              预约详情
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedEvent ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{selectedEvent.resource.client_name}</h3>
                  <p className="text-sm text-muted-foreground">{selectedEvent.resource.phone}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">治疗项目</p>
                  <p className="text-sm text-muted-foreground">{selectedEvent.resource.treatment_name}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">时间</p>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedEvent.start, 'yyyy年MM月dd日 HH:mm')} - {format(selectedEvent.end, 'HH:mm')}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">状态</p>
                  <Badge className={getStatusBadgeColor(selectedEvent.resource.status)}>
                    {getStatusText('appointment', selectedEvent.resource.status)}
                  </Badge>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleSelectEvent(selectedEvent)}
                  >
                    编辑预约
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setSelectedEvent(null)}
                  >
                    关闭
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                  <Clock className="h-8 w-8 text-muted-foreground" />
                </div>
                <p className="text-muted-foreground text-sm">点击日历中的预约查看详情</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Appointment Modal */}
      <AppointmentModal
        isOpen={isAppointmentModalOpen}
        onClose={() => setIsAppointmentModalOpen(false)}
        onSuccess={handleAppointmentSuccess}
        selectedTime={selectedSlotTime}
        editingAppointment={editingAppointment}
      />
      </div>
    </PageContainer>
  )
}
