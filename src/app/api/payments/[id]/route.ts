import { NextRequest, NextResponse } from 'next/server'
import { paymentQueries, businessLogic } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/payments/[id] - Get payment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const payment = await paymentQueries.getById(id)
    return NextResponse.json({ payment })
  } catch (error) {
    logger.error('Error fetching payment:', error as Error)
    return NextResponse.json(
      { error: '付款记录不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/payments/[id] - Update payment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()

    // Get current payment to track invoice_id for status update
    const currentPayment = await paymentQueries.getById(id)
    
    // Prepare update data
    const updateData: unknown = {}
    
    if (body.amount !== undefined) updateData.amount = body.amount
    if (body.payment_date) updateData.payment_date = body.payment_date
    if (body.payment_method) updateData.payment_method = body.payment_method
    if (body.payment_type) updateData.payment_type = body.payment_type
    if (body.reference_number !== undefined) updateData.reference_number = body.reference_number
    if (body.notes !== undefined) updateData.notes = body.notes

    const payment = await paymentQueries.update(id, updateData)
    
    // Update invoice status if payment amount changed
    if (body.amount !== undefined) {
      await businessLogic.updateInvoiceStatus(currentPayment.invoice_id)
    }
    
    return NextResponse.json({ payment })
  } catch (error) {
    logger.error('Error updating payment:', error as Error)
    return NextResponse.json(
      { error: '更新付款记录失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/payments/[id] - Delete payment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    // Get payment info before deletion for invoice status update
    const payment = await paymentQueries.getById(id)
    const invoiceId = payment.invoice_id

    await paymentQueries.delete(id)

    // Update invoice status after payment deletion
    await businessLogic.updateInvoiceStatus(invoiceId)

    return NextResponse.json({ message: '付款记录已删除' })
  } catch (error) {
    logger.error('Error deleting payment:', error as Error)
    return NextResponse.json(
      { error: '删除付款记录失败' },
      { status: 500 }
    )
  }
}
