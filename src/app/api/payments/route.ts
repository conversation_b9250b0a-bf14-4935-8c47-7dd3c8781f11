import { NextRequest, NextResponse } from 'next/server'
import { paymentQueries, businessLogic } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/payments - Get all payments or by invoice/client
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const invoiceId = searchParams.get('invoice_id')
    const clientId = searchParams.get('client_id')

    let payments
    if (invoiceId) {
      payments = await paymentQueries.getByInvoiceId(invoiceId)
    } else if (clientId) {
      payments = await paymentQueries.getByClientId(clientId)
    } else {
      payments = await paymentQueries.getAll()
    }

    return NextResponse.json({ payments })
  } catch (error) {
    logger.error('Error fetching payments:', error as Error)
    return NextResponse.json(
      { error: '获取付款记录失败' },
      { status: 500 }
    )
  }
}

// POST /api/payments - Create new payment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.invoice_id || !body.client_id || !body.amount || !body.payment_method || !body.payment_type) {
      return NextResponse.json(
        { error: '缺少必填字段：账单ID、客户ID、金额、付款方式、付款类型' },
        { status: 400 }
      )
    }

    const paymentData = {
      invoice_id: body.invoice_id,
      client_id: body.client_id,
      amount: body.amount,
      payment_date: body.payment_date ?? new Date().toISOString().split('T')[0],
      payment_method: body.payment_method,
      payment_type: body.payment_type,
      reference_number: body.reference_number ?? null,
      notes: body.notes ?? null
    }

    const payment = await paymentQueries.create(paymentData)
    
    // Update invoice status based on new payment
    await businessLogic.updateInvoiceStatus(body.invoice_id)
    
    return NextResponse.json({ payment }, { status: 201 })
  } catch (error) {
    logger.error('Error creating payment:', error as Error)
    return NextResponse.json(
      { error: '创建付款记录失败' },
      { status: 500 }
    )
  }
}
