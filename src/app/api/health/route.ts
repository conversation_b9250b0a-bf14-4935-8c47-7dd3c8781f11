import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase/client'

/**
 * Health Check Endpoint
 * Provides comprehensive health status for monitoring and load balancers
 */

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  version: string
  uptime: number
  checks: {
    database: {
      status: 'healthy' | 'unhealthy'
      responseTime?: number
      error?: string
    }
    memory: {
      status: 'healthy' | 'degraded'
      usage: number
      limit: number
    }
    environment: {
      status: 'healthy' | 'unhealthy'
      nodeEnv: string
      requiredVars: boolean
    }
  }
}

// Track application start time
const startTime = Date.now()

async function checkDatabase(): Promise<HealthStatus['checks']['database']> {
  try {
    const start = Date.now()
    
    // Simple query to test database connectivity
    const { data, error } = await supabase
      .from('treatments')
      .select('id')
      .limit(1)
    
    const responseTime = Date.now() - start
    
    if (error) {
      return {
        status: 'unhealthy',
        responseTime,
        error: error.message
      }
    }
    
    return {
      status: 'healthy',
      responseTime
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? (error as Error).message : 'Unknown database error'
    }
  }
}

function checkMemory(): HealthStatus['checks']['memory'] {
  const usage = process.memoryUsage()
  const totalMemory = usage.heapUsed + usage.external
  const memoryLimit = 512 * 1024 * 1024 // 512MB limit
  
  return {
    status: totalMemory > memoryLimit * 0.9 ? 'degraded' : 'healthy',
    usage: totalMemory,
    limit: memoryLimit
  }
}

function checkEnvironment(): HealthStatus['checks']['environment'] {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  return {
    status: missingVars.length === 0 ? 'healthy' : 'unhealthy',
    nodeEnv: process.env.NODE_ENV ?? 'unknown',
    requiredVars: missingVars.length === 0
  }
}

export async function GET(request: NextRequest) {
  try {
    const uptime = Date.now() - startTime
    
    // Run all health checks
    const [database, memory, environment] = await Promise.all([
      checkDatabase(),
      Promise.resolve(checkMemory()),
      Promise.resolve(checkEnvironment())
    ])
    
    // Determine overall status
    let overallStatus: HealthStatus['status'] = 'healthy'
    
    if (database.status === 'unhealthy' || environment.status === 'unhealthy') {
      overallStatus = 'unhealthy'
    } else if (memory.status === 'degraded') {
      overallStatus = 'degraded'
    }
    
    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version ?? '1.0.0',
      uptime,
      checks: {
        database,
        memory,
        environment
      }
    }
    
    // Return appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503
    
    return NextResponse.json(healthStatus, { status: httpStatus })
    
  } catch (error) {
    const errorResponse: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version ?? '1.0.0',
      uptime: Date.now() - startTime,
      checks: {
        database: {
          status: 'unhealthy',
          error: 'Health check failed'
        },
        memory: {
          status: 'healthy',
          usage: 0,
          limit: 0
        },
        environment: {
          status: 'unhealthy',
          nodeEnv: process.env.NODE_ENV ?? 'unknown',
          requiredVars: false
        }
      }
    }
    
    return NextResponse.json(errorResponse, { status: 503 })
  }
}

// Simple health check for load balancers
export async function HEAD(request: NextRequest) {
  try {
    // Quick database ping
    const { error } = await supabase
      .from('treatments')
      .select('id')
      .limit(1)
    
    if (error) {
      return new NextResponse(null, { status: 503 })
    }
    
    return new NextResponse(null, { status: 200 })
  } catch {
    return new NextResponse(null, { status: 503 })
  }
}
