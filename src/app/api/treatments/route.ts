import { NextRequest, NextResponse } from 'next/server'
import { treatmentQueries } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/treatments - Get all treatments or active only
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const activeOnly = searchParams.get('active_only') === 'true'

    let treatments
    if (activeOnly) {
      treatments = await treatmentQueries.getActive()
    } else {
      treatments = await treatmentQueries.getAll()
    }

    return NextResponse.json({ treatments })
  } catch (error) {
    logger.error('Error fetching treatments:', error as Error)
    return NextResponse.json(
      { error: '获取治疗项目失败' },
      { status: 500 }
    )
  }
}

// POST /api/treatments - Create new treatment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.name_chinese || !body.default_price || !body.duration_minutes || !body.category) {
      return NextResponse.json(
        { error: '治疗名称、价格、时长和类别为必填项' },
        { status: 400 }
      )
    }

    const treatment = await treatmentQueries.create({
      name: body.name,
      name_chinese: body.name_chinese,
      description: body.description ?? null,
      description_chinese: body.description_chinese ?? null,
      default_price: parseFloat(body.default_price),
      fixed_deposit_amount: body.fixed_deposit_amount ? parseFloat(body.fixed_deposit_amount) : 0,
      consultation_fee: body.consultation_fee ? parseFloat(body.consultation_fee) : 0,
      duration_minutes: parseInt(body.duration_minutes),
      category: body.category,
      is_active: body.is_active !== false,
      requires_consultation: body.requires_consultation ?? false
    })

    return NextResponse.json({ treatment }, { status: 201 })
  } catch (error) {
    logger.error('Error creating treatment:', error as Error)
    return NextResponse.json(
      { error: '创建治疗项目失败' },
      { status: 500 }
    )
  }
}
