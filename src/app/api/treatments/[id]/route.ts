import { NextRequest, NextResponse } from 'next/server'
import { treatmentQueries } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/treatments/[id] - Get treatment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const treatment = await treatmentQueries.getById(id)
    return NextResponse.json({ treatment })
  } catch (error) {
    logger.error('Error fetching treatment:', error as Error)
    return NextResponse.json(
      { error: '治疗项目不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/treatments/[id] - Update treatment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()

    const updateData: unknown = {
      name: body.name,
      name_chinese: body.name_chinese,
      description: body.description ?? null,
      description_chinese: body.description_chinese ?? null,
      category: body.category,
      is_active: body.is_active,
      requires_consultation: body.requires_consultation
    }

    // Only include numeric fields if they have valid values
    if (body.default_price !== undefined && body.default_price !== null) {
      updateData.default_price = parseFloat(body.default_price)
    }
    if (body.fixed_deposit_amount !== undefined && body.fixed_deposit_amount !== null) {
      updateData.fixed_deposit_amount = parseFloat(body.fixed_deposit_amount)
    }
    if (body.consultation_fee !== undefined && body.consultation_fee !== null) {
      updateData.consultation_fee = parseFloat(body.consultation_fee)
    }
    if (body.duration_minutes !== undefined && body.duration_minutes !== null) {
      updateData.duration_minutes = parseInt(body.duration_minutes)
    }

    const treatment = await treatmentQueries.update(id, updateData)

    return NextResponse.json({ treatment })
  } catch (error) {
    logger.error('Error updating treatment:', error as Error)
    return NextResponse.json(
      { error: '更新治疗项目失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/treatments/[id] - Delete treatment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    await treatmentQueries.delete(id)
    return NextResponse.json({ message: '治疗项目删除成功' })
  } catch (error) {
    logger.error('Error deleting treatment:', error as Error)
    return NextResponse.json(
      { error: '删除治疗项目失败' },
      { status: 500 }
    )
  }
}
