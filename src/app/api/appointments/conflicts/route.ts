import { NextRequest, NextResponse } from 'next/server'
import { businessLogic } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { appointment_date, start_time, end_time, exclude_appointment_id } = body

    if (!appointment_date || !start_time || !end_time) {
      return NextResponse.json(
        { error: '缺少必要的时间参数' },
        { status: 400 }
      )
    }

    const conflicts = await businessLogic.getConflictingAppointments(
      appointment_date,
      start_time,
      end_time,
      exclude_appointment_id
    )

    return NextResponse.json({ conflicts })
  } catch (error) {
    logger.error('Error checking appointment conflicts:', error as Error)
    return NextResponse.json(
      { error: '检查冲突失败' },
      { status: 500 }
    )
  }
}
