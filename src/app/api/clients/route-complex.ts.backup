import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/client'

// Define query schema for GET requests
const getQuerySchema = z.object({
  q: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
})

// Enhanced client creation schema with address fields
const createClientSchema = z.object({
  first_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
  last_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
  phone: z.string().regex(/^\d{10}$/, '请输入10位数字的电话号码（如：1234567890）'),
  email: z.string().email('无效的邮箱格式').optional().or(z.literal('')),
  date_of_birth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须为 YYYY-MM-DD').optional().or(z.literal('')),
  address_line_1: z.string().max(200, '地址过长').optional(),
  address_line_2: z.string().max(200, '地址过长').optional(),
  city: z.string().max(100, '城市名过长').optional(),
  state_province: z.string().max(100, '省份名过长').optional(),
  postal_code: z.string().max(20, '邮编过长').optional(),
  country: z.string().max(100, '国家名过长').optional(),
  latitude: z.number().optional().nullable(),
  longitude: z.number().optional().nullable(),
  emergency_contact_name: z.string().max(100, '紧急联系人姓名过长').optional(),
  emergency_contact_phone: z.string().regex(/^\d{10}$/, '请输入10位数字的电话号码（如：1234567890）').optional().or(z.literal('')),
  referral_source: z.string().max(100, '推荐来源过长').optional(),
  notes: z.string().max(1000, '备注过长').optional(),
  preferred_language: z.string().max(10, '语言代码过长').optional(),
  status: z.enum(['active', 'inactive', 'archived']).optional(),
})

// Create API handler with validation
const handler = createApiHandler({
  querySchema: getQuerySchema,
  bodySchema: createClientSchema,
})({
  GET: async (request, { query }) => {
    const { q, page = 1, limit = 10 } = query ?? {}
    const serverQueries = getServerClientQueries()

    let clients
    if (q) {
      clients = await serverQueries.search(q)
    } else {
      clients = await serverQueries.getAll()
    }

    // TODO: Implement proper pagination at database level
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedClients = clients.slice(startIndex, endIndex)

    return createSuccessResponse({
      clients: paginatedClients,
      pagination: {
        page,
        limit,
        total: clients.length,
        totalPages: Math.ceil(clients.length / limit),
      },
    })
  },

  POST: async (request, { body }) => {
    const clientData = body!
    const serverQueries = getServerClientQueries()

    const client = await serverQueries.create({
      first_name: clientData.first_name,
      last_name: clientData.last_name,
      phone: clientData.phone,
      email: clientData.email && clientData.email.trim() !== '' ? clientData.email : null,
      date_of_birth: clientData.date_of_birth && clientData.date_of_birth.trim() !== '' ? clientData.date_of_birth : null,
      address_line_1: clientData.address_line_1 && clientData.address_line_1.trim() !== '' ? clientData.address_line_1 : null,
      address_line_2: clientData.address_line_2 && clientData.address_line_2.trim() !== '' ? clientData.address_line_2 : null,
      city: clientData.city && clientData.city.trim() !== '' ? clientData.city : null,
      state_province: clientData.state_province && clientData.state_province.trim() !== '' ? clientData.state_province : null,
      postal_code: clientData.postal_code && clientData.postal_code.trim() !== '' ? clientData.postal_code : null,
      country: clientData.country && clientData.country.trim() !== '' ? clientData.country : '美国',
      latitude: clientData.latitude ?? null,
      longitude: clientData.longitude ?? null,
      emergency_contact_name: clientData.emergency_contact_name && clientData.emergency_contact_name.trim() !== '' ? clientData.emergency_contact_name : null,
      emergency_contact_phone: clientData.emergency_contact_phone && clientData.emergency_contact_phone.trim() !== '' ? clientData.emergency_contact_phone : null,
      notes: clientData.notes && clientData.notes.trim() !== '' ? clientData.notes : null,
      referral_source: clientData.referral_source && clientData.referral_source.trim() !== '' ? clientData.referral_source : null,
      preferred_language: clientData.preferred_language ?? 'zh-CN',
      status: clientData.status ?? 'active'
    })

    return createSuccessResponse({ client }, 201, '客户创建成功')
  },
})

// Export the handler functions
export const GET = handler
export const POST = handler
