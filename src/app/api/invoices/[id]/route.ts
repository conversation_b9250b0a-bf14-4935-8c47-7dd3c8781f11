import { NextRequest, NextResponse } from 'next/server'
import { invoiceQueries, businessLogic } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/invoices/[id] - Get invoice by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const invoice = await invoiceQueries.getById(id)
    return NextResponse.json({ invoice })
  } catch (error) {
    logger.error('Error fetching invoice:', error as Error)
    return NextResponse.json(
      { error: '账单不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/invoices/[id] - Update invoice
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()
    
    // Prepare update data
    const updateData: unknown = {}
    
    if (body.client_id) updateData.client_id = body.client_id
    if (body.invoice_number) updateData.invoice_number = body.invoice_number
    if (body.invoice_date) updateData.invoice_date = body.invoice_date
    if (body.treatment_date) updateData.treatment_date = body.treatment_date
    if (body.total_amount !== undefined) {
      updateData.total_amount = body.total_amount
      // Recalculate deposit if total amount changes
      if (body.deposit_percentage !== undefined) {
        updateData.deposit_amount = businessLogic.calculateDeposit(body.total_amount, body.deposit_percentage)
        updateData.deposit_percentage = body.deposit_percentage
      }
    }
    if (body.deposit_amount !== undefined) updateData.deposit_amount = body.deposit_amount
    if (body.deposit_percentage !== undefined) updateData.deposit_percentage = body.deposit_percentage
    if (body.consultation_fee_waived !== undefined) updateData.consultation_fee_waived = body.consultation_fee_waived
    if (body.original_consultation_fee !== undefined) updateData.original_consultation_fee = body.original_consultation_fee
    if (body.status) updateData.status = body.status
    if (body.due_date) updateData.due_date = body.due_date
    if (body.notes !== undefined) updateData.notes = body.notes

    const invoice = await invoiceQueries.update(id, updateData)
    return NextResponse.json({ invoice })
  } catch (error) {
    logger.error('Error updating invoice:', error as Error)
    return NextResponse.json(
      { error: '更新账单失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/invoices/[id] - Delete invoice
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    await invoiceQueries.delete(id)
    return NextResponse.json({ message: '账单已删除' })
  } catch (error) {
    logger.error('Error deleting invoice:', error as Error)
    return NextResponse.json(
      { error: '删除账单失败' },
      { status: 500 }
    )
  }
}
