import { NextRequest, NextResponse } from 'next/server'
import { invoiceQueries, businessLogic } from '@/lib/supabase/queries'
import { logger } from '@/lib/logger';

// GET /api/invoices - Get all invoices or by client
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('client_id')

    let invoices
    if (clientId) {
      invoices = await invoiceQueries.getByClientId(clientId)
    } else {
      invoices = await invoiceQueries.getAll()
    }

    return NextResponse.json({ invoices })
  } catch (error) {
    logger.error('Error fetching invoices:', error as Error)
    return NextResponse.json(
      { error: '获取账单失败' },
      { status: 500 }
    )
  }
}

// POST /api/invoices - Create new invoice
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.client_id || !body.treatment_date || !body.total_amount) {
      return NextResponse.json(
        { error: '缺少必填字段：客户ID、治疗日期、总金额' },
        { status: 400 }
      )
    }

    // Generate invoice number if not provided
    const invoiceNumber = body.invoice_number ?? businessLogic.generateInvoiceNumber()
    
    // Calculate deposit - use fixed amount if appointments provided, otherwise fallback to percentage
    let depositAmount = body.deposit_amount
    if (!depositAmount) {
      if (body.appointments && body.appointments.length > 0) {
        // Use new fixed deposit calculation
        depositAmount = await businessLogic.calculateFixedDeposit(body.appointments)
      } else {
        // Fallback to percentage-based calculation for backward compatibility
        depositAmount = businessLogic.calculateDeposit(body.total_amount, body.deposit_percentage ?? 50)
      }
    }

    // Set due date if not provided (7 days from now)
    const dueDate = body.due_date ?? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const invoiceData = {
      client_id: body.client_id,
      invoice_number: invoiceNumber,
      invoice_date: body.invoice_date ?? new Date().toISOString().split('T')[0],
      treatment_date: body.treatment_date,
      total_amount: body.total_amount,
      deposit_amount: depositAmount,
      deposit_percentage: body.deposit_percentage ?? 50,
      consultation_fee_waived: body.consultation_fee_waived ?? false,
      original_consultation_fee: body.original_consultation_fee ?? 0,
      status: body.status ?? 'draft',
      due_date: dueDate,
      notes: body.notes ?? null
    }

    const invoice = await invoiceQueries.create(invoiceData)
    return NextResponse.json({ invoice }, { status: 201 })
  } catch (error) {
    logger.error('Error creating invoice:', error as Error)
    return NextResponse.json(
      { error: '创建账单失败' },
      { status: 500 }
    )
  }
}
