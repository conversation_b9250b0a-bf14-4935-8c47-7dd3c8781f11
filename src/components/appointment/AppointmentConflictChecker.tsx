'use client'


import { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Clock, User, Calendar } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { logger } from '@/lib/logger';

interface ConflictingAppointment {
  id: string
  client_name: string
  treatment_name: string
  start_time: string
  end_time: string
  status: string
}

interface AppointmentConflictCheckerProps {
  appointmentDate: string
  startTime: string
  endTime: string
  excludeAppointmentId?: string
  onConflictDetected: (hasConflict: boolean, conflicts: ConflictingAppointment[]) => void
}

export default function AppointmentConflictChecker({
  appointmentDate,
  startTime,
  endTime,
  excludeAppointmentId,
  onConflictDetected
}: AppointmentConflictCheckerProps) {
  const [conflicts, setConflicts] = useState<ConflictingAppointment[]>([])
  const [isChecking, setIsChecking] = useState(false)
  const [lastChecked, setLastChecked] = useState<string>('')

  const checkForConflicts = async () => {
    if (!appointmentDate || !startTime || !endTime) {
      setConflicts([])
      onConflictDetected(false, [])
      return
    }

    const checkKey = `${appointmentDate}-${startTime}-${endTime}-${excludeAppointmentId ?? ''}`
    if (checkKey === lastChecked) return

    setIsChecking(true)
    try {
      const response = await fetch('/api/appointments/conflicts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointment_date: appointmentDate,
          start_time: startTime,
          end_time: endTime,
          exclude_appointment_id: excludeAppointmentId
        }),
      })

      if (!response.ok) {
        throw new Error('检查冲突失败')
      }

      const data = await response.json()
      const conflictingAppointments = data.conflicts ?? []
      
      setConflicts(conflictingAppointments)
      setLastChecked(checkKey)
      onConflictDetected(conflictingAppointments.length > 0, conflictingAppointments)
    } catch (error) {
      logger.error('Error checking conflicts:', error as Error)
      setConflicts([])
      onConflictDetected(false, [])
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkForConflicts()
    }, 500) // 防抖，500ms后检查

    return () => clearTimeout(timeoutId)
  }, [appointmentDate, startTime, endTime, excludeAppointmentId])

  if (isChecking) {
    return (
      <Alert className="border-blue-200 bg-blue-50">
        <Clock className="h-4 w-4 animate-spin" />
        <AlertDescription>
          正在检查时间冲突...
        </AlertDescription>
      </Alert>
    )
  }

  if (conflicts.length === 0) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <Calendar className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          ✅ 该时间段可用，无冲突预约
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Alert className="border-red-200 bg-red-50">
      <AlertTriangle className="h-4 w-4 text-red-600" />
      <AlertDescription>
        <div className="space-y-3">
          <p className="font-medium text-red-800">
            ⚠️ 检测到 {conflicts.length} 个时间冲突
          </p>
          
          <div className="space-y-2">
            {conflicts.map((conflict) => (
              <div
                key={conflict.id}
                className="p-3 bg-white rounded-lg border border-red-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="font-medium">{conflict.client_name}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {conflict.status === 'scheduled' && '已预约'}
                    {conflict.status === 'confirmed' && '已确认'}
                    {conflict.status === 'in_progress' && '进行中'}
                    {conflict.status === 'completed' && '已完成'}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {conflict.treatment_name}
                </p>
                
                <div className="flex items-center gap-2 mt-2 text-sm text-red-600">
                  <Clock className="h-3 w-3" />
                  <span>
                    {conflict.start_time} - {conflict.end_time}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          <div className="pt-2 border-t border-red-200">
            <p className="text-sm text-red-700">
              💡 建议：请选择其他时间段，或联系相关客户调整预约时间
            </p>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}

// 创建冲突检测API端点
export const createConflictCheckAPI = `
// src/app/api/appointments/conflicts/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { businessLogic } from '@/lib/supabase/queries'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { appointment_date, start_time, end_time, exclude_appointment_id } = body

    if (!appointment_date || !start_time || !end_time) {
      return NextResponse.json(
        { error: '缺少必要的时间参数' },
        { status: 400 }
      )
    }

    const conflicts = await businessLogic.getConflictingAppointments(
      appointment_date,
      start_time,
      end_time,
      exclude_appointment_id
    )

    return NextResponse.json({ conflicts })
  } catch (error) {
    logger.error('Error checking appointment conflicts:', error as Error)
    return NextResponse.json(
      { error: '检查冲突失败' },
      { status: 500 }
    )
  }
}
`
