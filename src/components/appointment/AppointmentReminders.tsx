'use client'


import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { logger } from '@/lib/logger';
import {
  Bell, 
  Clock, 
  User, 
  Phone, 
  Calendar,
  CheckCircle,
  AlertTriangle,
  X
} from 'lucide-react'
import { format, isToday, isTomorrow, addDays, parseISO } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface UpcomingAppointment {
  id: string
  client_name: string
  client_phone: string
  treatment_name: string
  appointment_date: string
  start_time: string
  end_time: string
  status: string
  appointment_type: string
}

interface AppointmentRemindersProps {
  onMarkAsReminded?: (appointmentId: string) => void
}

export default function AppointmentReminders({ onMarkAsReminded }: AppointmentRemindersProps) {
  const [upcomingAppointments, setUpcomingAppointments] = useState<UpcomingAppointment[]>([])
  const [loading, setLoading] = useState(true)
  const [dismissedReminders, setDismissedReminders] = useState<Set<string>>(new Set())

  const fetchUpcomingAppointments = async () => {
    try {
      setLoading(true)
      const today = new Date()
      const threeDaysLater = addDays(today, 3)
      
      const response = await fetch(
        `/api/appointments?start_date=${today.toISOString().split('T')[0]}&end_date=${threeDaysLater.toISOString().split('T')[0]}&status=scheduled,confirmed`
      )
      
      if (!response.ok) throw new Error('Failed to fetch appointments')
      
      const data = await response.json()
      const appointments = data.appointments ?? []
      
      // Filter and format appointments
      const upcoming = appointments
        .filter((apt: unknown) => {
          const appointmentDate = parseISO(apt.appointment_date)
          return appointmentDate >= today && ['scheduled', 'confirmed'].includes(apt.status)
        })
        .map((apt: unknown) => ({
          id: apt.id,
          client_name: `${apt.clients.last_name}${apt.clients.first_name}`,
          client_phone: apt.clients.phone,
          treatment_name: apt.treatments.name_chinese,
          appointment_date: apt.appointment_date,
          start_time: apt.start_time,
          end_time: apt.end_time,
          status: apt.status,
          appointment_type: apt.appointment_type
        }))
        .sort((a: UpcomingAppointment, b: UpcomingAppointment) => {
          const dateA = new Date(`${a.appointment_date}T${a.start_time}`)
          const dateB = new Date(`${b.appointment_date}T${b.start_time}`)
          return dateA.getTime() - dateB.getTime()
        })
      
      setUpcomingAppointments(upcoming)
    } catch (error) {
      logger.error('Error fetching upcoming appointments:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUpcomingAppointments()
    // Refresh every 30 minutes
    const interval = setInterval(fetchUpcomingAppointments, 30 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const getAppointmentUrgency = (appointment: UpcomingAppointment) => {
    const appointmentDate = parseISO(appointment.appointment_date)
    
    if (isToday(appointmentDate)) {
      return { level: 'urgent', label: '今天', color: 'bg-red-100 text-red-800 border-red-200' }
    } else if (isTomorrow(appointmentDate)) {
      return { level: 'warning', label: '明天', color: 'bg-orange-100 text-orange-800 border-orange-200' }
    } else {
      return { level: 'normal', label: format(appointmentDate, 'MM月dd日', { locale: zhCN }), color: 'bg-blue-100 text-blue-800 border-blue-200' }
    }
  }

  const handleDismissReminder = (appointmentId: string) => {
    setDismissedReminders(prev => new Set([...prev, appointmentId]))
  }

  const handleMarkAsReminded = (appointmentId: string) => {
    if (onMarkAsReminded) {
      onMarkAsReminded(appointmentId)
    }
    handleDismissReminder(appointmentId)
  }

  const visibleAppointments = upcomingAppointments.filter(
    apt => !dismissedReminders.has(apt.id)
  )

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            预约提醒
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Clock className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (visibleAppointments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            预约提醒
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              ✅ 近期无需要提醒的预约
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          预约提醒
          <Badge variant="secondary" className="ml-2">
            {visibleAppointments.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {visibleAppointments.map((appointment) => {
            const urgency = getAppointmentUrgency(appointment)
            
            return (
              <div
                key={appointment.id}
                className="p-4 border rounded-lg bg-card hover:bg-muted/30 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    {/* Urgency Badge */}
                    <div className="flex items-center gap-2">
                      <Badge className={urgency.color}>
                        {urgency.level === 'urgent' && <AlertTriangle className="w-3 h-3 mr-1" />}
                        {urgency.level === 'warning' && <Clock className="w-3 h-3 mr-1" />}
                        {urgency.level === 'normal' && <Calendar className="w-3 h-3 mr-1" />}
                        {urgency.label}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {appointment.appointment_type === 'consultation' ? '咨询' : '治疗'}
                      </Badge>
                    </div>
                    
                    {/* Client Info */}
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{appointment.client_name}</span>
                      <Phone className="h-3 w-3 text-muted-foreground ml-2" />
                      <span className="text-sm text-muted-foreground">{appointment.client_phone}</span>
                    </div>
                    
                    {/* Treatment and Time */}
                    <div className="text-sm text-muted-foreground">
                      <p>{appointment.treatment_name}</p>
                      <p className="flex items-center gap-1 mt-1">
                        <Clock className="h-3 w-3" />
                        {appointment.start_time} - {appointment.end_time}
                      </p>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center gap-1 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleMarkAsReminded(appointment.id)}
                      className="text-xs"
                    >
                      已提醒
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDismissReminder(appointment.id)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {/* Quick Actions */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                visibleAppointments.forEach(apt => handleMarkAsReminded(apt.id))
              }}
              className="text-xs"
            >
              全部标记为已提醒
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={fetchUpcomingAppointments}
              className="text-xs"
            >
              刷新
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
