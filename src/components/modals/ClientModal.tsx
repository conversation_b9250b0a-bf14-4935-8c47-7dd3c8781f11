'use client'











import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertCircle, Calendar, Eye, FileText, History, Mail, MapPin, Phone, User, UserPlus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { showErrorToast, showLoadingToast, showSuccessToast } from '@/lib/toast-utils';
import { EnhancedInput, EnhancedSelect, EnhancedTextarea } from '@/components/ui/enhanced-input';
import { COMMON_RULES, ValidationRule, validateField, validateForm } from '@/lib/form-validation';
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/client/ClientHistory';
import { logger } from '@/lib/logger';

import AddressInput, { AddressData, formatAddress, isAddressEmpty } from '@/components/ui/address-input'






interface Client {
  id: string
  first_name: string
  last_name: string
  phone: string
  email: string | null
  date_of_birth: string | null
  address_line_1: string | null
  address_line_2: string | null
  city: string | null
  state_province: string | null
  postal_code: string | null
  country: string | null
  latitude: number | null
  longitude: number | null
  emergency_contact_name: string | null
  emergency_contact_phone: string | null
  notes: string | null
  status: 'active' | 'inactive' | 'archived'
  referral_source: string | null
  preferred_language: string
  created_at: string
  updated_at: string
}

interface ClientModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  editingClient?: Client | null
  mode?: 'view' | 'edit' | 'create'
}

export default function ClientModal({
  isOpen,
  onClose,
  onSuccess,
  editingClient,
  mode = 'create'
}: ClientModalProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    date_of_birth: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: '美国',
    latitude: null as number | null,
    longitude: null as number | null,
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: '',
    status: 'active' as 'active' | 'inactive' | 'archived',
    referral_source: '',
    preferred_language: 'zh-CN'
  })

  // 验证规则
  const validationRules: Record<string, ValidationRule> = {
    first_name: COMMON_RULES.firstName,
    last_name: COMMON_RULES.lastName,
    phone: COMMON_RULES.phoneStrict,
    email: { ...COMMON_RULES.email, required: false },
    date_of_birth: { required: false },
    address_line_1: COMMON_RULES.addressLine,
    city: COMMON_RULES.city,
    state_province: COMMON_RULES.state,
    postal_code: { ...COMMON_RULES.postalCode, required: false },
    country: COMMON_RULES.country,
    emergency_contact_name: COMMON_RULES.shortText,
    emergency_contact_phone: { ...COMMON_RULES.phone, required: false },
    notes: COMMON_RULES.notes,
    referral_source: COMMON_RULES.shortText
  }

  // 字段更新函数
  const updateField = (field: string, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // 如果字段已被触摸，立即验证
    if (touched[field]) {
      const rule = validationRules[field]
      if (rule) {
        const error = validateField(value, rule)
        setErrors(prev => ({ ...prev, [field]: error ?? '' }))
      }
    }
  }

  // 字段失焦处理
  const handleFieldBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))

    const rule = validationRules[field]
    if (rule && field in formData) {
      const error = validateField(formData[field as keyof typeof formData], rule)
      setErrors(prev => ({ ...prev, [field]: error ?? '' }))
    }
  }

  useEffect(() => {
    if (editingClient) {
      setFormData({
        first_name: editingClient.first_name,
        last_name: editingClient.last_name,
        phone: editingClient.phone,
        email: editingClient.email ?? '',
        date_of_birth: editingClient.date_of_birth ?? '',
        address_line_1: editingClient.address_line_1 ?? '',
        address_line_2: editingClient.address_line_2 ?? '',
        city: editingClient.city ?? '',
        state_province: editingClient.state_province ?? '',
        postal_code: editingClient.postal_code ?? '',
        country: editingClient.country ?? '美国',
        latitude: editingClient.latitude,
        longitude: editingClient.longitude,
        emergency_contact_name: editingClient.emergency_contact_name ?? '',
        emergency_contact_phone: editingClient.emergency_contact_phone ?? '',
        notes: editingClient.notes ?? '',
        status: editingClient.status,
        referral_source: editingClient.referral_source ?? '',
        preferred_language: editingClient.preferred_language
      })
    } else {
      setFormData({
        first_name: '',
        last_name: '',
        phone: '',
        email: '',
        date_of_birth: '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        state_province: '',
        postal_code: '',
        country: '美国',
        latitude: null,
        longitude: null,
        emergency_contact_name: '',
        emergency_contact_phone: '',
        notes: '',
        status: 'active',
        referral_source: '',
        preferred_language: 'zh-CN'
      })
    }
  }, [editingClient, isOpen])

  // 表单验证
  const validateFormData = () => {
    const result = validateForm(formData, validationRules)
    setErrors(result.errors)
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}))
    return result.isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (mode === 'view') return

    if (!validateFormData()) {
      showErrorToast('请检查表单信息', '请修正标红的字段后重试')
      return
    }

    setLoading(true)
    const toastId = showLoadingToast(editingClient ? '正在更新客户信息...' : '正在创建客户...')

    try {
      const url = editingClient
        ? `/api/clients/${editingClient.id}`
        : '/api/clients'

      const method = editingClient ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error ?? '操作失败')
      }

      showSuccessToast(
        editingClient ? '客户信息更新成功' : '客户创建成功',
        `${formData.last_name}${formData.first_name}的信息已保存`
      )
      onSuccess()
      handleClose()
    } catch (error: unknown) {
      logger.error('Error saving client:', error as Error)
      showErrorToast(
        editingClient ? '更新客户失败' : '创建客户失败',
        (error as Error).message ?? '请稍后重试或联系管理员'
      )
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onClose()
    setErrors({})
    setFormData({
      first_name: '',
      last_name: '',
      phone: '',
      email: '',
      date_of_birth: '',
      address_line_1: '',
      address_line_2: '',
      city: '',
      state_province: '',
      postal_code: '',
      country: '美国',
      latitude: null,
      longitude: null,
      emergency_contact_name: '',
      emergency_contact_phone: '',
      notes: '',
      status: 'active',
      referral_source: '',
      preferred_language: 'zh-CN'
    })
  }

  const isReadOnly = mode === 'view'

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl w-[85vw] max-h-[90vh] overflow-y-auto dialog-content-enhanced">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <User className="h-6 w-6" />
            {mode === 'view' ? '查看客户信息' :
             mode === 'edit' ? '编辑客户信息' : '新建客户'}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2" disabled={!editingClient}>
              <History className="h-4 w-4" />
              历史记录
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Basic Info */}
              <div className="space-y-3">
                <h3 className="text-base font-semibold text-gray-800 border-b border-gray-200 pb-2">基本信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <EnhancedInput
                label="姓"
                icon={<User className="h-4 w-4" />}
                placeholder="客户姓氏"
                value={formData.last_name}
                onChange={(value) => updateField('last_name', value)}
                onBlur={() => handleFieldBlur('last_name')}
                error={errors.last_name}
                touched={touched.last_name}
                required
                disabled={isReadOnly}
                validation={validationRules.last_name}
              />

              <EnhancedInput
                label="名"
                placeholder="客户名字"
                value={formData.first_name}
                onChange={(value) => updateField('first_name', value)}
                onBlur={() => handleFieldBlur('first_name')}
                error={errors.first_name}
                touched={touched.first_name}
                required
                disabled={isReadOnly}
                validation={validationRules.first_name}
              />

              <EnhancedSelect
                label="客户状态"
                value={formData.status}
                onChange={(value) => updateField('status', value)}
                onBlur={() => handleFieldBlur('status')}
                error={errors.status}
                touched={touched.status}
                disabled={isReadOnly}
                options={[
                  { value: 'active', label: '活跃' },
                  { value: 'inactive', label: '非活跃' },
                  { value: 'archived', label: '已归档' }
                ]}
              />
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-3">
            <h3 className="text-base font-semibold text-gray-800 border-b border-gray-200 pb-2">联系信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <EnhancedInput
                label="电话号码"
                icon={<Phone className="h-4 w-4" />}
                type="tel"
                placeholder="1234567890"
                value={formData.phone}
                onChange={(value) => updateField('phone', value)}
                onBlur={() => handleFieldBlur('phone')}
                error={errors.phone}
                touched={touched.phone}
                required
                disabled={isReadOnly}
                validation={validationRules.phone}
                formatter="phone"
                helpText="请输入10位数字的电话号码"
              />

              <EnhancedInput
                label="邮箱"
                icon={<Mail className="h-4 w-4" />}
                type="email"
                placeholder="客户邮箱地址"
                value={formData.email}
                onChange={(value) => updateField('email', value)}
                onBlur={() => handleFieldBlur('email')}
                error={errors.email}
                touched={touched.email}
                disabled={isReadOnly}
                validation={validationRules.email}
              />

              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  出生日期
                </Label>
                <Input
                  type="date"
                  value={formData.date_of_birth}
                  onChange={(e) => updateField('date_of_birth', e.target.value)}
                  onBlur={() => handleFieldBlur('date_of_birth')}
                  disabled={isReadOnly}
                  className={cn(
                    "transition-colors",
                    touched.date_of_birth && errors.date_of_birth && "border-red-500 focus:border-red-500"
                  )}
                />
                {touched.date_of_birth && errors.date_of_birth && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.date_of_birth}
                  </p>
                )}
              </div>
            </div>
          </div>



          {/* Address Info */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-800 border-b border-gray-200 pb-2">地址信息</h3>
            <AddressInput
              value={{
                address_line_1: formData.address_line_1,
                address_line_2: formData.address_line_2,
                city: formData.city,
                state_province: formData.state_province,
                postal_code: formData.postal_code,
                country: formData.country,
                latitude: formData.latitude ?? undefined,
                longitude: formData.longitude ?? undefined
              }}
              onChange={(address) => {
                setFormData(prev => ({
                  ...prev,
                  address_line_1: address.address_line_1,
                  address_line_2: address.address_line_2,
                  city: address.city,
                  state_province: address.state_province,
                  postal_code: address.postal_code,
                  country: address.country,
                  latitude: address.latitude ?? null,
                  longitude: address.longitude ?? null
                }))
              }}
              disabled={isReadOnly}
            />
          </div>

          {/* Additional Info */}
          <div className="space-y-3">
            <h3 className="text-base font-semibold text-gray-800 border-b border-gray-200 pb-2">其他信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <EnhancedInput
                label="紧急联系人"
                icon={<UserPlus className="h-4 w-4" />}
                placeholder="紧急联系人姓名"
                value={formData.emergency_contact_name}
                onChange={(value) => updateField('emergency_contact_name', value)}
                onBlur={() => handleFieldBlur('emergency_contact_name')}
                error={errors.emergency_contact_name}
                touched={touched.emergency_contact_name}
                disabled={isReadOnly}
                validation={validationRules.emergency_contact_name}
              />

              <EnhancedInput
                label="紧急联系电话"
                type="tel"
                placeholder="1234567890"
                value={formData.emergency_contact_phone}
                onChange={(value) => updateField('emergency_contact_phone', value)}
                onBlur={() => handleFieldBlur('emergency_contact_phone')}
                error={errors.emergency_contact_phone}
                touched={touched.emergency_contact_phone}
                disabled={isReadOnly}
                validation={validationRules.emergency_contact_phone}
                formatter="phone"
              />
            </div>

            <EnhancedSelect
              label="推荐来源"
              placeholder="选择推荐来源"
              value={formData.referral_source}
              onChange={(value) => updateField('referral_source', value)}
              onBlur={() => handleFieldBlur('referral_source')}
              error={errors.referral_source}
              touched={touched.referral_source}
              disabled={isReadOnly}
              validation={validationRules.referral_source}
              options={[
                { value: '网上搜索', label: '网上搜索' },
                { value: '朋友推荐', label: '朋友推荐' },
                { value: '社交媒体', label: '社交媒体' },
                { value: '广告', label: '广告' },
                { value: '其他', label: '其他' }
              ]}
            />

            <EnhancedTextarea
              label="备注"
              icon={<FileText className="h-4 w-4" />}
              placeholder="客户相关备注信息..."
              value={formData.notes}
              onChange={(value) => updateField('notes', value)}
              onBlur={() => handleFieldBlur('notes')}
              error={errors.notes}
              touched={touched.notes}
              disabled={isReadOnly}
              validation={validationRules.notes}
              rows={2}
              maxLength={1000}
            />
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-end gap-3 pt-5 action-button-area -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="w-full sm:w-auto min-w-[120px]"
            >
              {mode === 'view' ? '关闭' : '取消'}
            </Button>
            {mode !== 'view' && (
              <Button
                type="submit"
                disabled={loading}
                className="w-full sm:w-auto min-w-[120px]"
              >
                {loading ? '保存中...' : (editingClient ? '更新客户' : '创建客户')}
              </Button>
            )}
              </div>
            </form>
          </TabsContent>

          <TabsContent value="history">
            {editingClient && (
              <ClientHistory clientId={editingClient.id} />
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
