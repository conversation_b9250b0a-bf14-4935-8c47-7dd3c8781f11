'use client'


import { useEffect } from 'react';
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Clock, AlertCircle, Info } from 'lucide-react'
import { 
  isWorkingDay, 
  getAvailableTimeSlots, 
  validateAppointmentTime,
  getWorkingHoursForDate,
  formatWorkingHours
} from '@/lib/working-hours'

interface SmartTimePickerProps {
  selectedDate: string // YYYY-MM-DD format
  startTime: string
  endTime: string
  onStartTimeChange: (time: string) => void
  onEndTimeChange: (time: string) => void
  treatmentDuration?: number // 治疗时长（分钟）
  disabled?: boolean
  className?: string
}

export default function SmartTimePicker({
  selectedDate,
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  treatmentDuration = 60,
  disabled = false,
  className = ''
}: SmartTimePickerProps) {
  const [availableStartTimes, setAvailableStartTimes] = useState<string[]>([])
  const [availableEndTimes, setAvailableEndTimes] = useState<string[]>([])
  const [validation, setValidation] = useState<{ isValid: boolean; error?: string }>({ isValid: true })
  const [workingHoursInfo, setWorkingHoursInfo] = useState<string>('')

  // 更新可用时间段
  useEffect(() => {
    if (!selectedDate) {
      setAvailableStartTimes([])
      setAvailableEndTimes([])
      setWorkingHoursInfo('')
      return
    }

    const date = new Date(selectedDate)
    
    // 检查是否为工作日
    if (!isWorkingDay(date)) {
      setAvailableStartTimes([])
      setAvailableEndTimes([])
      setWorkingHoursInfo('选择的日期不是工作日')
      return
    }

    // 获取工作时间信息
    const workingHours = getWorkingHoursForDate(date)
    if (workingHours) {
      setWorkingHoursInfo(formatWorkingHours(workingHours))
    }

    // 获取可用的开始时间段
    const startSlots = getAvailableTimeSlots(date, 30) // 30分钟间隔
    setAvailableStartTimes(startSlots)

    // 如果有选择的开始时间，计算可用的结束时间
    if (startTime && startSlots.includes(startTime)) {
      const startMinutes = timeToMinutes(startTime)
      const minEndMinutes = startMinutes + treatmentDuration
      const endSlots = getAvailableTimeSlots(date, 30)
        .filter(time => timeToMinutes(time) >= minEndMinutes)
      setAvailableEndTimes(endSlots)
    } else {
      setAvailableEndTimes([])
    }
  }, [selectedDate, startTime, treatmentDuration])

  // 验证选择的时间
  useEffect(() => {
    if (!selectedDate || !startTime || !endTime) {
      setValidation({ isValid: true })
      return
    }

    const date = new Date(selectedDate)
    const result = validateAppointmentTime(date, startTime, endTime)
    setValidation(result)
  }, [selectedDate, startTime, endTime])

  // 处理开始时间变化
  const handleStartTimeChange = (time: string) => {
    onStartTimeChange(time)
    
    // 自动计算建议的结束时间
    if (time && treatmentDuration) {
      const startMinutes = timeToMinutes(time)
      const suggestedEndMinutes = startMinutes + treatmentDuration
      const suggestedEndTime = minutesToTime(suggestedEndMinutes)
      
      // 检查建议的结束时间是否可用
      const date = new Date(selectedDate)
      const endSlots = getAvailableTimeSlots(date, 30)
      
      if (endSlots.some(slot => timeToMinutes(slot) >= suggestedEndMinutes)) {
        onEndTimeChange(suggestedEndTime)
      }
    }
  }

  // 时间转换工具函数
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  const minutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 工作时间信息 */}
      {workingHoursInfo && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {workingHoursInfo === '选择的日期不是工作日' ? (
              <span className="text-orange-600">{workingHoursInfo}</span>
            ) : (
              <span>工作时间: {workingHoursInfo}</span>
            )}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-2 gap-4">
        {/* 开始时间 */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            开始时间
          </Label>
          <Select
            value={startTime}
            onValueChange={handleStartTimeChange}
            disabled={disabled ?? availableStartTimes.length === 0}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择开始时间" />
            </SelectTrigger>
            <SelectContent>
              {availableStartTimes.map((time) => (
                <SelectItem key={time} value={time}>
                  {time}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 结束时间 */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            结束时间
          </Label>
          <Select
            value={endTime}
            onValueChange={onEndTimeChange}
            disabled={disabled || !startTime || availableEndTimes.length === 0}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择结束时间" />
            </SelectTrigger>
            <SelectContent>
              {availableEndTimes.map((time) => (
                <SelectItem key={time} value={time}>
                  {time}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 验证错误提示 */}
      {!validation.isValid && validation.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {validation.error}
          </AlertDescription>
        </Alert>
      )}

      {/* 时长提示 */}
      {startTime && endTime && validation.isValid && (
        <div className="text-sm text-muted-foreground">
          预约时长: {Math.round((timeToMinutes(endTime) - timeToMinutes(startTime)))} 分钟
        </div>
      )}
    </div>
  )
}
