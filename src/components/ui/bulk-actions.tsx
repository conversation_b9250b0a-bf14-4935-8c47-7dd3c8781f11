'use client'


import React, { Fragment, useState } from 'react';
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { logger } from '@/lib/logger';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  ChevronDown, 
  Trash2, 
  Edit, 
  Send, 
  Download, 
  Archive,
  CheckCircle,
  XCircle,
  Calendar,
  DollarSign,
  Users
} from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface BulkAction {
  id: string
  label: string
  icon: React.ElementType
  variant?: 'default' | 'destructive' | 'outline'
  requiresConfirmation?: boolean
  confirmationTitle?: string
  confirmationDescription?: string
}

interface BulkActionsProps {
  selectedItems: string[]
  totalItems: number
  onSelectAll: (selected: boolean) => void
  onClearSelection: () => void
  actions: BulkAction[]
  onAction: (actionId: string, selectedItems: string[]) => Promise<void>
  itemType?: string // e.g., "客户", "预约", "账单"
}

export function BulkActions({
  selectedItems,
  totalItems,
  onSelectAll,
  onClearSelection,
  actions,
  onAction,
  itemType = "项目"
}: BulkActionsProps) {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
  const [pendingAction, setPendingAction] = useState<BulkAction | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const isAllSelected = selectedItems.length === totalItems && totalItems > 0
  const isIndeterminate = selectedItems.length > 0 && selectedItems.length < totalItems

  const handleSelectAll = (checked: boolean) => {
    onSelectAll(checked)
  }

  const handleActionClick = (action: BulkAction) => {
    if (action.requiresConfirmation) {
      setPendingAction(action)
      setIsConfirmDialogOpen(true)
    } else {
      executeAction(action)
    }
  }

  const executeAction = async (action: BulkAction) => {
    try {
      setIsProcessing(true)
      await onAction(action.id, selectedItems)
      
      showSuccessToast(
        '操作成功',
        `已对 ${selectedItems.length} 个${itemType}执行"${action.label}"操作`
      )
      
      onClearSelection()
    } catch (error: unknown) {
      logger.error('Bulk action error:', error as Error)
      showErrorToast('操作失败', (error as Error).message ?? '批量操作执行失败')
    } finally {
      setIsProcessing(false)
      setIsConfirmDialogOpen(false)
      setPendingAction(null)
    }
  }

  const handleConfirmAction = () => {
    if (pendingAction) {
      executeAction(pendingAction)
    }
  }

  if (selectedItems.length === 0) {
    return (
      <div className="flex items-center gap-2">
        <Checkbox
          checked={isAllSelected}
          onCheckedChange={handleSelectAll}
          aria-label="全选"
        />
        <span className="text-sm text-muted-foreground">
          {totalItems} 个{itemType}
        </span>
      </div>
    )
  }

  return (
    <>
      <div className="flex items-center gap-4 p-3 bg-muted/30 rounded-lg border">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={isIndeterminate ? 'indeterminate' : isAllSelected}
            onCheckedChange={handleSelectAll}
            aria-label="全选"
          />
          <Badge variant="secondary" className="font-medium">
            已选择 {selectedItems.length} 个{itemType}
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          {/* Quick Actions */}
          {actions.slice(0, 3).map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.id}
                variant={action.variant ?? "outline"}
                size="sm"
                onClick={() => handleActionClick(action)}
                disabled={isProcessing}
                className="h-8"
              >
                <Icon className="mr-1 h-3 w-3" />
                {action.label}
              </Button>
            )
          })}

          {/* More Actions Dropdown */}
          {actions.length > 3 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  更多操作
                  <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {actions.slice(3).map((action) => {
                  const Icon = action.icon
                  return (
                    <DropdownMenuItem
                      key={action.id}
                      onClick={() => handleActionClick(action)}
                      disabled={isProcessing}
                      className={action.variant === 'destructive' ? 'text-destructive focus:text-destructive' : ''}
                    >
                      <Icon className="mr-2 h-4 w-4" />
                      {action.label}
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <DropdownMenuSeparator />

          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="h-8 text-muted-foreground hover:text-foreground"
          >
            取消选择
          </Button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {pendingAction?.confirmationTitle ?? `确认${pendingAction?.label}`}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingAction?.confirmationDescription ?? `您确定要对选中的 ${selectedItems.length} 个${itemType}执行"${pendingAction?.label}"操作吗？`
              }
              {pendingAction?.variant === 'destructive' && (
                <div className="mt-2 p-2 bg-destructive/10 rounded-md">
                  <p className="text-sm text-destructive font-medium">
                    ⚠️ 此操作无法撤销，请谨慎操作。
                  </p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAction}
              disabled={isProcessing}
              className={pendingAction?.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
            >
              {isProcessing ? '处理中...' : '确认'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

// Predefined bulk actions for different entity types
export const clientBulkActions: BulkAction[] = []

export const appointmentBulkActions: BulkAction[] = []

export const invoiceBulkActions: BulkAction[] = []

// Hook for managing bulk selection
export function useBulkSelection<T extends { id: string }>(items: T[]) {
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const selectItem = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const selectAll = (selected: boolean) => {
    setSelectedItems(selected ? items.map(item => item.id) : [])
  }

  const clearSelection = () => {
    setSelectedItems([])
  }

  const isSelected = (id: string) => selectedItems.includes(id)

  return {
    selectedItems,
    selectItem,
    selectAll,
    clearSelection,
    isSelected,
    selectedCount: selectedItems.length,
    isAllSelected: selectedItems.length === items.length && items.length > 0,
    isIndeterminate: selectedItems.length > 0 && selectedItems.length < items.length
  }
}
