import React, { Fragment } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MobileTableProps {
  data: unknown[];
  renderCard: (item: unknown, index: number) => React.ReactNode;
  className?: string;
}

export function MobileTable({ data, renderCard, className }: MobileTableProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {data.map((item, index) => (
        <Card key={item.id ?? index} className="card-hover">
          <CardContent className="p-4">
            {renderCard(item, index)}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface MobileTableFieldProps {
  label: string;
  value: React.ReactNode;
  className?: string;
}

export function MobileTableField({ label, value, className }: MobileTableFieldProps) {
  return (
    <div className={cn('flex justify-between items-center py-1', className)}>
      <span className="text-sm text-muted-foreground font-medium">{label}:</span>
      <span className="text-sm">{value}</span>
    </div>
  );
}

interface ResponsiveTableProps {
  data: unknown[];
  desktopTable: React.ReactNode;
  renderMobileCard: (item: unknown, index: number) => React.ReactNode;
  className?: string;
}

export function ResponsiveTable({ 
  data, 
  desktopTable, 
  renderMobileCard, 
  className 
}: ResponsiveTableProps) {
  return (
    <>
      {/* Desktop Table */}
      <div className="hidden md:block">
        {desktopTable}
      </div>
      
      {/* Mobile Cards */}
      <div className="md:hidden">
        <MobileTable 
          data={data} 
          renderCard={renderMobileCard}
          className={className}
        />
      </div>
    </>
  );
}
