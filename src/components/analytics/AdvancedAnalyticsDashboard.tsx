/**
import React, { useEffect, useState } from 'react';

 * Advanced Analytics Dashboard
 * Comprehensive business intelligence dashboard with real-time analytics and predictive insights
 */

'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { logger } from '@/lib/logger';
import {
  TrendingUp, 
  TrendingDown,
  Users, 
  Calendar, 
  DollarSign,
  Activity,
  Brain,
  Target,
  Zap,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw,
  Download,
  Filter,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Bar<PERSON>hart as RechartsBarChart,
  <PERSON><PERSON>hart as RechartsPieChart,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  Line,
  Bar,
  Pie,
  Cell,
  Area,
  AreaChart,
} from 'recharts'

/**
 * Analytics data interfaces
 */
interface AnalyticsData {
  businessMetrics: unknown
  predictiveAnalytics: unknown
  isLoading: boolean
  lastUpdated: Date
}

/**
 * Advanced Analytics Dashboard Component
 */
export const AdvancedAnalyticsDashboard: React.FC = () => {
  const [data, setData] = useState<AnalyticsData>({
    businessMetrics: null,
    predictiveAnalytics: null,
    isLoading: true,
    lastUpdated: new Date(),
  })
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setData(prev => ({ ...prev, isLoading: true }))
      
      // In production, these would be real API calls
      const [businessMetrics, predictiveAnalytics] = await Promise.all([])
      
      setData({
        businessMetrics,
        predictiveAnalytics,
        isLoading: false,
        lastUpdated: new Date(),
      })
    } catch (error) {
      logger.error('Failed to fetch analytics data:', error as Error)
      // Mock data for demonstration
      setData({
        businessMetrics: mockBusinessMetrics,
        predictiveAnalytics: mockPredictiveAnalytics,
        isLoading: false,
        lastUpdated: new Date(),
      })
    }
  }

  // Auto-refresh data
  useEffect(() => {
    fetchAnalyticsData()
    
    if (autoRefresh) {
      const interval = setInterval(fetchAnalyticsData, 5 * 60 * 1000) // 5 minutes
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    )
  }

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-600' : 'text-red-600'
  }

  if (data.isLoading && !data.businessMetrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">加载分析数据...</span>
      </div>
    )
  }

  const { businessMetrics, predictiveAnalytics } = data

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">高级分析面板</h1>
          <p className="text-gray-600 mt-1">
            实时业务洞察、预测分析和智能建议
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">自动刷新</span>
            <Button
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              {autoRefresh ? <Activity className="h-4 w-4" /> : <Activity className="h-4 w-4 opacity-50" />}
            </Button>
          </div>
          <Button onClick={fetchAnalyticsData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(businessMetrics?.revenue?.total ?? 0)}
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              {getGrowthIcon(businessMetrics?.revenue?.growth ?? 0)}
              <span className={getGrowthColor(businessMetrics?.revenue?.growth ?? 0)}>
                {formatPercentage(businessMetrics?.revenue?.growth ?? 0)} 月增长
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">客户总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {businessMetrics?.clients?.total ?? 0}
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Badge variant="secondary">
                {businessMetrics?.clients?.new ?? 0} 新客户
              </Badge>
              <span>本月</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">预约完成率</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(businessMetrics?.appointments?.utilization ?? 0)}
            </div>
            <div className="text-xs text-muted-foreground">
              {businessMetrics?.appointments?.completed ?? 0} / {businessMetrics?.appointments?.total ?? 0} 预约
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">客户留存率</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(businessMetrics?.clients?.retention ?? 0)}
            </div>
            <div className="text-xs text-muted-foreground">
              平均生命周期价值: {formatCurrency(businessMetrics?.clients?.lifetime_value ?? 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="revenue">收入分析</TabsTrigger>
          <TabsTrigger value="clients">客户分析</TabsTrigger>
          <TabsTrigger value="treatments">治疗分析</TabsTrigger>
          <TabsTrigger value="predictions">预测分析</TabsTrigger>
          <TabsTrigger value="insights">智能洞察</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>收入趋势</CardTitle>
                <CardDescription>过去6个月收入变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stroke="#3b82f6" 
                      fill="#3b82f6" 
                      fillOpacity={0.1}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>收入预测</CardTitle>
                <CardDescription>未来6个月收入预测</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={forecastChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Line 
                      type="monotone" 
                      dataKey="actual" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={{ fill: '#3b82f6' }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="forecast" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={{ fill: '#10b981' }}
                    />
                    <Legend />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>客户分布</CardTitle>
                <CardDescription>新客户 vs 回头客户</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={clientDistributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {clientDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>客户获取趋势</CardTitle>
                <CardDescription>每月新客户获取情况</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={clientAcquisitionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="new_clients" fill="#3b82f6" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="treatments" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>热门治疗项目</CardTitle>
                <CardDescription>按预约次数排序</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {businessMetrics?.treatments?.popular?.slice(0, 5).map((treatment: unknown, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{treatment.name}</p>
                          <p className="text-sm text-gray-500">{treatment.count} 次预约</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(treatment.revenue)}</p>
                        <p className="text-sm text-gray-500">收入</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>治疗效果评估</CardTitle>
                <CardDescription>客户满意度和复购率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {businessMetrics?.treatments?.effectiveness?.map((treatment: unknown, index: number) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{treatment.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">满意度</span>
                          <Badge variant="secondary">{treatment.satisfaction}/5</Badge>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>复购率</span>
                          <span>{treatment.repeat_rate}%</span>
                        </div>
                        <Progress value={treatment.repeat_rate} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  <span>客户流失风险预测</span>
                </CardTitle>
                <CardDescription>基于AI模型的客户流失风险评估</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {predictiveAnalytics?.client_behavior?.churn_risk?.map((client: unknown, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">客户 {client.client_id}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {client.factors.map((factor: string, i: number) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="h-4 w-4 text-orange-500" />
                          <span className="font-medium text-orange-600">
                            {(client.risk_score * 100).toFixed(0)}%
                          </span>
                        </div>
                        <p className="text-xs text-gray-500">流失风险</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-green-600" />
                  <span>收入预测</span>
                </CardTitle>
                <CardDescription>基于历史数据的收入预测</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">下月预测收入</span>
                      <span className="text-2xl font-bold text-green-600">
                        {formatCurrency(predictiveAnalytics?.revenue_forecast?.next_month ?? 0)}
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      置信区间: {formatCurrency(predictiveAnalytics?.revenue_forecast?.confidence_interval?.[0] || 0)} - {formatCurrency(predictiveAnalytics?.revenue_forecast?.confidence_interval?.[1] || 0)}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">影响因素</h4>
                    {predictiveAnalytics?.revenue_forecast?.factors?.map((factor: string, index: number) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{factor}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-green-600">优化建议</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <p className="text-sm">在下午2-4点增加预约时段，这是需求高峰期</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <p className="text-sm">推广面部护理项目，客户满意度和复购率最高</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <p className="text-sm">关注高风险流失客户，及时进行客户关怀</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-blue-600">市场机会</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-500 mt-0.5" />
                    <p className="text-sm">春夏季节治疗需求上升，可提前准备营销活动</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-500 mt-0.5" />
                    <p className="text-sm">新客户获取成本较低，可加大获客投入</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-500 mt-0.5" />
                    <p className="text-sm">客户生命周期价值持续增长，值得长期投资</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-orange-600">风险提醒</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <p className="text-sm">部分客户预约取消率较高，需要改善服务流程</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <p className="text-sm">员工工作负荷不均，需要优化排班安排</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                    <p className="text-sm">竞争加剧，需要关注市场动态和价格策略</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Last Updated */}
      <div className="text-center text-sm text-gray-500">
        最后更新: {data.lastUpdated.toLocaleString('zh-CN')}
      </div>
    </div>
  )
}

// Mock data and chart configurations
const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']

const mockBusinessMetrics = {
  revenue: { total: 580000, monthly: 45000, daily: 1500, growth: 12.5, forecast: [48000, 52000, 55000, 58000, 61000, 64000] },
  clients: { total: 1250, new: 85, returning: 320, retention: 78.5, lifetime_value: 12500 },
  appointments: { total: 450, completed: 380, cancelled: 45, no_show: 25, utilization: 84.4 },
  treatments: {
    popular: [
      { name: '面部护理', count: 120, revenue: 24000 },
      { name: '激光治疗', count: 95, revenue: 28500 },
      { name: '注射美容', count: 80, revenue: 32000 },
      { name: '皮肤检测', count: 65, revenue: 9750 },
    ],
    effectiveness: [
      { name: '面部护理', satisfaction: 4.5, repeat_rate: 75 },
      { name: '激光治疗', satisfaction: 4.3, repeat_rate: 68 },
      { name: '注射美容', satisfaction: 4.7, repeat_rate: 82 },
      { name: '皮肤检测', satisfaction: 4.1, repeat_rate: 45 },
    ]
  },
  staff: { productivity: [], utilization: 78.5, satisfaction: 4.2 }
}

const mockPredictiveAnalytics = {
  client_behavior: {
    churn_risk: [
      { client_id: 'C001', risk_score: 0.75, factors: ['长时间未预约', '取消率高'] },
      { client_id: 'C002', risk_score: 0.65, factors: ['价格敏感', '服务满意度下降'] },
    ]
  },
  revenue_forecast: {
    next_month: 48000,
    next_quarter: 155000,
    confidence_interval: [42000, 54000],
    factors: ['季节性趋势', '客户增长', '服务价格调整', '市场竞争']
  }
}

const revenueChartData = [
  { month: '7月', revenue: 38000 },
  { month: '8月', revenue: 42000 },
  { month: '9月', revenue: 39000 },
  { month: '10月', revenue: 45000 },
  { month: '11月', revenue: 41000 },
  { month: '12月', revenue: 45000 },
]

const forecastChartData = [
  { month: '10月', actual: 45000, forecast: null },
  { month: '11月', actual: 41000, forecast: null },
  { month: '12月', actual: 45000, forecast: null },
  { month: '1月', actual: null, forecast: 48000 },
  { month: '2月', actual: null, forecast: 52000 },
  { month: '3月', actual: null, forecast: 55000 },
]

const clientDistributionData = [
  { name: '新客户', value: 85 },
  { name: '回头客户', value: 320 },
]

const clientAcquisitionData = [
  { month: '7月', new_clients: 65 },
  { month: '8月', new_clients: 78 },
  { month: '9月', new_clients: 72 },
  { month: '10月', new_clients: 85 },
  { month: '11月', new_clients: 79 },
  { month: '12月', new_clients: 85 },
]
