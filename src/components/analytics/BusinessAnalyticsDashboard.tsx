'use client'


import { useEffe<PERSON>, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { logger } from '@/lib/logger';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  Pie<PERSON>hart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar,
  DollarSign,
  Package,
  Clock,
  Star,
  Target,
  Activity,
  BarChart3,
  Download
} from 'lucide-react'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface BusinessMetrics {
  total_clients: number
  new_clients_this_month: number
  client_growth_rate: number
  total_appointments: number
  appointment_completion_rate: number
  average_appointment_value: number
  client_retention_rate: number
  popular_treatments: TreatmentPopularity[]
  revenue_by_period: RevenueData[]
  client_satisfaction_score: number
  staff_utilization_rate: number
}

interface TreatmentPopularity {
  treatment_name: string
  count: number
  revenue: number
  growth_rate: number
}

interface RevenueData {
  period: string
  revenue: number
  appointments: number
  new_clients: number
}

interface ClientSegment {
  segment: string
  count: number
  percentage: number
  avg_spending: number
  color: string
}

interface AppointmentPattern {
  hour: number
  count: number
  day_of_week: string
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export default function BusinessAnalyticsDashboard() {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null)
  const [clientSegments, setClientSegments] = useState<ClientSegment[]>([])
  const [appointmentPatterns, setAppointmentPatterns] = useState<AppointmentPattern[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('30days')

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      
      // Here you would fetch real analytics data from your API
      // const response = await fetch(`/api/analytics/business?period=${selectedPeriod}`)
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockMetrics: BusinessMetrics = {
        total_clients: 1250,
        new_clients_this_month: 85,
        client_growth_rate: 12.5,
        total_appointments: 2340,
        appointment_completion_rate: 92.5,
        average_appointment_value: 450,
        client_retention_rate: 78.5,
        client_satisfaction_score: 4.6,
        staff_utilization_rate: 85.2,
        popular_treatments: [
          { treatment_name: '深层清洁面膜', count: 156, revenue: 31200, growth_rate: 15.2 },
          { treatment_name: '补水保湿护理', count: 134, revenue: 40200, growth_rate: 8.7 },
          { treatment_name: '抗衰老精华', count: 98, revenue: 49000, growth_rate: 22.1 },
          { treatment_name: '激光美白', count: 76, revenue: 38000, growth_rate: -5.3 },
          { treatment_name: '微针治疗', count: 45, revenue: 22500, growth_rate: 35.8 }
        ],
        revenue_by_period: Array.from({ length: 30 }, (_, i) => ({
          period: format(subDays(new Date(), 29 - i), 'MM/dd'),
          revenue: Math.floor(Math.random() * 8000) + 2000,
          appointments: Math.floor(Math.random() * 20) + 5,
          new_clients: Math.floor(Math.random() * 5) + 1
        }))
      }

      const mockClientSegments: ClientSegment[] = [
        { segment: 'VIP客户', count: 125, percentage: 10, avg_spending: 2500, color: '#8B5CF6' },
        { segment: '常规客户', count: 750, percentage: 60, avg_spending: 800, color: '#06B6D4' },
        { segment: '新客户', count: 250, percentage: 20, avg_spending: 400, color: '#10B981' },
        { segment: '流失客户', count: 125, percentage: 10, avg_spending: 200, color: '#F59E0B' }
      ]

      const mockAppointmentPatterns: AppointmentPattern[] = [
        { hour: 9, count: 45, day_of_week: '周一' },
        { hour: 10, count: 78, day_of_week: '周一' },
        { hour: 11, count: 92, day_of_week: '周一' },
        { hour: 14, count: 85, day_of_week: '周一' },
        { hour: 15, count: 95, day_of_week: '周一' },
        { hour: 16, count: 88, day_of_week: '周一' },
        { hour: 17, count: 72, day_of_week: '周一' },
        { hour: 18, count: 65, day_of_week: '周一' }
      ]

      setMetrics(mockMetrics)
      setClientSegments(mockClientSegments)
      setAppointmentPatterns(mockAppointmentPatterns)
    } catch (error) {
      logger.error('Error fetching analytics data:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalyticsData()
  }, [selectedPeriod])

  const exportReport = () => {
    // Here you would implement report export functionality
    logger.info('Exporting analytics report')
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!metrics) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">业务分析仪表板</h2>
          <p className="text-muted-foreground">全面的业务数据分析和洞察</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="1year">过去一年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={exportReport}>
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总客户数</p>
                <p className="text-2xl font-bold">{metrics.total_clients.toLocaleString()}</p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span className="text-xs text-green-600">+{metrics.client_growth_rate}%</span>
                </div>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">预约完成率</p>
                <p className="text-2xl font-bold">{metrics.appointment_completion_rate}%</p>
                <p className="text-xs text-muted-foreground mt-1">
                  {metrics.total_appointments} 总预约
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">平均客单价</p>
                <p className="text-2xl font-bold">${metrics.average_appointment_value}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  每次预约平均收入
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">客户满意度</p>
                <p className="text-2xl font-bold">{metrics.client_satisfaction_score}/5.0</p>
                <div className="flex items-center gap-1 mt-1">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span className="text-xs text-muted-foreground">优秀评价</span>
                </div>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">客户留存率</p>
                <p className="text-2xl font-bold">{metrics.client_retention_rate}%</p>
              </div>
              <Target className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">员工利用率</p>
                <p className="text-2xl font-bold">{metrics.staff_utilization_rate}%</p>
              </div>
              <Activity className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">本月新客户</p>
                <p className="text-2xl font-bold">{metrics.new_clients_this_month}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analysis */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="revenue">收入分析</TabsTrigger>
          <TabsTrigger value="treatments">治疗项目</TabsTrigger>
          <TabsTrigger value="clients">客户分析</TabsTrigger>
          <TabsTrigger value="patterns">预约模式</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>收入趋势分析</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={metrics.revenue_by_period}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'revenue' ? `$${Number(value).toLocaleString()}` : value,
                      name === 'revenue' ? '收入' : name === 'appointments' ? '预约数' : '新客户'
                    ]}
                  />
                  <Area type="monotone" dataKey="revenue" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="appointments" stackId="2" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="treatments" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>热门治疗项目</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={metrics.popular_treatments}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="treatment_name" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value) => [value, '次数']} />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>治疗项目表现</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.popular_treatments.map((treatment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{treatment.treatment_name}</p>
                        <p className="text-sm text-muted-foreground">
                          {treatment.count} 次 • ${treatment.revenue.toLocaleString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge 
                          variant={treatment.growth_rate > 0 ? "default" : "destructive"}
                          className="text-xs"
                        >
                          {treatment.growth_rate > 0 ? '+' : ''}{treatment.growth_rate}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>客户分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={clientSegments}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ segment, percentage }) => `${segment} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {clientSegments.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [value, '客户数']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>客户价值分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {clientSegments.map((segment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: segment.color }}
                        />
                        <div>
                          <p className="font-medium">{segment.segment}</p>
                          <p className="text-sm text-muted-foreground">{segment.count} 位客户</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${segment.avg_spending.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">平均消费</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>预约时间模式</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={appointmentPatterns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" tickFormatter={(hour) => `${hour}:00`} />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [value, '预约数']}
                    labelFormatter={(hour) => `${hour}:00 时段`}
                  />
                  <Bar dataKey="count" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
