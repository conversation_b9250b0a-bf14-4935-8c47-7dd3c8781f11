/**
import React, { useEffect, useState } from 'react';

 * Mobile-Optimized Layout Component
 * Responsive layout with mobile-first design and touch-friendly navigation
 */

'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { 
  Home, 
  Users, 
  Calendar, 
  CreditCard, 
  BarChart3, 
  Settings,
  Menu,
  X,
  Bell,
  Search,
  User
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

/**
 * Navigation item interface
 */
interface NavItem {
  href: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  mobileOnly?: boolean
  desktopOnly?: boolean
}

/**
 * Mobile layout props
 */
interface MobileLayoutProps {
  children: React.ReactNode
  title?: string
  showBackButton?: boolean
  onBackClick?: () => void
  rightAction?: React.ReactNode
}

/**
 * Navigation items configuration
 */
const navigationItems: NavItem[] = [
  {
    href: '/',
    label: '首页',
    icon: Home,
  },
  {
    href: '/clients',
    label: '客户',
    icon: Users,
  },
  {
    href: '/appointments',
    label: '预约',
    icon: Calendar,
    badge: 3, // Example badge for pending appointments
  },
  {
    href: '/payments',
    label: '收费',
    icon: CreditCard,
  },
  {
    href: '/reports',
    label: '报表',
    icon: BarChart3,
    desktopOnly: true, // Only show on desktop
  },
  {
    href: '/settings',
    label: '设置',
    icon: Settings,
  },
]

/**
 * Mobile Layout Component
 */
export const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  onBackClick,
  rightAction,
}) => {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Detect mobile screen size
  useEffect(() => {
    setMounted(true)
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(typeof window !== "undefined" && window.innerWidth < 1024)
      }
    }

    checkMobile()
    if (typeof window !== 'undefined') {
      typeof window !== "undefined" && window.addEventListener('resize', checkMobile)
      return () => typeof window !== "undefined" && window.removeEventListener('resize', checkMobile)
    }
  }, [])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (typeof document !== 'undefined') {
      if (isMobileMenuOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'unset';
      }
    }

    return () => {
      if (typeof document !== 'undefined') {
        document.body.style.overflow = 'unset';
      }
    };
  }, [isMobileMenuOpen])

  const isActiveRoute = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <header className="lg:hidden sticky top-0 z-40 bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {showBackButton ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBackClick}
                className="btn-touch p-2"
              >
                <X className="h-5 w-5" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(true)}
                className="btn-touch p-2"
              >
                <Menu className="h-5 w-5" />
              </Button>
            )}
            <h1 className="text-lg font-semibold text-gray-900 truncate">
              {title ?? '医美诊所管理系统'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="btn-touch p-2">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="sm" className="btn-touch p-2 relative">
              <Bell className="h-5 w-5" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
              >
                2
              </Badge>
            </Button>
            {rightAction}
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="fixed inset-y-0 left-0 w-80 max-w-sm bg-white shadow-xl">
            {/* Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">管理员</p>
                  <p className="text-sm text-gray-500"><EMAIL></p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(false)}
                className="btn-touch p-2"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Menu Items */}
            <nav className="p-4 space-y-2">
              {navigationItems
                .filter(item => !item.desktopOnly)
                .map((item) => {
                  const Icon = item.icon
                  const isActive = isActiveRoute(item.href)
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors btn-touch',
                        isActive
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-100'
                      )}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  )
                })}
            </nav>

            {/* Menu Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
              <Link
                href="/profile"
                className="flex items-center space-x-3 px-3 py-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors btn-touch"
              >
                <User className="h-5 w-5" />
                <span className="font-medium">个人资料</span>
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <aside className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-40 lg:w-64 lg:bg-white lg:border-r lg:border-gray-200 lg:flex lg:flex-col">
        {/* Sidebar Header */}
        <div className="flex items-center h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-900">医美诊所</span>
          </div>
        </div>

        {/* Sidebar Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const isActive = isActiveRoute(item.href)
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-100'
                )}
              >
                <Icon className="h-5 w-5" />
                <span className="font-medium">{item.label}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </Link>
            )
          })}
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-3 px-3 py-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-gray-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">管理员</p>
              <p className="text-xs text-gray-500 truncate"><EMAIL></p>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main className={cn(
        'min-h-screen',
        'lg:pl-64', // Desktop: account for sidebar
        'pb-20 lg:pb-0' // Mobile: account for bottom navigation
      )}>
        <div className="container-responsive py-6">
          {children}
        </div>
      </main>

      {/* Mobile Bottom Navigation */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-2 z-30">
        <div className="flex justify-around">
          {navigationItems
            .filter(item => !item.desktopOnly && !item.mobileOnly)
            .slice(0, 5) // Limit to 5 items for mobile
            .map((item) => {
              const Icon = item.icon
              const isActive = isActiveRoute(item.href)
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'nav-mobile-item relative',
                    isActive && 'active'
                  )}
                >
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs">{item.label}</span>
                  {item.badge && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
                    >
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              )
            })}
        </div>
        
        {/* Safe area for devices with home indicator */}
        <div className="h-safe-area-inset-bottom" />
      </nav>
    </div>
  )
}

/**
 * Mobile Layout Provider for easy usage
 */
export const withMobileLayout = <P extends object>(
  Component: React.ComponentType<P>,
  layoutProps?: Partial<MobileLayoutProps>
) => {
  const WrappedComponent = (props: P) => (
    <MobileLayout {...layoutProps}>
      <Component {...props} />
    </MobileLayout>
  )
  
  WrappedComponent.displayName = `withMobileLayout(${Component.displayName ?? Component.name})`
  return WrappedComponent
}
