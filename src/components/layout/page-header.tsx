
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  description?: string;
  emoji?: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: LucideIcon;
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  };
  children?: React.ReactNode;
}

export default function PageHeader({
  title,
  description,
  emoji,
  action,
  children
}: PageHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 border-b border-border/50">
      <div className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">
          {emoji && <span className="mr-2">{emoji}</span>}
          {title}
        </h1>
        {description && (
          <p className="text-sm sm:text-base text-muted-foreground max-w-2xl">{description}</p>
        )}
      </div>

      <div className="flex items-center gap-2 w-full sm:w-auto">
        {children}
        {action && (
          <Button
            onClick={action.onClick}
            variant={action.variant ?? 'default'}
            className="w-full sm:w-auto shadow-sm"
            size="default"
          >
            {action.icon && <action.icon className="mr-2 h-4 w-4" />}
            {action.label}
          </Button>
        )}
      </div>
    </div>
  );
}
