
import React, { Fragment } from 'react';
import PageContainer from '@/components/layout/page-container';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface PageLayoutProps {
  title: string;
  description: string;
  action?: {
    label: string;
    onClick?: () => void;
    href?: string;
    icon?: LucideIcon;
    variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  };
  children: React.ReactNode;
  scrollable?: boolean;
}

export default function PageLayout({
  title,
  description,
  action,
  children,
  scrollable = true
}: PageLayoutProps) {
  return (
    <PageContainer scrollable={scrollable}>
      <div className='flex flex-1 flex-col space-y-4'>
        <div className='flex items-start justify-between'>
          <Heading title={title} description={description} />
          {action && (
            <>
              {action.href ? (
                <a
                  href={action.href}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    action.variant === 'outline'
                      ? 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
                      : 'bg-primary text-primary-foreground hover:bg-primary/90'
                  } h-10 px-4 py-2`}
                >
                  {action.icon && <action.icon className='mr-2 h-4 w-4' />}
                  {action.label}
                </a>
              ) : (
                <Button
                  onClick={action.onClick}
                  variant={action.variant ?? 'default'}
                >
                  {action.icon && <action.icon className='mr-2 h-4 w-4' />}
                  {action.label}
                </Button>
              )}
            </>
          )}
        </div>
        <Separator />
        {children}
      </div>
    </PageContainer>
  );
}
