'use client'

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface ClientThemeWrapperProps {
  children: React.ReactNode
  activeThemeValue?: string
}

export default function ClientThemeWrapper({ 
  children, 
  activeThemeValue 
}: ClientThemeWrapperProps) {
  const [mounted, setMounted] = useState(false)
  const [themeClasses, setThemeClasses] = useState('')

  useEffect(() => {
    setMounted(true)
    
    // Calculate theme classes on client side to avoid hydration mismatch
    const isScaled = activeThemeValue?.endsWith('-scaled')
    const classes = cn(
      activeThemeValue ? `theme-${activeThemeValue}` : '',
      isScaled ? 'theme-scaled' : ''
    )
    setThemeClasses(classes)
  }, [activeThemeValue])

  // Apply theme classes to body after mount to avoid hydration issues
  useEffect(() => {
    if (mounted && themeClasses) {
      const body = document.body
      // Remove any existing theme classes
      body.classList.forEach(className => {
        if (className.startsWith('theme-')) {
          body.classList.remove(className)
        }
      })
      // Add new theme classes
      if (themeClasses) {
        themeClasses.split(' ').forEach(className => {
          if (className.trim()) {
            body.classList.add(className.trim())
          }
        })
      }
    }
  }, [mounted, themeClasses])

  return <>{children}</>
}
