'use client'


import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { logger } from '@/lib/logger';
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Eye,
  TrendingUp,
  Activity,
  CreditCard
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { getStatusColor, getStatusText } from '@/lib/colors'

interface Appointment {
  id: string
  appointment_date: string
  start_time: string
  end_time: string
  status: string
  appointment_type: string
  notes?: string
  custom_price?: number
  treatment?: {
    name_chinese: string
    default_price: number
    duration_minutes: number
  }
}

interface Invoice {
  id: string
  invoice_date: string
  treatment_date: string
  total_amount: number
  deposit_amount: number
  status: string
  invoice_items?: Array<{
    treatment_name_chinese: string
    quantity: number
    unit_price: number
    total_price: number
  }>
}

interface Payment {
  id: string
  payment_date: string
  amount: number
  payment_method: string
  payment_type: string
  reference_number?: string
}

interface ClientHistoryProps {
  clientId: string
  className?: string
}

export default function ClientHistory({ clientId, className = '' }: ClientHistoryProps) {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalAppointments: 0,
    completedTreatments: 0,
    totalSpent: 0,
    lastVisit: null as string | null
  })

  useEffect(() => {
    fetchClientHistory()
  }, [clientId])

  const fetchClientHistory = async () => {
    try {
      setLoading(true)
      
      // 并行获取数据
      const [appointmentsRes, invoicesRes, paymentsRes] = await Promise.all([])

      const appointmentsData = appointmentsRes.ok ? await appointmentsRes.json() : { appointments: [] }
      const invoicesData = invoicesRes.ok ? await invoicesRes.json() : { invoices: [] }
      const paymentsData = paymentsRes.ok ? await paymentsRes.json() : { payments: [] }

      setAppointments(appointmentsData.appointments ?? [])
      setInvoices(invoicesData.invoices ?? [])
      setPayments(paymentsData.payments ?? [])

      // 计算统计数据
      const totalAppointments = appointmentsData.appointments?.length ?? 0
      const completedTreatments = appointmentsData.appointments?.filter((apt: Appointment) => 
        apt.status === 'completed'
      ).length ?? 0
      const totalSpent = paymentsData.payments?.reduce((sum: number, payment: Payment) => 
        sum + payment.amount, 0
      ) || 0
      const lastVisit = appointmentsData.appointments?.length > 0 
        ? appointmentsData.appointments
            .filter((apt: Appointment) => apt.status === 'completed')
            .sort((a: Appointment, b: Appointment) => 
              new Date(b.appointment_date).getTime() - new Date(a.appointment_date).getTime()
            )[0]?.appointment_date
        : null

      setStats({
        totalAppointments,
        completedTreatments,
        totalSpent,
        lastVisit
      })

    } catch (error) {
      logger.error('Error fetching client history:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`

  const getAppointmentStatusBadge = (status: string) => {
    const colors = getStatusColor('appointment', status)
    const text = getStatusText('appointment', status)
    return (
      <Badge className={`${colors.bg} ${colors.text} ${colors.border} border`}>
        {text}
      </Badge>
    )
  }

  const getPaymentMethodText = (method: string) => {
    const methods: Record<string, string> = {
      'cash': '现金',
      'credit_card': '信用卡',
      'debit_card': '借记卡',
      'bank_transfer': '银行转账',
      'check': '支票',
      'other': '其他'
    }
    return methods[method] || method
  }

  const getPaymentTypeText = (type: string) => {
    const types: Record<string, string> = {
      'deposit': '定金',
      'full_payment': '全款',
      'partial_payment': '部分付款'
    }
    return types[type] || type
  }

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">总预约数</p>
                <p className="text-2xl font-bold">{stats.totalAppointments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-full">
                <Activity className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">完成治疗</p>
                <p className="text-2xl font-bold">{stats.completedTreatments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-full">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">总消费</p>
                <p className="text-2xl font-bold">{formatCurrency(stats.totalSpent)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-full">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">最后就诊</p>
                <p className="text-lg font-semibold">
                  {stats.lastVisit 
                    ? format(new Date(stats.lastVisit), 'MM/dd', { locale: zhCN })
                    : '无记录'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细历史记录 */}
      <Tabs defaultValue="appointments" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="appointments">预约历史</TabsTrigger>
          <TabsTrigger value="invoices">账单记录</TabsTrigger>
          <TabsTrigger value="payments">付款记录</TabsTrigger>
        </TabsList>

        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                预约历史 ({appointments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {appointments.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">暂无预约记录</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {appointments.map((appointment) => (
                    <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-medium">
                            {appointment.treatment?.name_chinese ?? '未知治疗'}
                          </h4>
                          {getAppointmentStatusBadge(appointment.status)}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {format(new Date(appointment.appointment_date), 'yyyy年MM月dd日', { locale: zhCN })}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {appointment.start_time} - {appointment.end_time}
                          </span>
                          {appointment.custom_price && (
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {formatCurrency(appointment.custom_price)}
                            </span>
                          )}
                        </div>
                        {appointment.notes && (
                          <p className="text-sm text-muted-foreground mt-2">{appointment.notes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                账单记录 ({invoices.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {invoices.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">暂无账单记录</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <h4 className="font-medium">账单 #{invoice.id.slice(-8)}</h4>
                          <Badge variant={invoice.status === 'paid_in_full' ? 'default' : 'secondary'}>
                            {getStatusText('invoice', invoice.status)}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatCurrency(invoice.total_amount)}</p>
                          <p className="text-sm text-muted-foreground">
                            定金: {formatCurrency(invoice.deposit_amount)}
                          </p>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>账单日期: {format(new Date(invoice.invoice_date), 'yyyy年MM月dd日', { locale: zhCN })}</p>
                        <p>治疗日期: {format(new Date(invoice.treatment_date), 'yyyy年MM月dd日', { locale: zhCN })}</p>
                      </div>
                      {invoice.invoice_items && invoice.invoice_items.length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <p className="text-sm font-medium mb-2">治疗项目:</p>
                          {invoice.invoice_items.map((item, index) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span>{item.treatment_name_chinese} x{item.quantity}</span>
                              <span>{formatCurrency(item.total_price)}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                付款记录 ({payments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {payments.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">暂无付款记录</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="flex items-center gap-3 mb-1">
                          <h4 className="font-medium">{formatCurrency(payment.amount)}</h4>
                          <Badge variant="outline">{getPaymentTypeText(payment.payment_type)}</Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>{format(new Date(payment.payment_date), 'yyyy年MM月dd日', { locale: zhCN })}</span>
                          <span>{getPaymentMethodText(payment.payment_method)}</span>
                          {payment.reference_number && (
                            <span>参考号: {payment.reference_number}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
