'use client'


import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { logger } from '@/lib/logger';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Settings, 
  Clock, 
  Bell, 
  Heart, 
  Thermometer,
  Volume2,
  Palette,
  Save,
  RotateCcw
} from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface ClientPreference {
  id: string
  client_id: string
  // Appointment preferences
  preferred_time_slots: string[]
  preferred_days: string[]
  advance_notice_days: number
  reminder_preferences: {
    sms: boolean
    email: boolean
    call: boolean
  }
  // Treatment preferences
  preferred_room_temperature: number
  preferred_music_volume: number
  preferred_lighting: string
  skin_sensitivity_level: string
  allergies: string[]
  preferred_products: string[]
  // Communication preferences
  preferred_language: string
  communication_style: string
  privacy_level: string
  // Special notes
  special_instructions: string
  medical_notes: string
  created_at: string
  updated_at: string
}

interface ClientPreferencesProps {
  clientId: string
  onPreferencesUpdate?: (preferences: ClientPreference) => void
}

const timeSlots = [
  { value: 'morning', label: '上午 (9:00-12:00)' },
  { value: 'afternoon', label: '下午 (12:00-17:00)' },
  { value: 'evening', label: '晚上 (17:00-20:00)' }
]

const weekDays = [
  { value: 'monday', label: '周一' },
  { value: 'tuesday', label: '周二' },
  { value: 'wednesday', label: '周三' },
  { value: 'thursday', label: '周四' },
  { value: 'friday', label: '周五' },
  { value: 'saturday', label: '周六' },
  { value: 'sunday', label: '周日' }
]

const skinSensitivityLevels = [
  { value: 'low', label: '不敏感', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: '轻度敏感', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: '高度敏感', color: 'bg-red-100 text-red-800' }
]

const lightingOptions = [
  { value: 'bright', label: '明亮' },
  { value: 'dim', label: '柔和' },
  { value: 'dark', label: '昏暗' }
]

export default function ClientPreferences({ clientId, onPreferencesUpdate }: ClientPreferencesProps) {
  const [preferences, setPreferences] = useState<ClientPreference | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const [formData, setFormData] = useState({
    preferred_time_slots: [] as string[],
    preferred_days: [] as string[],
    advance_notice_days: 1,
    reminder_preferences: {
      sms: true,
      email: false,
      call: false
    },
    preferred_room_temperature: 22,
    preferred_music_volume: 3,
    preferred_lighting: 'dim',
    skin_sensitivity_level: 'medium',
    allergies: [] as string[],
    preferred_products: [] as string[],
    preferred_language: 'zh-CN',
    communication_style: 'friendly',
    privacy_level: 'normal',
    special_instructions: '',
    medical_notes: ''
  })

  const fetchPreferences = async () => {
    try {
      setLoading(true)
      // Here you would fetch from your API
      // const response = await fetch(`/api/clients/${clientId}/preferences`)
      // if (response.ok) {
      //   const data = await response.json()
      //   setPreferences(data.preferences)
      //   setFormData(data.preferences)
      // }
      
      // For now, use default values
      setPreferences(null)
    } catch (error) {
      logger.error('Error fetching preferences:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (clientId) {
      fetchPreferences()
    }
  }, [clientId])

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // Here you would save to your API
      // const response = await fetch(`/api/clients/${clientId}/preferences`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData)
      // })
      
      // if (!response.ok) throw new Error('保存失败')
      
      // const savedPreferences = await response.json()
      // setPreferences(savedPreferences.preferences)
      
      setHasChanges(false)
      showSuccessToast('偏好设置已保存', '客户偏好设置已成功更新')
      
      if (onPreferencesUpdate) {
        onPreferencesUpdate(formData as any)
      }
    } catch (error: unknown) {
      logger.error('Error saving preferences:', error as Error)
      showErrorToast('保存失败', (error as Error).message)
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (preferences) {
      setFormData(preferences)
    } else {
      // Reset to defaults
      setFormData({
        preferred_time_slots: [],
        preferred_days: [],
        advance_notice_days: 1,
        reminder_preferences: {
          sms: true,
          email: false,
          call: false
        },
        preferred_room_temperature: 22,
        preferred_music_volume: 3,
        preferred_lighting: 'dim',
        skin_sensitivity_level: 'medium',
        allergies: [],
        preferred_products: [],
        preferred_language: 'zh-CN',
        communication_style: 'friendly',
        privacy_level: 'normal',
        special_instructions: '',
        medical_notes: ''
      })
    }
    setHasChanges(false)
  }

  const updateFormData = (field: string, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }

  const toggleArrayValue = (array: string[], value: string) => {
    return array.includes(value)
      ? array.filter(item => item !== value)
      : [...array, value]
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Clock className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">加载偏好设置...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          <h3 className="text-lg font-semibold">客户偏好设置</h3>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={!hasChanges}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!hasChanges ?? saving}
          >
            <Save className="mr-2 h-4 w-4" />
            {saving ? '保存中...' : '保存设置'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Appointment Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              预约偏好
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Preferred Time Slots */}
            <div>
              <Label className="text-sm font-medium">偏好时间段</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {timeSlots.map((slot) => (
                  <Badge
                    key={slot.value}
                    variant={formData.preferred_time_slots.includes(slot.value) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => updateFormData('preferred_time_slots', 
                      toggleArrayValue(formData.preferred_time_slots, slot.value)
                    )}
                  >
                    {slot.label}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Preferred Days */}
            <div>
              <Label className="text-sm font-medium">偏好日期</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {weekDays.map((day) => (
                  <Badge
                    key={day.value}
                    variant={formData.preferred_days.includes(day.value) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => updateFormData('preferred_days', 
                      toggleArrayValue(formData.preferred_days, day.value)
                    )}
                  >
                    {day.label}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Advance Notice */}
            <div>
              <Label htmlFor="advance-notice">提前通知天数</Label>
              <Input
                id="advance-notice"
                type="number"
                min="0"
                max="7"
                value={formData.advance_notice_days}
                onChange={(e) => updateFormData('advance_notice_days', parseInt(e.target.value))}
                className="mt-1"
              />
            </div>

            {/* Reminder Preferences */}
            <div>
              <Label className="text-sm font-medium mb-3 block">提醒方式</Label>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4" />
                    <span className="text-sm">短信提醒</span>
                  </div>
                  <Switch
                    checked={formData.reminder_preferences.sms}
                    onCheckedChange={(checked) => 
                      updateFormData('reminder_preferences', {
                        ...formData.reminder_preferences,
                        sms: checked
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4" />
                    <span className="text-sm">邮件提醒</span>
                  </div>
                  <Switch
                    checked={formData.reminder_preferences.email}
                    onCheckedChange={(checked) => 
                      updateFormData('reminder_preferences', {
                        ...formData.reminder_preferences,
                        email: checked
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4" />
                    <span className="text-sm">电话提醒</span>
                  </div>
                  <Switch
                    checked={formData.reminder_preferences.call}
                    onCheckedChange={(checked) => 
                      updateFormData('reminder_preferences', {
                        ...formData.reminder_preferences,
                        call: checked
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Treatment Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              治疗偏好
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Room Temperature */}
            <div>
              <Label className="flex items-center gap-2">
                <Thermometer className="h-4 w-4" />
                房间温度偏好: {formData.preferred_room_temperature}°C
              </Label>
              <Input
                type="range"
                min="18"
                max="28"
                value={formData.preferred_room_temperature}
                onChange={(e) => updateFormData('preferred_room_temperature', parseInt(e.target.value))}
                className="mt-2"
              />
            </div>

            {/* Music Volume */}
            <div>
              <Label className="flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                音乐音量: {formData.preferred_music_volume}/10
              </Label>
              <Input
                type="range"
                min="0"
                max="10"
                value={formData.preferred_music_volume}
                onChange={(e) => updateFormData('preferred_music_volume', parseInt(e.target.value))}
                className="mt-2"
              />
            </div>

            {/* Lighting */}
            <div>
              <Label className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                灯光偏好
              </Label>
              <Select
                value={formData.preferred_lighting}
                onValueChange={(value) => updateFormData('preferred_lighting', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {lightingOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Skin Sensitivity */}
            <div>
              <Label className="text-sm font-medium">肌肤敏感度</Label>
              <div className="flex gap-2 mt-2">
                {skinSensitivityLevels.map((level) => (
                  <Badge
                    key={level.value}
                    className={
                      formData.skin_sensitivity_level === level.value 
                        ? level.color 
                        : 'bg-gray-100 text-gray-600 cursor-pointer'
                    }
                    onClick={() => updateFormData('skin_sensitivity_level', level.value)}
                  >
                    {level.label}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Special Instructions */}
            <div>
              <Label htmlFor="special-instructions">特殊说明</Label>
              <Textarea
                id="special-instructions"
                placeholder="请输入特殊治疗要求或注意事项..."
                value={formData.special_instructions}
                onChange={(e) => updateFormData('special_instructions', e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>

            {/* Medical Notes */}
            <div>
              <Label htmlFor="medical-notes">医疗备注</Label>
              <Textarea
                id="medical-notes"
                placeholder="请输入过敏史、病史或其他医疗相关信息..."
                value={formData.medical_notes}
                onChange={(e) => updateFormData('medical_notes', e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
