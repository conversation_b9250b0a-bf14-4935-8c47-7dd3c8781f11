'use client'
















import React, { Fragment, useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { logger } from '@/lib/logger';
import { , Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { , DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Edit, Eye, EyeOff, MoreHorizontal, Package, Palette, Plus, Trash2 } from 'lucide-react';
import { showErrorToast, showSuccessToast } from '@/lib/toast-utils';

interface TreatmentCategory {
  id: string
  name: string
  name_chinese: string
  description?: string
  color: string
  icon: string
  is_active: boolean
  sort_order: number
  treatment_count: number
  created_at: string
  updated_at: string
}

const categoryColors = [
  { value: 'blue', label: '蓝色', class: 'bg-blue-100 text-blue-800 border-blue-200' },
  { value: 'green', label: '绿色', class: 'bg-green-100 text-green-800 border-green-200' },
  { value: 'purple', label: '紫色', class: 'bg-purple-100 text-purple-800 border-purple-200' },
  { value: 'orange', label: '橙色', class: 'bg-orange-100 text-orange-800 border-orange-200' },
  { value: 'pink', label: '粉色', class: 'bg-pink-100 text-pink-800 border-pink-200' },
  { value: 'teal', label: '青色', class: 'bg-teal-100 text-teal-800 border-teal-200' },
  { value: 'indigo', label: '靛蓝', class: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
  { value: 'red', label: '红色', class: 'bg-red-100 text-red-800 border-red-200' }
]

const categoryIcons = [
  { value: 'package', label: '包装', icon: Package },
  { value: 'palette', label: '调色板', icon: Palette },
  { value: 'plus', label: '加号', icon: Plus }
]

interface TreatmentCategoryManagerProps {
  onCategorySelect?: (category: TreatmentCategory) => void
}

export default function TreatmentCategoryManager({ onCategorySelect }: TreatmentCategoryManagerProps) {
  const [categories, setCategories] = useState<TreatmentCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<TreatmentCategory | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    name_chinese: '',
    description: '',
    color: 'blue',
    icon: 'package',
    is_active: true
  })

  const fetchCategories = async () => {
    try {
      setLoading(true)
      // Here you would fetch from your API
      // const response = await fetch('/api/treatment-categories')
      // const data = await response.json()
      // setCategories(data.categories ?? [])
      
      // Mock data for demonstration
      const mockCategories: TreatmentCategory[] = []
      setCategories(mockCategories)
    } catch (error) {
      logger.error('Error fetching categories:', error as Error)
      showErrorToast('加载失败', '无法加载治疗分类')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const categoryData = {
        ...formData,
        sort_order: categories.length + 1
      }

      if (editingCategory) {
        // Update existing category
        // const response = await fetch(`/api/treatment-categories/${editingCategory.id}`, {
        //   method: 'PUT',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(categoryData)
        // })
        
        showSuccessToast('分类更新成功', `"${formData.name_chinese}"分类已更新`)
      } else {
        // Create new category
        // const response = await fetch('/api/treatment-categories', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(categoryData)
        // })
        
        showSuccessToast('分类创建成功', `"${formData.name_chinese}"分类已创建`)
      }

      setIsDialogOpen(false)
      setEditingCategory(null)
      resetForm()
      fetchCategories()
    } catch (error: unknown) {
      logger.error('Error saving category:', error as Error)
      showErrorToast('保存失败', (error as Error).message)
    }
  }

  const handleEdit = (category: TreatmentCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      name_chinese: category.name_chinese,
      description: category.description ?? '',
      color: category.color,
      icon: category.icon,
      is_active: category.is_active
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (categoryId: string) => {
    if (!confirm('确定要删除这个分类吗？此操作无法撤销。')) {
      return
    }

    try {
      // const response = await fetch(`/api/treatment-categories/${categoryId}`, {
      //   method: 'DELETE'
      // })
      
      showSuccessToast('分类删除成功', '分类已成功删除')
      fetchCategories()
    } catch (error: unknown) {
      logger.error('Error deleting category:', error as Error)
      showErrorToast('删除失败', (error as Error).message)
    }
  }

  const handleToggleActive = async (category: TreatmentCategory) => {
    try {
      // const response = await fetch(`/api/treatment-categories/${category.id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ ...category, is_active: !category.is_active })
      // })
      
      showSuccessToast(
        category.is_active ? '分类已禁用' : '分类已启用',
        `"${category.name_chinese}"分类状态已更新`
      )
      fetchCategories()
    } catch (error: unknown) {
      logger.error('Error toggling category status:', error as Error)
      showErrorToast('状态更新失败', (error as Error).message)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      name_chinese: '',
      description: '',
      color: 'blue',
      icon: 'package',
      is_active: true
    })
  }

  const getCategoryColorClass = (color: string) => {
    return categoryColors.find(c => c.value === color)?.class ?? categoryColors[0].class
  }

  const getCategoryIcon = (iconName: string) => {
    const iconConfig = categoryIcons.find(i => i.value === iconName)
    return iconConfig ? iconConfig.icon : Package
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Package className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">加载分类...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            治疗分类管理
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { resetForm(); setEditingCategory(null) }}>
                <Plus className="mr-2 h-4 w-4" />
                新建分类
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? '编辑分类' : '新建分类'}
                </DialogTitle>
                <DialogDescription>
                  {editingCategory ? '修改治疗分类信息' : '创建新的治疗分类'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      英文名称
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="col-span-3"
                      placeholder="facial"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name_chinese" className="text-right">
                      中文名称
                    </Label>
                    <Input
                      id="name_chinese"
                      value={formData.name_chinese}
                      onChange={(e) => setFormData(prev => ({ ...prev, name_chinese: e.target.value }))}
                      className="col-span-3"
                      placeholder="面部护理"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="description" className="text-right">
                      描述
                    </Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      className="col-span-3"
                      placeholder="分类描述..."
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">颜色</Label>
                    <div className="col-span-3 flex flex-wrap gap-2">
                      {categoryColors.map((color) => (
                        <Badge
                          key={color.value}
                          className={
                            formData.color === color.value 
                              ? color.class 
                              : 'bg-gray-100 text-gray-600 cursor-pointer'
                          }
                          onClick={() => setFormData(prev => ({ ...prev, color: color.value }))}
                        >
                          {color.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">
                    {editingCategory ? '更新分类' : '创建分类'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {categories.map((category) => {
            const IconComponent = getCategoryIcon(category.icon)
            return (
              <div
                key={category.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-muted rounded-lg">
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{category.name_chinese}</h4>
                      <Badge className={getCategoryColorClass(category.color)}>
                        {category.name}
                      </Badge>
                      {!category.is_active && (
                        <Badge variant="outline" className="text-muted-foreground">
                          已禁用
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {category.description ?? '暂无描述'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {category.treatment_count} 个治疗项目
                    </p>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(category)}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleToggleActive(category)}>
                      {category.is_active ? (
                        <>
                          <EyeOff className="mr-2 h-4 w-4" />
                          禁用
                        </>
                      ) : (
                        <>
                          <Eye className="mr-2 h-4 w-4" />
                          启用
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDelete(category.id)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )
          })}
          
          {categories.length === 0 && (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">暂无治疗分类</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
