'use client';

import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { logger } from '@/lib/logger';
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription
} from '@/components/ui/card';


interface RecentPayment {
  id: string;
  clientName: string;
  treatmentName: string;
  amount: number;
  date: string;
  paymentMethod: string;
}

export function RecentSales() {
  const [recentPayments, setRecentPayments] = useState<RecentPayment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecentPayments = async () => {
      try {
        const response = await fetch('/api/payments');
        if (response.ok) {
          const data = await response.json();
          const payments = data.payments ?? [];

          // 获取最近5个付款记录并格式化
          const recentPayments = payments
            .sort((a: unknown, b: unknown) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())
            .slice(0, 5)
            .map((payment: unknown) => ({
              id: payment.id,
              clientName: `${payment.clients?.first_name ?? ''} ${payment.clients?.last_name ?? ''}`.trim() || '未知客户',
              treatmentName: payment.invoices?.treatments?.name_chinese ?? '治疗项目',
              amount: payment.amount,
              date: payment.payment_date,
              paymentMethod: payment.payment_method
            }));

          setRecentPayments(recentPayments);
        }
      } catch (error) {
        logger.error('Error fetching recent payments:', error as Error);
        setRecentPayments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentPayments();
  }, []);

  if (loading) {
    return (
      <Card className='h-full'>
        <CardHeader>
          <CardTitle>最近治疗</CardTitle>
          <CardDescription>加载中...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-8'>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className='flex items-center animate-pulse'>
                <div className='h-9 w-9 bg-gray-200 rounded-full'></div>
                <div className='ml-4 space-y-1'>
                  <div className='h-4 w-24 bg-gray-200 rounded'></div>
                  <div className='h-3 w-32 bg-gray-200 rounded'></div>
                </div>
                <div className='ml-auto h-4 w-16 bg-gray-200 rounded'></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='h-full'>
      <CardHeader>
        <CardTitle>最近付款</CardTitle>
        <CardDescription>
          {recentPayments.length > 0
            ? `最近 ${recentPayments.length} 笔付款记录`
            : '暂无付款记录'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {recentPayments.length === 0 ? (
          <div className='flex items-center justify-center h-32 text-muted-foreground'>
            <p>暂无付款记录</p>
          </div>
        ) : (
          <div className='space-y-8'>
            {recentPayments.map((payment) => (
              <div key={payment.id} className='flex items-center'>
                <Avatar className='h-9 w-9'>
                  <AvatarFallback>
                    {payment.clientName.slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                <div className='ml-4 space-y-1'>
                  <p className='text-sm leading-none font-medium'>{payment.clientName}</p>
                  <p className='text-muted-foreground text-sm'>{payment.treatmentName}</p>
                </div>
                <div className='ml-auto font-medium'>${payment.amount.toFixed(2)}</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
