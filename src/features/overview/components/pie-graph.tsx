'use client';


import React, { useEffect, useMemo, useState } from 'react';
import { IconTrendingUp } from '@tabler/icons-react';
import { Label, Pie, PieChart } from 'recharts';
import { logger } from '@/lib/logger';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

// 模拟数据 - 实际应用中应该从API获取
const chartData = [
  { category: '面部护理', count: 275, fill: 'var(--color-面部护理)' },
  { category: '身体护理', count: 200, fill: 'var(--color-身体护理)' },
  { category: '激光治疗', count: 187, fill: 'var(--color-激光治疗)' },
  { category: '其他', count: 173, fill: 'var(--color-其他)' },
];

const chartConfig = {
  count: {
    label: '数量',
  },
  面部护理: {
    label: '面部护理',
    color: 'hsl(var(--chart-1))'
  },
  身体护理: {
    label: '身体护理',
    color: 'hsl(var(--chart-2))'
  },
  激光治疗: {
    label: '激光治疗',
    color: 'hsl(var(--chart-3))'
  },
  其他: {
    label: '其他',
    color: 'hsl(var(--chart-4))'
  }
} satisfies ChartConfig;

export function PieGraph() {
  const [loading, setLoading] = React.useState(true);
  const [data, setData] = React.useState(chartData);

  React.useEffect(() => {
    const fetchTreatmentData = async () => {
      try {
        const response = await fetch('/api/treatments');
        if (response.ok) {
          const data = await response.json();
          const treatments = data.treatments ?? [];

          // 按分类统计治疗项目
          const categoryCount: Record<string, number> = {};
          treatments.forEach((treatment: unknown) => {
            const category = treatment.category ?? '其他';
            categoryCount[category] = (categoryCount[category] || 0) + 1;
          });

          // 转换为图表数据格式
          const chartData = Object.entries(categoryCount).map(([category, count]) => ({
            category,
            count,
            fill: `var(--color-${category})`
          }));

          setData(chartData.length > 0 ? chartData : [
            { category: '暂无数据', count: 1, fill: 'var(--color-其他)' }
          ]);
        }
      } catch (error) {
        logger.error('Error fetching treatment data:', error as Error);
        // 使用默认数据
        setData(chartData);
      } finally {
        setLoading(false);
      }
    };

    fetchTreatmentData();
  }, []);

  const totalTreatments = React.useMemo(() => {
    return data.reduce((acc, curr) => acc + curr.count, 0);
  }, [data]);

  if (loading) {
    return (
      <Card className='flex flex-col'>
        <CardHeader className='items-center pb-0'>
          <CardTitle>治疗项目分布</CardTitle>
          <CardDescription>按类别统计的治疗项目数量</CardDescription>
        </CardHeader>
        <CardContent className='flex-1 pb-0'>
          <div className='flex items-center justify-center h-[250px]'>
            <div className='text-muted-foreground'>加载中...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='flex flex-col'>
      <CardHeader className='items-center pb-0'>
        <CardTitle>治疗项目分布</CardTitle>
        <CardDescription>按类别统计的治疗项目数量</CardDescription>
      </CardHeader>
      <CardContent className='flex-1 pb-0'>
        <ChartContainer
          config={chartConfig}
          className='mx-auto aspect-square max-h-[250px]'
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey='count'
              nameKey='category'
              innerRadius={60}
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor='middle'
                        dominantBaseline='middle'
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className='fill-foreground text-3xl font-bold'
                        >
                          {totalTreatments.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className='fill-muted-foreground'
                        >
                          治疗项目
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className='flex-col gap-2 text-sm'>
        <div className='flex items-center gap-2 font-medium leading-none'>
          本月新增治疗项目 <IconTrendingUp className='h-4 w-4' />
        </div>
        <div className='leading-none text-muted-foreground'>
          显示当前系统中各类治疗项目的分布情况
        </div>
      </CardFooter>
    </Card>
  );
}
