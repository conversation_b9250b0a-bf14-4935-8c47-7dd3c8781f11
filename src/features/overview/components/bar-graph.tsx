'use client';


import React, { useEffect, useMemo, useState } from 'react';
import { Bar, BarChart, CartesianGrid, XAxis } from 'recharts';
import { logger } from '@/lib/logger';
// import { format, subDays } from 'date-fns';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from '@/components/ui/chart';

export function BarGraph() {
  const [chartData, setChartData] = React.useState(() => {
    // 生成最近30天的默认数据
    return Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`,
        revenue: Math.floor(Math.random() * 2000) + 500,
        appointments: Math.floor(Math.random() * 10) + 2
      };
    });
  });
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchRevenueData = async () => {
      try {
        // 获取付款数据
        const paymentsResponse = await fetch('/api/payments');
        if (paymentsResponse.ok) {
          const paymentsData = await paymentsResponse.json();
          const payments = paymentsData.payments ?? [];

          // 按日期统计收入
          const dailyRevenue: Record<string, number> = {};
          payments.forEach((payment: unknown) => {
            const paymentDate = new Date(payment.payment_date);
            const dateKey = `${(paymentDate.getMonth() + 1).toString().padStart(2, '0')}/${paymentDate.getDate().toString().padStart(2, '0')}`;
            dailyRevenue[dateKey] = (dailyRevenue[dateKey] || 0) + payment.amount;
          });

          // 获取预约数据
          const today = new Date();
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(today.getDate() - 30);
          const appointmentsResponse = await fetch(
            `/api/appointments?start_date=${thirtyDaysAgo.toISOString().split('T')[0]}&end_date=${today.toISOString().split('T')[0]}`
          );

          let dailyAppointments: Record<string, number> = {};
          if (appointmentsResponse.ok) {
            const appointmentsData = await appointmentsResponse.json();
            const appointments = appointmentsData.appointments ?? [];

            appointments.forEach((appointment: unknown) => {
              const appointmentDate = new Date(appointment.appointment_date);
              const dateKey = `${(appointmentDate.getMonth() + 1).toString().padStart(2, '0')}/${appointmentDate.getDate().toString().padStart(2, '0')}`;
              dailyAppointments[dateKey] = (dailyAppointments[dateKey] || 0) + 1;
            });
          }

          // 生成图表数据
          const newChartData = Array.from({ length: 30 }, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - (29 - i));
            const dateKey = `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
            return {
              date: dateKey,
              revenue: dailyRevenue[dateKey] || 0,
              appointments: dailyAppointments[dateKey] || 0
            };
          });

          setChartData(newChartData);
        }
      } catch (error) {
        logger.error('Error fetching revenue data:', error as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchRevenueData();
  }, []);

const chartConfig = {
  revenue: {
    label: '收入',
    color: 'hsl(var(--chart-1))'
  },
  appointments: {
    label: '预约数',
    color: 'hsl(var(--chart-2))'
  }
} satisfies ChartConfig;

  const [activeChart, setActiveChart] =
    React.useState<keyof typeof chartConfig>('revenue');

  const total = React.useMemo(
    () => ({
      revenue: chartData.reduce((acc, curr) => acc + curr.revenue, 0),
      appointments: chartData.reduce((acc, curr) => acc + curr.appointments, 0)
    }),
    [chartData]
  );

  if (loading) {
    return (
      <Card>
        <CardHeader className='flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row'>
          <div className='flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6'>
            <CardTitle>每日收入统计</CardTitle>
            <CardDescription>显示最近30天的收入和预约数据</CardDescription>
          </div>
        </CardHeader>
        <CardContent className='px-2 sm:p-6'>
          <div className="flex items-center justify-center h-[250px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className='flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row'>
        <div className='flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6'>
          <CardTitle>每日收入统计</CardTitle>
          <CardDescription>显示最近30天的收入和预约数据</CardDescription>
        </div>
        <div className='flex'>
          {['revenue', 'appointments'].map((key) => {
            const chart = key as keyof typeof chartConfig;
            return (
              <button
                key={chart}
                data-active={activeChart === chart}
                className='relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l data-[active=true]:bg-muted/50 sm:border-l sm:border-t-0 sm:px-8 sm:py-6'
                onClick={() => setActiveChart(chart)}
              >
                <span className='text-xs text-muted-foreground'>
                  {chartConfig[chart].label}
                </span>
                <span className='text-lg font-bold leading-none sm:text-3xl'>
                  {chart === 'revenue' ? `$${total[chart].toLocaleString()}` : total[chart].toLocaleString()}
                </span>
              </button>
            );
          })}
        </div>
      </CardHeader>
      <CardContent className='px-2 sm:p-6'>
        <ChartContainer
          config={chartConfig}
          className='aspect-auto h-[250px] w-full'
        >
          <BarChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey='date'
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className='w-[150px]'
                  nameKey='views'
                  labelFormatter={(value) => {
                    return value;
                  }}
                />
              }
            />
            <Bar dataKey={activeChart} fill={`var(--color-${activeChart})`} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
