'use client';


import React, { useEffect, useState } from 'react';
import { IconTrendingUp } from '@tabler/icons-react';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { logger } from '@/lib/logger';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from '@/components/ui/chart';

export function AreaGraph() {
  const [chartData, setChartData] = React.useState([
    { month: '1月', newClients: 12, appointments: 45 },
    { month: '2月', newClients: 18, appointments: 52 },
    { month: '3月', newClients: 15, appointments: 48 },
    { month: '4月', newClients: 22, appointments: 65 },
    { month: '5月', newClients: 28, appointments: 72 },
    { month: '6月', newClients: 25, appointments: 68 }
  ]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchGrowthData = async () => {
      try {
        // 获取客户数据
        const clientsResponse = await fetch('/api/clients');
        if (clientsResponse.ok) {
          const clientsData = await clientsResponse.json();
          const clients = clientsData.data?.clients ?? [];

          // 获取预约数据
          const appointmentsResponse = await fetch('/api/appointments?start_date=2025-01-01&end_date=2025-12-31');
          if (appointmentsResponse.ok) {
            const appointmentsData = await appointmentsResponse.json();
            const appointments = appointmentsData.appointments ?? [];

            // 按月统计新客户和预约数
            const monthlyStats: Record<string, { newClients: number; appointments: number }> = {};

            // 统计新客户（按创建时间）
            clients.forEach((client: unknown) => {
              const date = new Date(client.created_at);
              const monthKey = `${date.getMonth() + 1}月`;
              if (!monthlyStats[monthKey]) {
                monthlyStats[monthKey] = { newClients: 0, appointments: 0 };
              }
              monthlyStats[monthKey].newClients++;
            });

            // 统计预约数
            appointments.forEach((appointment: unknown) => {
              const date = new Date(appointment.appointment_date);
              const monthKey = `${date.getMonth() + 1}月`;
              if (!monthlyStats[monthKey]) {
                monthlyStats[monthKey] = { newClients: 0, appointments: 0 };
              }
              monthlyStats[monthKey].appointments++;
            });

            // 生成图表数据（最近6个月）
            const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            const currentMonth = new Date().getMonth();
            const recentMonths = [];

            for (let i = 5; i >= 0; i--) {
              const monthIndex = (currentMonth - i + 12) % 12;
              recentMonths.push(months[monthIndex]);
            }

            const newChartData = recentMonths.map(month => ({
              month,
              newClients: monthlyStats[month]?.newClients ?? 0,
              appointments: monthlyStats[month]?.appointments ?? 0
            }));

            if (newChartData.some(data => data.newClients > 0 || data.appointments > 0)) {
              setChartData(newChartData);
            }
          }
        }
      } catch (error) {
        logger.error('Error fetching growth data:', error as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchGrowthData();
  }, []);

const chartConfig = {
  newClients: {
    label: '新客户',
    color: 'hsl(var(--chart-1))'
  },
  appointments: {
    label: '预约数',
    color: 'hsl(var(--chart-2))'
  }
} satisfies ChartConfig;

  if (loading) {
    return (
      <Card className='@container/card'>
        <CardHeader>
          <CardTitle>业务增长趋势</CardTitle>
          <CardDescription>加载中...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[250px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='@container/card'>
      <CardHeader>
        <CardTitle>业务增长趋势</CardTitle>
        <CardDescription>
          显示最近6个月的新客户和预约增长情况
        </CardDescription>
      </CardHeader>
      <CardContent className='px-2 pt-4 sm:px-6 sm:pt-6'>
        <ChartContainer
          config={chartConfig}
          className='aspect-auto h-[250px] w-full'
        >
          <AreaChart
            data={chartData}
            margin={{
              left: 12,
              right: 12
            }}
          >
            <defs>
              <linearGradient id='fillNewClients' x1='0' y1='0' x2='0' y2='1'>
                <stop
                  offset='5%'
                  stopColor='var(--color-newClients)'
                  stopOpacity={0.8}
                />
                <stop
                  offset='95%'
                  stopColor='var(--color-newClients)'
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id='fillAppointments' x1='0' y1='0' x2='0' y2='1'>
                <stop
                  offset='5%'
                  stopColor='var(--color-appointments)'
                  stopOpacity={0.8}
                />
                <stop
                  offset='95%'
                  stopColor='var(--color-appointments)'
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey='month'
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator='dot' />}
            />
            <Area
              dataKey='appointments'
              type='natural'
              fill='url(#fillAppointments)'
              stroke='var(--color-appointments)'
              stackId='a'
            />
            <Area
              dataKey='newClients'
              type='natural'
              fill='url(#fillNewClients)'
              stroke='var(--color-newClients)'
              stackId='a'
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className='flex w-full items-start gap-2 text-sm'>
          <div className='grid gap-2'>
            <div className='flex items-center gap-2 leading-none font-medium'>
              业务持续增长 <IconTrendingUp className='h-4 w-4' />
            </div>
            <div className='text-muted-foreground flex items-center gap-2 leading-none'>
              显示最近6个月的新客户获取和预约趋势
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
