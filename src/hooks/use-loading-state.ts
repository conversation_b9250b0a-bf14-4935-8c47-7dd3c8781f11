/**
 * Loading State Management Hook
 * Provides comprehensive loading state management for async operations
 */

import { useState, useCallback, useRef } from 'react';
import { logger } from '@/lib/logger';

// Loading state interface
interface LoadingState {
  isLoading: boolean;
  error: Error | null;
  data: unknown;
}

// Loading operation options
interface LoadingOptions {
  showToast?: boolean;
  logErrors?: boolean;
  retries?: number;
  retryDelay?: number;
  timeout?: number;
}

// Multiple loading states for different operations
interface MultipleLoadingStates {
  [key: string]: LoadingState;
}

// Single loading state hook
export function useLoadingState<T = unknown>(initialData?: T) {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    error: null,
    data: initialData ?? null
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const execute = useCallback(async <R = T>(
    operation: (signal?: AbortSignal) => Promise<R>,
    options: LoadingOptions = {}
  ): Promise<R | null> => {
    const {
      showToast = true,
      logErrors = true,
      retries = 0,
      retryDelay = 1000,
      timeout = 30000
    } = options;

    // Cancel previous operation if still running
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    let attempt = 0;
    const maxAttempts = retries + 1;

    while (attempt < maxAttempts) {
      try {
        // Set timeout
        const timeoutId = setTimeout(() => {
          abortControllerRef.current?.abort();
        }, timeout);

        const result = await operation(signal);
        
        clearTimeout(timeoutId);

        setState({
          isLoading: false,
          error: null,
          data: result
        });

        return result;
      } catch (error) {
        attempt++;
        
        if (signal.aborted) {
          setState({
            isLoading: false,
            error: new Error('操作已取消'),
            data: null
          });
          return null;
        }

        if (attempt >= maxAttempts) {
          const finalError = error instanceof Error ? error : new Error(String(error));
          
          setState({
            isLoading: false,
            error: finalError,
            data: null
          });

          if (logErrors) {
            logger.error('Loading operation failed', {
              error: finalError,
              attempts: attempt,
              options
            });
          }

          return null;
        }

        // Wait before retry
        if (retryDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }

        if (logErrors) {
          logger.warn(`Loading operation failed, retrying (${attempt}/${maxAttempts})`, {
            error: error instanceof Error ? error : new Error(String(error)),
            attempt
          });
        }
      }
    }

    return null;
  }, []);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState({
      isLoading: false,
      error: null,
      data: null
    });
  }, []);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    ...state,
    execute,
    reset,
    cancel
  };
}

// Multiple loading states hook
export function useMultipleLoadingStates() {
  const [states, setStates] = useState<MultipleLoadingStates>({});
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());

  const execute = useCallback(async <T>(
    key: string,
    operation: (signal?: AbortSignal) => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T | null> => {
    const {
      showToast = true,
      logErrors = true,
      retries = 0,
      retryDelay = 1000,
      timeout = 30000
    } = options;

    // Cancel previous operation for this key if still running
    const existingController = abortControllersRef.current.get(key);
    if (existingController) {
      existingController.abort();
    }

    // Create new abort controller for this key
    const abortController = new AbortController();
    abortControllersRef.current.set(key, abortController);
    const signal = abortController.signal;

    setStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        error: null,
        data: prev[key]?.data ?? null
      }
    }));

    let attempt = 0;
    const maxAttempts = retries + 1;

    while (attempt < maxAttempts) {
      try {
        // Set timeout
        const timeoutId = setTimeout(() => {
          abortController.abort();
        }, timeout);

        const result = await operation(signal);
        
        clearTimeout(timeoutId);

        setStates(prev => ({
          ...prev,
          [key]: {
            isLoading: false,
            error: null,
            data: result
          }
        }));

        abortControllersRef.current.delete(key);
        return result;
      } catch (error) {
        attempt++;
        
        if (signal.aborted) {
          setStates(prev => ({
            ...prev,
            [key]: {
              isLoading: false,
              error: new Error('操作已取消'),
              data: null
            }
          }));
          abortControllersRef.current.delete(key);
          return null;
        }

        if (attempt >= maxAttempts) {
          const finalError = error instanceof Error ? error : new Error(String(error));
          
          setStates(prev => ({
            ...prev,
            [key]: {
              isLoading: false,
              error: finalError,
              data: null
            }
          }));

          if (logErrors) {
            logger.error(`Loading operation failed for key: ${key}`, {
              error: finalError,
              attempts: attempt,
              options
            });
          }

          abortControllersRef.current.delete(key);
          return null;
        }

        // Wait before retry
        if (retryDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }

        if (logErrors) {
          logger.warn(`Loading operation failed for key: ${key}, retrying (${attempt}/${maxAttempts})`, {
            error: error instanceof Error ? error : new Error(String(error)),
            attempt
          });
        }
      }
    }

    return null;
  }, []);

  const reset = useCallback((key?: string) => {
    if (key) {
      const controller = abortControllersRef.current.get(key);
      if (controller) {
        controller.abort();
        abortControllersRef.current.delete(key);
      }
      setStates(prev => {
        const newStates = { ...prev };
        delete newStates[key];
        return newStates;
      });
    } else {
      // Reset all states
      abortControllersRef.current.forEach(controller => controller.abort());
      abortControllersRef.current.clear();
      setStates({});
    }
  }, []);

  const cancel = useCallback((key?: string) => {
    if (key) {
      const controller = abortControllersRef.current.get(key);
      if (controller) {
        controller.abort();
      }
    } else {
      // Cancel all operations
      abortControllersRef.current.forEach(controller => controller.abort());
    }
  }, []);

  const getState = useCallback((key: string): LoadingState => {
    return states[key] ?? {
      isLoading: false,
      error: null,
      data: null
    };
  }, [states]);

  return {
    states,
    execute,
    reset,
    cancel,
    getState
  };
}

// Loading state context for global loading management
export interface LoadingContextValue {
  globalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;
  loadingOperations: Set<string>;
  startOperation: (operationId: string) => void;
  endOperation: (operationId: string) => void;
}

// Global loading state hook
export function useGlobalLoadingState() {
  const [loadingOperations, setLoadingOperations] = useState<Set<string>>(new Set());

  const startOperation = useCallback((operationId: string) => {
    setLoadingOperations(prev => new Set(prev).add(operationId));
  }, []);

  const endOperation = useCallback((operationId: string) => {
    setLoadingOperations(prev => {
      const newSet = new Set(prev);
      newSet.delete(operationId);
      return newSet;
    });
  }, []);

  const setGlobalLoading = useCallback((loading: boolean) => {
    if (!loading) {
      setLoadingOperations(new Set());
    }
  }, []);

  return {
    globalLoading: loadingOperations.size > 0,
    setGlobalLoading,
    loadingOperations,
    startOperation,
    endOperation
  };
}
