/**
 * Error Handler Unit Tests
 * Tests for the enhanced error handling system
 */

import { 
  AppError, 
  ErrorType, 
  ErrorSeverity, 
  classifyError, 
  handleError, 
  withErrorHandling, 
  apiCall 
} from '@/lib/error-handler';
import { logger } from '@/lib/logger';
import { showErrorToast } from '@/lib/toast-utils';

// Mock dependencies
jest.mock('@/lib/logger');
jest.mock('@/lib/toast-utils');

const mockLogger = logger as jest.Mocked<typeof logger>;
const mockShowErrorToast = showErrorToast as jest.MockedFunction<typeof showErrorToast>;

describe('Error Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AppError', () => {
    it('should create AppError with default values', () => {
      const error = new AppError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.type).toBe(ErrorType.UNKNOWN);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.code).toBeUndefined();
      expect(error.details).toBeUndefined();
      expect(error.timestamp).toBeInstanceOf(Date);
    });

    it('should create AppError with custom values', () => {
      const details = { userId: '123' };
      const error = new AppError(
        'Validation failed',
        ErrorType.VALIDATION,
        ErrorSeverity.LOW,
        'VALIDATION_001',
        details
      );
      
      expect(error.message).toBe('Validation failed');
      expect(error.type).toBe(ErrorType.VALIDATION);
      expect(error.severity).toBe(ErrorSeverity.LOW);
      expect(error.code).toBe('VALIDATION_001');
      expect(error.details).toBe(details);
    });
  });

  describe('classifyError', () => {
    it('should return AppError as-is', () => {
      const appError = new AppError('Test', ErrorType.NETWORK);
      const result = classifyError(appError);
      
      expect(result).toBe(appError);
    });

    it('should classify network errors', () => {
      const networkError = new Error('fetch failed');
      const result = classifyError(networkError);
      
      expect(result.type).toBe(ErrorType.NETWORK);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.code).toBe('NETWORK_ERROR');
    });

    it('should classify validation errors', () => {
      const validationError = new Error('validation failed');
      const result = classifyError(validationError);
      
      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.severity).toBe(ErrorSeverity.LOW);
      expect(result.code).toBe('VALIDATION_ERROR');
    });

    it('should classify authentication errors', () => {
      const authError = new Error('unauthorized access');
      const result = classifyError(authError);
      
      expect(result.type).toBe(ErrorType.AUTHENTICATION);
      expect(result.severity).toBe(ErrorSeverity.HIGH);
      expect(result.code).toBe('AUTH_ERROR');
    });

    it('should classify unknown errors', () => {
      const unknownError = new Error('something went wrong');
      const result = classifyError(unknownError);
      
      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.code).toBe('GENERIC_ERROR');
    });

    it('should handle non-Error objects', () => {
      const result = classifyError('string error');
      
      expect(result.message).toBe('string error');
      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.code).toBe('UNKNOWN_ERROR');
    });
  });

  describe('handleError', () => {
    it('should handle error with default options', () => {
      const error = new Error('Test error');
      const result = handleError(error);
      
      expect(result).toBeInstanceOf(AppError);
      expect(mockLogger.warn).toHaveBeenCalled();
      expect(mockShowErrorToast).toHaveBeenCalledWith('操作失败', expect.any(String));
    });

    it('should not show toast when showToast is false', () => {
      const error = new Error('Test error');
      handleError(error, { showToast: false });
      
      expect(mockShowErrorToast).not.toHaveBeenCalled();
    });

    it('should not log when logError is false', () => {
      const error = new Error('Test error');
      handleError(error, { logError: false });
      
      expect(mockLogger.warn).not.toHaveBeenCalled();
    });

    it('should use fallback message', () => {
      const error = new Error('Test error');
      const fallbackMessage = 'Custom error message';
      handleError(error, { fallbackMessage });
      
      expect(mockShowErrorToast).toHaveBeenCalledWith('操作失败', fallbackMessage);
    });

    it('should log critical errors', () => {
      const criticalError = new AppError('Critical', ErrorType.SERVER, ErrorSeverity.CRITICAL);
      handleError(criticalError);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('CRITICAL ERROR'),
        expect.any(Object)
      );
    });
  });

  describe('withErrorHandling', () => {
    it('should execute operation successfully', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      const result = await withErrorHandling(operation);
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
    });

    it('should handle operation failure', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Operation failed'));
      const result = await withErrorHandling(operation);
      
      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalled();
    });

    it('should pass options to error handler', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Operation failed'));
      await withErrorHandling(operation, { showToast: false });
      
      expect(mockShowErrorToast).not.toHaveBeenCalled();
    });
  });

  describe('apiCall', () => {
    beforeEach(() => {
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should make successful API call', async () => {
      const mockResponse = { data: 'test' };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await apiCall('/api/test');
      
      expect(result).toEqual(mockResponse);
      expect(global.fetch).toHaveBeenCalledWith('/api/test', {
        headers: { 'Content-Type': 'application/json' }
      });
    });

    it('should handle 400 validation error', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: () => Promise.resolve({ message: 'Validation failed' })
      });

      const result = await apiCall('/api/test');
      
      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalled();
    });

    it('should handle 401 authentication error', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: () => Promise.resolve({ message: 'Unauthorized' })
      });

      const result = await apiCall('/api/test');
      
      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle 500 server error', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () => Promise.resolve({ message: 'Server error' })
      });

      const result = await apiCall('/api/test');
      
      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle network error', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await apiCall('/api/test');
      
      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalled();
    });

    it('should pass custom headers', async () => {
      const mockResponse = { data: 'test' };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      await apiCall('/api/test', {
        headers: { 'Authorization': 'Bearer token' }
      });
      
      expect(global.fetch).toHaveBeenCalledWith('/api/test', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token'
        }
      });
    });
  });
});
