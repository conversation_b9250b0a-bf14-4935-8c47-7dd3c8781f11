import { test, expect } from '@playwright/test'

test.describe('Appointment Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the appointments page
    await page.goto('/dashboard/appointments')
    await page.waitForLoadState('networkidle')
  })

  test('should create a new appointment successfully', async ({ page }) => {
    // Click on a time slot in the calendar to create appointment
    await page.click('.rbc-time-slot')
    
    // Wait for appointment modal to appear
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('text=新建预约')).toBeVisible()

    // Fill in the appointment form
    // Select client
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户') // Assuming a test client exists

    // Select treatment
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理') // Assuming a test treatment exists

    // Set appointment date and time
    await page.fill('input[name="appointment_date"]', '2025-07-20')
    await page.fill('input[name="start_time"]', '10:00')
    await page.fill('input[name="end_time"]', '11:30')

    // Add notes
    await page.fill('textarea[name="notes"]', '客户首次预约，需要详细咨询')

    // Submit the form
    await page.click('button:has-text("创建预约")')

    // Wait for success message
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Verify the modal closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()

    // Verify the appointment appears in the calendar
    await expect(page.locator('text=面部护理')).toBeVisible()
  })

  test('should detect appointment conflicts', async ({ page }) => {
    // Create first appointment
    await page.click('.rbc-time-slot')
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-21')
    await page.fill('input[name="start_time"]', '14:00')
    await page.fill('input[name="end_time"]', '15:30')
    await page.click('button:has-text("创建预约")')
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Try to create conflicting appointment
    await page.click('.rbc-time-slot')
    await page.click('[data-testid="client-select"]')
    await page.click('text=其他 客户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=身体护理')
    await page.fill('input[name="appointment_date"]', '2025-07-21')
    await page.fill('input[name="start_time"]', '14:30') // Overlapping time
    await page.fill('input[name="end_time"]', '16:00')
    await page.click('button:has-text("创建预约")')

    // Should show conflict error
    await expect(page.locator('text=该时间段已有预约冲突')).toBeVisible()
  })

  test('should edit existing appointment', async ({ page }) => {
    // First create an appointment
    await page.click('.rbc-time-slot')
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-22')
    await page.fill('input[name="start_time"]', '09:00')
    await page.fill('input[name="end_time"]', '10:30')
    await page.click('button:has-text("创建预约")')
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Click on the created appointment to edit
    await page.click('.rbc-event:has-text("面部护理")')
    
    // Wait for edit modal
    await expect(page.locator('text=编辑预约')).toBeVisible()

    // Modify the end time
    await page.fill('input[name="end_time"]', '11:00')

    // Update notes
    await page.fill('textarea[name="notes"]', '时间已调整为11点结束')

    // Submit the update
    await page.click('button:has-text("更新预约")')

    // Wait for success message
    await expect(page.locator('text=预约更新成功')).toBeVisible()

    // Verify the modal closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should change appointment status', async ({ page }) => {
    // Create an appointment first
    await page.click('.rbc-time-slot')
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-23')
    await page.fill('input[name="start_time"]', '11:00')
    await page.fill('input[name="end_time"]', '12:30')
    await page.click('button:has-text("创建预约")')
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Click on the appointment to edit
    await page.click('.rbc-event:has-text("面部护理")')
    
    // Change status to confirmed
    await page.click('[data-testid="status-select"]')
    await page.click('text=已确认')

    // Submit the update
    await page.click('button:has-text("更新预约")')

    // Wait for success message
    await expect(page.locator('text=预约更新成功')).toBeVisible()

    // Verify status change is reflected (appointments with different statuses might have different colors)
    await expect(page.locator('.rbc-event.confirmed')).toBeVisible()
  })

  test('should validate appointment form fields', async ({ page }) => {
    await page.click('.rbc-time-slot')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Try to submit without filling required fields
    await page.click('button:has-text("创建预约")')

    // Check for validation errors
    await expect(page.locator('text=请选择客户')).toBeVisible()
    await expect(page.locator('text=请选择治疗项目')).toBeVisible()
    await expect(page.locator('text=请选择预约日期')).toBeVisible()
  })

  test('should validate appointment time logic', async ({ page }) => {
    await page.click('.rbc-time-slot')
    
    // Fill in basic info
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-24')

    // Set end time before start time
    await page.fill('input[name="start_time"]', '15:00')
    await page.fill('input[name="end_time"]', '14:00')

    await page.click('button:has-text("创建预约")')

    // Should show time validation error
    await expect(page.locator('text=结束时间必须晚于开始时间')).toBeVisible()
  })

  test('should handle custom pricing', async ({ page }) => {
    await page.click('.rbc-time-slot')
    
    // Fill in basic appointment info
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-25')
    await page.fill('input[name="start_time"]', '13:00')
    await page.fill('input[name="end_time"]', '14:30')

    // Enable custom pricing
    await page.check('input[name="use_custom_price"]')

    // Set custom price
    await page.fill('input[name="custom_price"]', '350.00')

    await page.click('button:has-text("创建预约")')

    // Wait for success
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Verify custom price is saved (could check in appointment details)
    await page.click('.rbc-event:has-text("面部护理")')
    await expect(page.locator('text=$350.00')).toBeVisible()
  })

  test('should navigate between calendar views', async ({ page }) => {
    // Test month view (default)
    await expect(page.locator('.rbc-toolbar button.rbc-active:has-text("月")')).toBeVisible()

    // Switch to week view
    await page.click('.rbc-toolbar button:has-text("周")')
    await expect(page.locator('.rbc-time-view')).toBeVisible()

    // Switch to day view
    await page.click('.rbc-toolbar button:has-text("日")')
    await expect(page.locator('.rbc-time-view')).toBeVisible()

    // Navigate to next day
    await page.click('.rbc-toolbar button:has-text("下一个")')
    
    // Navigate to previous day
    await page.click('.rbc-toolbar button:has-text("上一个")')

    // Go to today
    await page.click('.rbc-toolbar button:has-text("今天")')
  })

  test('should filter appointments by status', async ({ page }) => {
    // Create appointments with different statuses first
    const appointments = [
      { status: 'scheduled', time: '09:00-10:30' },
      { status: 'confirmed', time: '11:00-12:30' },
      { status: 'completed', time: '14:00-15:30' },
    ]

    for (const apt of appointments) {
      await page.click('.rbc-time-slot')
      await page.click('[data-testid="client-select"]')
      await page.click('text=测试 用户')
      await page.click('[data-testid="treatment-select"]')
      await page.click('text=面部护理')
      await page.fill('input[name="appointment_date"]', '2025-07-26')
      const [start, end] = apt.time.split('-')
      await page.fill('input[name="start_time"]', start)
      await page.fill('input[name="end_time"]', end)
      
      // Set status
      await page.click('[data-testid="status-select"]')
      await page.click(`text=${apt.status === 'scheduled' ? '已安排' : apt.status === 'confirmed' ? '已确认' : '已完成'}`)
      
      await page.click('button:has-text("创建预约")')
      await expect(page.locator('text=预约创建成功')).toBeVisible()
    }

    // Test status filter
    await page.click('[data-testid="status-filter"]')
    await page.click('text=已确认')

    // Should only show confirmed appointments
    await expect(page.locator('.rbc-event.confirmed')).toBeVisible()
    await expect(page.locator('.rbc-event.scheduled')).not.toBeVisible()
  })

  test('should cancel appointment', async ({ page }) => {
    // Create an appointment first
    await page.click('.rbc-time-slot')
    await page.click('[data-testid="client-select"]')
    await page.click('text=测试 用户')
    await page.click('[data-testid="treatment-select"]')
    await page.click('text=面部护理')
    await page.fill('input[name="appointment_date"]', '2025-07-27')
    await page.fill('input[name="start_time"]', '10:00')
    await page.fill('input[name="end_time"]', '11:30')
    await page.click('button:has-text("创建预约")')
    await expect(page.locator('text=预约创建成功')).toBeVisible()

    // Click on the appointment to edit
    await page.click('.rbc-event:has-text("面部护理")')
    
    // Change status to cancelled
    await page.click('[data-testid="status-select"]')
    await page.click('text=已取消')

    // Add cancellation reason
    await page.fill('textarea[name="notes"]', '客户临时有事，需要取消预约')

    // Submit the update
    await page.click('button:has-text("更新预约")')

    // Wait for success message
    await expect(page.locator('text=预约更新成功')).toBeVisible()

    // Cancelled appointments might be hidden or shown differently
    await expect(page.locator('.rbc-event.cancelled')).toBeVisible()
  })
})
