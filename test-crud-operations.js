#!/usr/bin/env node

// CRUD操作测试脚本
const http = require('http');

const BASE_URL = 'http://localhost:3001';

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// 测试数据存储
let testClientId = null;
let testTreatmentId = null;
let testAppointmentId = null;
let testInvoiceId = null;
let testPaymentId = null;

// HTTP请求工具函数
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试函数
async function runTest(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`);
    await testFn();
    console.log(`✅ 通过: ${name}`);
    testResults.passed++;
    testResults.tests.push({ name, status: 'PASSED' });
  } catch (error) {
    console.log(`❌ 失败: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, status: 'FAILED', error: error.message });
  }
}

// 断言函数
function assert(condition, message) {
  if (!condition) {
    throw new Error(message);
  }
}

// 客户CRUD测试
async function testCreateClient() {
  const clientData = {
    first_name: '测试',
    last_name: '客户CRUD',
    phone: '5551234567',
    email: '<EMAIL>',
    address_line_1: '123 Test Street',
    city: 'Test City',
    state_province: 'TS',
    postal_code: '12345',
    country: '美国'
  };

  const response = await makeRequest('/api/clients', 'POST', clientData);
  assert(response.status === 201, '创建客户应该返回201状态码');
  assert(response.data.success === true, '创建客户应该返回成功状态');
  assert(response.data.data.client.id, '创建客户应该返回客户ID');
  
  testClientId = response.data.data.client.id;
  console.log(`   📊 创建客户ID: ${testClientId}`);
}

async function testReadClient() {
  assert(testClientId, '需要先创建客户');
  
  const response = await makeRequest(`/api/clients/${testClientId}`);
  assert(response.status === 200, '读取客户应该返回200状态码');
  assert(response.data.client, '读取客户应该返回客户对象');
  assert(response.data.client.first_name === '测试', '客户姓名应该匹配');

  console.log(`   📊 读取客户: ${response.data.client.first_name} ${response.data.client.last_name}`);
}

async function testUpdateClient() {
  assert(testClientId, '需要先创建客户');
  
  const updateData = {
    first_name: '更新测试',
    notes: '这是一个CRUD测试客户'
  };

  const response = await makeRequest(`/api/clients/${testClientId}`, 'PUT', updateData);
  assert(response.status === 200, '更新客户应该返回200状态码');
  assert(response.data.client, '更新客户应该返回客户对象');
  
  console.log(`   📊 更新客户成功`);
}

// 治疗项目CRUD测试
async function testCreateTreatment() {
  const treatmentData = {
    name: 'Test Treatment CRUD',
    name_chinese: '测试治疗CRUD',
    description: 'Test treatment for CRUD operations',
    description_chinese: 'CRUD操作测试治疗',
    default_price: 199.99,
    duration_minutes: 45,
    category: '测试类',
    fixed_deposit_amount: 99.99,
    consultation_fee: 39.99
  };

  const response = await makeRequest('/api/treatments', 'POST', treatmentData);
  assert(response.status === 201, '创建治疗项目应该返回201状态码');
  assert(response.data.treatment, '创建治疗项目应该返回治疗项目对象');
  assert(response.data.treatment.id, '创建治疗项目应该返回ID');

  testTreatmentId = response.data.treatment.id;
  console.log(`   📊 创建治疗项目ID: ${testTreatmentId}`);
}

async function testReadTreatment() {
  assert(testTreatmentId, '需要先创建治疗项目');
  
  const response = await makeRequest(`/api/treatments/${testTreatmentId}`);
  assert(response.status === 200, '读取治疗项目应该返回200状态码');
  assert(response.data.treatment, '读取治疗项目应该返回治疗项目对象');
  assert(response.data.treatment.name_chinese === '测试治疗CRUD', '治疗项目名称应该匹配');

  console.log(`   📊 读取治疗项目: ${response.data.treatment.name_chinese}`);
}

// 预约CRUD测试
async function testCreateAppointment() {
  assert(testClientId && testTreatmentId, '需要先创建客户和治疗项目');
  
  const appointmentData = {
    client_id: testClientId,
    treatment_id: testTreatmentId,
    appointment_date: '2025-08-01',
    start_time: '10:00',
    end_time: '10:45',
    appointment_type: 'treatment',
    status: 'scheduled',
    notes: 'CRUD测试预约'
  };

  const response = await makeRequest('/api/appointments', 'POST', appointmentData);
  assert(response.status === 201, '创建预约应该返回201状态码');
  assert(response.data.appointment, '创建预约应该返回预约对象');
  assert(response.data.appointment.id, '创建预约应该返回ID');

  testAppointmentId = response.data.appointment.id;
  console.log(`   📊 创建预约ID: ${testAppointmentId}`);
}

// 账单CRUD测试
async function testCreateInvoice() {
  assert(testClientId && testTreatmentId, '需要先创建客户和治疗项目');
  
  const invoiceData = {
    client_id: testClientId,
    treatment_date: '2025-08-01',
    total_amount: 199.99,
    deposit_percentage: 50
  };

  const response = await makeRequest('/api/invoices', 'POST', invoiceData);
  assert(response.status === 201, '创建账单应该返回201状态码');
  assert(response.data.invoice, '创建账单应该返回账单对象');
  assert(response.data.invoice.id, '创建账单应该返回ID');

  testInvoiceId = response.data.invoice.id;
  console.log(`   📊 创建账单ID: ${testInvoiceId}`);
}

// 付款CRUD测试
async function testCreatePayment() {
  assert(testInvoiceId && testClientId, '需要先创建账单和客户');
  
  const paymentData = {
    invoice_id: testInvoiceId,
    client_id: testClientId,
    amount: 99.99,
    payment_method: 'credit_card',
    payment_type: 'deposit',
    payment_date: '2025-08-01'
  };

  const response = await makeRequest('/api/payments', 'POST', paymentData);
  assert(response.status === 201, '创建付款应该返回201状态码');
  assert(response.data.payment, '创建付款应该返回付款对象');
  assert(response.data.payment.id, '创建付款应该返回ID');

  testPaymentId = response.data.payment.id;
  console.log(`   📊 创建付款ID: ${testPaymentId}`);
}

// 清理测试数据
async function cleanupTestData() {
  const cleanupTasks = [];
  
  if (testPaymentId) {
    cleanupTasks.push(makeRequest(`/api/payments/${testPaymentId}`, 'DELETE'));
  }
  if (testInvoiceId) {
    cleanupTasks.push(makeRequest(`/api/invoices/${testInvoiceId}`, 'DELETE'));
  }
  if (testAppointmentId) {
    cleanupTasks.push(makeRequest(`/api/appointments/${testAppointmentId}`, 'DELETE'));
  }
  if (testTreatmentId) {
    cleanupTasks.push(makeRequest(`/api/treatments/${testTreatmentId}`, 'DELETE'));
  }
  if (testClientId) {
    cleanupTasks.push(makeRequest(`/api/clients/${testClientId}`, 'DELETE'));
  }

  await Promise.allSettled(cleanupTasks);
  console.log('   🧹 测试数据清理完成');
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始CRUD操作测试...\n');

  // 创建操作测试
  await runTest('创建客户', testCreateClient);
  await runTest('创建治疗项目', testCreateTreatment);
  await runTest('创建预约', testCreateAppointment);
  await runTest('创建账单', testCreateInvoice);
  await runTest('创建付款', testCreatePayment);

  // 读取操作测试
  await runTest('读取客户', testReadClient);
  await runTest('读取治疗项目', testReadTreatment);

  // 更新操作测试
  await runTest('更新客户', testUpdateClient);

  // 清理测试数据
  await runTest('清理测试数据', cleanupTestData);

  console.log('\n📊 CRUD测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests.filter(t => t.status === 'FAILED').forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }

  return testResults.failed === 0;
}

// 运行测试
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行出错:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests };
