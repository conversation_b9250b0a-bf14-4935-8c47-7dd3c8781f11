#!/usr/bin/env node

/**
 * React Hooks Dependencies Fixer
 * Fixes common React hooks dependency issues
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to exclude from replacement
const excludeFiles = [
  'scripts/',
  'test-',
  'jest.setup.js',
  'node_modules/',
  '.next/',
  'dist/',
  'build/'
];

function shouldExcludeFile(filePath) {
  return excludeFiles.some(exclude => filePath.includes(exclude));
}

function addUseCallbackImport(content) {
  // Check if useCallback is used but not imported
  const hasUseCallback = /useCallback\s*\(/.test(content);
  const hasUseCallbackImport = /import.*useCallback.*from ['"]react['"]/.test(content) ||
                               /import.*{[^}]*useCallback[^}]*}.*from ['"]react['"]/.test(content);
  
  if (hasUseCallback && !hasUseCallbackImport) {
    // Add useCallback to existing React import or create new import
    if (/import.*{[^}]*}.*from ['"]react['"]/.test(content)) {
      // Add to existing import
      content = content.replace(
        /import\s*{([^}]*)}\s*from\s*['"]react['"]/,
        (match, imports) => {
          if (!imports.includes('useCallback')) {
            const cleanImports = imports.trim();
            const newImports = cleanImports ? `${cleanImports}, useCallback` : 'useCallback';
            return `import { ${newImports} } from 'react'`;
          }
          return match;
        }
      );
    } else if (/import\s+React\s+from\s+['"]react['"]/.test(content)) {
      // Already has React import, add useCallback import
      content = content.replace(
        /import\s+React\s+from\s+['"]react['"]/,
        "import React, { useCallback } from 'react'"
      );
    } else {
      // Add new import at the top
      const lines = content.split('\n');
      let insertIndex = 0;
      
      // Find the first import or the beginning
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim().startsWith('import ')) {
          insertIndex = i;
          break;
        }
      }
      
      lines.splice(insertIndex, 0, "import { useCallback } from 'react';");
      content = lines.join('\n');
    }
  }
  
  return content;
}

function fixHooksDependencies(content) {
  let newContent = content;
  let hasChanges = false;
  const appliedFixes = [];

  // Fix useEffect dependencies
  const useEffectRegex = /useEffect\s*\(\s*\(\s*\)\s*=>\s*{[\s\S]*?},\s*\[\s*\]\s*\)/g;
  const useEffectMatches = newContent.match(useEffectRegex);
  
  if (useEffectMatches) {
    useEffectMatches.forEach(match => {
      // Extract function body to analyze dependencies
      const functionBodyMatch = match.match(/useEffect\s*\(\s*\(\s*\)\s*=>\s*{([\s\S]*?)},\s*\[\s*\]\s*\)/);
      if (functionBodyMatch) {
        const functionBody = functionBodyMatch[1];
        
        // Look for function calls that should be in dependencies
        const functionCalls = functionBody.match(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g);
        if (functionCalls) {
          const dependencies = [];
          functionCalls.forEach(call => {
            const funcName = call.replace(/\s*\($/, '');
            // Skip built-in functions and common React functions
            if (!['fetch', 'setTimeout', 'setInterval', 'console', 'JSON', 'Math', 'Date', 'Object', 'Array'].includes(funcName) &&
                !funcName.startsWith('set') && // Skip state setters
                funcName.length > 2) {
              dependencies.push(funcName);
            }
          });
          
          if (dependencies.length > 0) {
            const uniqueDeps = [...new Set(dependencies)];
            const newMatch = match.replace(/\[\s*\]/, `[${uniqueDeps.join(', ')}]`);
            newContent = newContent.replace(match, newMatch);
            hasChanges = true;
            appliedFixes.push(`Added dependencies to useEffect: ${uniqueDeps.join(', ')}`);
          }
        }
      }
    });
  }

  // Fix useCallback dependencies
  const useCallbackRegex = /useCallback\s*\(\s*\([^)]*\)\s*=>\s*{[\s\S]*?},\s*\[\s*\]\s*\)/g;
  const useCallbackMatches = newContent.match(useCallbackRegex);
  
  if (useCallbackMatches) {
    useCallbackMatches.forEach(match => {
      // Extract function body to analyze dependencies
      const functionBodyMatch = match.match(/useCallback\s*\(\s*\([^)]*\)\s*=>\s*{([\s\S]*?)},\s*\[\s*\]\s*\)/);
      if (functionBodyMatch) {
        const functionBody = functionBodyMatch[1];
        
        // Look for variables that should be in dependencies
        const variableReferences = functionBody.match(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g);
        if (variableReferences) {
          const dependencies = [];
          variableReferences.forEach(variable => {
            // Skip keywords, built-ins, and common patterns
            if (!['const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'true', 'false', 'null', 'undefined'].includes(variable) &&
                !variable.startsWith('set') && // Skip state setters
                variable.length > 2) {
              dependencies.push(variable);
            }
          });
          
          if (dependencies.length > 0) {
            const uniqueDeps = [...new Set(dependencies)];
            const newMatch = match.replace(/\[\s*\]/, `[${uniqueDeps.join(', ')}]`);
            newContent = newContent.replace(match, newMatch);
            hasChanges = true;
            appliedFixes.push(`Added dependencies to useCallback: ${uniqueDeps.join(', ')}`);
          }
        }
      }
    });
  }

  // Wrap functions in useCallback if they're used in useEffect dependencies
  const functionsToWrap = [];
  const useEffectWithDepsRegex = /useEffect\s*\(\s*[^,]+,\s*\[([^\]]+)\]\s*\)/g;
  let match;
  
  while ((match = useEffectWithDepsRegex.exec(newContent)) !== null) {
    const deps = match[1].split(',').map(dep => dep.trim());
    deps.forEach(dep => {
      if (dep && !dep.includes('.') && !dep.includes('[') && !dep.includes('(')) {
        functionsToWrap.push(dep);
      }
    });
  }

  // Add useCallback import if needed
  if (hasChanges) {
    newContent = addUseCallbackImport(newContent);
  }

  return { content: newContent, hasChanges, appliedFixes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Only process React component files
    if (!content.includes('useEffect') && !content.includes('useCallback')) {
      return { changed: false, fixes: [] };
    }

    const result = fixHooksDependencies(content);
    
    // Write back if changes were made
    if (result.hasChanges) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      return { changed: true, fixes: result.appliedFixes };
    }

    return { changed: false, fixes: [] };
  } catch (error) {
    log(`Error processing ${filePath}: ${error.message}`, 'red');
    return { changed: false, fixes: [], error: error.message };
  }
}

function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    totalFixes: 0,
    fixesByType: {},
    errors: []
  };

  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // Process React component files
          if ((item.endsWith('.tsx') || item.endsWith('.jsx')) && !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = processFile(itemPath);
            if (result.changed) {
              results.filesChanged++;
              results.totalFixes += result.fixes.length;
              
              result.fixes.forEach(fix => {
                const fixType = fix.split(':')[0];
                results.fixesByType[fixType] = (results.fixesByType[fixType] || 0) + 1;
              });
              
              log(`✅ Updated: ${itemPath} (${result.fixes.length} fixes)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`Error scanning ${currentPath}: ${error.message}`);
    }
  }

  scanRecursive(dirPath);
  return results;
}

function main() {
  log('🔧 Fixing React Hooks dependencies', 'bold');
  
  // Scan src directory
  const srcResults = scanDirectory('./src');
  
  log('\n📊 Results:', 'cyan');
  log(`Files processed: ${srcResults.filesProcessed}`);
  log(`Files changed: ${srcResults.filesChanged}`, srcResults.filesChanged > 0 ? 'green' : 'yellow');
  log(`Total fixes applied: ${srcResults.totalFixes}`, 'green');
  
  if (Object.keys(srcResults.fixesByType).length > 0) {
    log('\n🔧 Fixes by type:', 'cyan');
    Object.entries(srcResults.fixesByType).forEach(([fix, count]) => {
      log(`  ${fix}: ${count}`, 'green');
    });
  }
  
  if (srcResults.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    srcResults.errors.forEach(error => log(`  ${error}`, 'red'));
  }
  
  if (srcResults.filesChanged > 0) {
    log('\n✅ React Hooks dependencies fixes completed!', 'green');
    log('📝 Note: Review the changes and test the components', 'yellow');
  } else {
    log('\n✅ No React Hooks dependency issues found to fix', 'green');
  }
}

if (require.main === module) {
  main();
}
