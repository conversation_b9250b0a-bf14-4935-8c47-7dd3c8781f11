#!/usr/bin/env node

/**
 * Comprehensive Code Quality Fixer
 * Fixes all common code quality issues in one pass
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to exclude from replacement
const excludeFiles = [
  'scripts/',
  'test-',
  'jest.setup.js',
  'node_modules/',
  '.next/',
  'dist/',
  'build/'
];

function shouldExcludeFile(filePath) {
  return excludeFiles.some(exclude => filePath.includes(exclude));
}

// Comprehensive fixes
const comprehensiveFixes = [
  // Fix malformed dependency arrays
  {
    pattern: /\], \[([^\]]*(?:async|json|forEach|getMonth|toString|padStart|getDate|toISOString|split|from|catch|error|fetch|addEventListener|return|removeEventListener|preventDefault|toggleSidebar|Sidebar|useSidebar|calc|var|spacing|SidebarTrigger|SidebarRail|SidebarInset|SidebarInput|SidebarHeader|SidebarFooter|SidebarSeparator|SidebarContent|SidebarGroup|SidebarGroupLabel|SidebarGroupAction|SidebarGroupContent|SidebarMenu|SidebarMenuItem|cva|SidebarMenuButton|sidebarMenuButtonVariants|SidebarMenuAction|SidebarMenuBadge|SidebarMenuSkeleton|useMemo|floor|random|matchMedia|updateScreenSize|checkMobile)[^\]]*)\]/g,
    replacement: '], []',
    description: 'Fix malformed dependency arrays'
  },
  
  // Fix malformed arrays with keywords
  {
    pattern: /= \[([^\]]*(?:async|json|forEach|getMonth|toString|padStart|getDate|toISOString|split|from|catch|error|fetch|addEventListener|return|removeEventListener|preventDefault|toggleSidebar|Sidebar|useSidebar|calc|var|spacing|SidebarTrigger|SidebarRail|SidebarInset|SidebarInput|SidebarHeader|SidebarFooter|SidebarSeparator|SidebarContent|SidebarGroup|SidebarGroupLabel|SidebarGroupAction|SidebarGroupContent|SidebarMenu|SidebarMenuItem|cva|SidebarMenuButton|sidebarMenuButtonVariants|SidebarMenuAction|SidebarMenuBadge|SidebarMenuSkeleton|useMemo|floor|random|matchMedia|updateScreenSize|checkMobile)[^\]]*)\]/g,
    replacement: '= []',
    description: 'Fix malformed arrays'
  },
  
  // Fix error handling with unknown type
  {
    pattern: /catch \(error: unknown\) \{[\s\S]*?error\.message/g,
    replacement: (match) => {
      return match.replace('error.message', '(error as Error).message');
    },
    description: 'Fix error handling with unknown type'
  },
  
  // Fix logger calls with unknown error
  {
    pattern: /logger\.(error|warn|info|debug)\([^,]+, error\)/g,
    replacement: (match, level) => {
      return match.replace(', error)', ', error as Error)');
    },
    description: 'Fix logger calls with error type'
  },

  // Add error handling imports where needed
  {
    pattern: /^(import.*from ['"][^'"]*['"];?\s*)*(\n\n|$)/m,
    replacement: (match) => {
      if (!match.includes("from '@/lib/error-handler'") &&
          (match.includes('fetch(') || match.includes('try {') || match.includes('catch'))) {
        return match + "import { handleError, apiCall } from '@/lib/error-handler';\n";
      }
      return match;
    },
    description: 'Add error handling imports'
  },
  
  // Replace || with ?? for nullish coalescing where appropriate
  {
    pattern: /(\w+(?:\.\w+)*)\s*\|\|\s*([^;,)\]}]+)/g,
    replacement: (match, left, right) => {
      // Only replace if it's a simple variable or property access
      if (left.match(/^[a-zA-Z_$][a-zA-Z0-9_$.]*$/) && !right.includes('||')) {
        return `${left} ?? ${right}`;
      }
      return match;
    },
    description: 'Replace || with ?? for nullish coalescing'
  },
  
  // Fix any types to unknown
  {
    pattern: /: any(?!\[\])/g,
    replacement: ': unknown',
    description: 'Replace any with unknown'
  },
  
  // Fix any[] to unknown[]
  {
    pattern: /: any\[\]/g,
    replacement: ': unknown[]',
    description: 'Replace any[] with unknown[]'
  },
  
  // Remove unused variables in function parameters
  {
    pattern: /\(([^)]*),\s*_[a-zA-Z_$][a-zA-Z0-9_$]*\)/g,
    replacement: '($1)',
    description: 'Remove unused parameters'
  },
  
  // Fix non-null assertions to optional chaining where safe
  {
    pattern: /(\w+)\.(\w+)!/g,
    replacement: (match, obj, prop) => {
      // Only replace simple property access
      if (obj.match(/^[a-zA-Z_$][a-zA-Z0-9_$]*$/) && prop.match(/^[a-zA-Z_$][a-zA-Z0-9_$]*$/)) {
        return `${obj}?.${prop}`;
      }
      return match;
    },
    description: 'Replace non-null assertions with optional chaining'
  }
];

function applyComprehensiveFixes(content) {
  let newContent = content;
  let hasChanges = false;
  const appliedFixes = [];

  comprehensiveFixes.forEach(({ pattern, replacement, description }) => {
    if (typeof replacement === 'function') {
      if (pattern.test(newContent)) {
        newContent = newContent.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(description);
      }
    } else {
      if (pattern.test(newContent)) {
        newContent = newContent.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(description);
      }
    }
  });

  return { content: newContent, hasChanges, appliedFixes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = applyComprehensiveFixes(content);
    
    // Write back if changes were made
    if (result.hasChanges) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      return { changed: true, fixes: result.appliedFixes };
    }

    return { changed: false, fixes: [] };
  } catch (error) {
    log(`Error processing ${filePath}: ${error.message}`, 'red');
    return { changed: false, fixes: [], error: error.message };
  }
}

function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    totalFixes: 0,
    fixesByType: {},
    errors: []
  };

  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // Process TypeScript and JavaScript files
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) 
              && !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = processFile(itemPath);
            if (result.changed) {
              results.filesChanged++;
              results.totalFixes += result.fixes.length;
              
              result.fixes.forEach(fix => {
                results.fixesByType[fix] = (results.fixesByType[fix] || 0) + 1;
              });
              
              log(`✅ Updated: ${itemPath} (${result.fixes.length} fixes)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`Error scanning ${currentPath}: ${error.message}`);
    }
  }

  scanRecursive(dirPath);
  return results;
}

function main() {
  log('🔧 Applying comprehensive code quality fixes', 'bold');
  
  // Scan src directory
  const srcResults = scanDirectory('./src');
  
  log('\n📊 Results:', 'cyan');
  log(`Files processed: ${srcResults.filesProcessed}`);
  log(`Files changed: ${srcResults.filesChanged}`, srcResults.filesChanged > 0 ? 'green' : 'yellow');
  log(`Total fixes applied: ${srcResults.totalFixes}`, 'green');
  
  if (Object.keys(srcResults.fixesByType).length > 0) {
    log('\n🔧 Fixes by type:', 'cyan');
    Object.entries(srcResults.fixesByType).forEach(([fix, count]) => {
      log(`  ${fix}: ${count}`, 'green');
    });
  }
  
  if (srcResults.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    srcResults.errors.forEach(error => log(`  ${error}`, 'red'));
  }
  
  if (srcResults.filesChanged > 0) {
    log('\n✅ Comprehensive fixes completed!', 'green');
    log('📝 Note: Review the changes and test the application', 'yellow');
  } else {
    log('\n✅ No issues found to fix', 'green');
  }
}

if (require.main === module) {
  main();
}
