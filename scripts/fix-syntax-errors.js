#!/usr/bin/env node

/**
 * Syntax Errors Fixer
 * Fixes common syntax errors caused by automated replacements
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to exclude from replacement
const excludeFiles = [
  'scripts/',
  'test-',
  'jest.setup.js',
  'node_modules/',
  '.next/',
  'dist/',
  'build/'
];

function shouldExcludeFile(filePath) {
  return excludeFiles.some(exclude => filePath.includes(exclude));
}

// Common syntax fixes
const syntaxFixes = [
  // Fix useEffect dependency arrays that got corrupted
  {
    pattern: /useEffect\(\(\) => \{\s*([^}]+)\s*\}, \[([^\]]*)\]\)/g,
    replacement: (match, body, deps) => {
      // Clean up the body and dependencies
      const cleanBody = body.trim();
      const cleanDeps = deps.trim();
      return `useEffect(() => {\n    ${cleanBody}\n  }, [${cleanDeps}])`;
    },
    description: 'Fix useEffect syntax'
  },
  
  // Fix useCallback dependency arrays
  {
    pattern: /useCallback\(([^,]+), \[([^\]]*)\]\)/g,
    replacement: (match, func, deps) => {
      const cleanFunc = func.trim();
      const cleanDeps = deps.trim();
      return `useCallback(${cleanFunc}, [${cleanDeps}])`;
    },
    description: 'Fix useCallback syntax'
  },
  
  // Fix error handling with unknown type
  {
    pattern: /catch \(error: unknown\) \{[\s\S]*?error\.message/g,
    replacement: (match) => {
      return match.replace('error.message', '(error as Error).message');
    },
    description: 'Fix error handling with unknown type'
  },
  
  // Fix logger calls with unknown error
  {
    pattern: /logger\.(error|warn|info|debug)\([^,]+, error\)/g,
    replacement: (match, level) => {
      return match.replace(', error)', ', error as Error)');
    },
    description: 'Fix logger calls with error type'
  },
  
  // Fix malformed function calls
  {
    pattern: /(\w+)\(\s*,/g,
    replacement: '$1(',
    description: 'Fix malformed function calls'
  },
  
  // Fix trailing commas in object literals
  {
    pattern: /,\s*\}/g,
    replacement: '\n  }',
    description: 'Fix trailing commas in objects'
  },
  
  // Fix malformed array access
  {
    pattern: /\[\s*,/g,
    replacement: '[',
    description: 'Fix malformed array access'
  }
];

function fixSyntaxErrors(content) {
  let newContent = content;
  let hasChanges = false;
  const appliedFixes = [];

  syntaxFixes.forEach(({ pattern, replacement, description }) => {
    if (typeof replacement === 'function') {
      if (pattern.test(newContent)) {
        newContent = newContent.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(description);
      }
    } else {
      if (pattern.test(newContent)) {
        newContent = newContent.replace(pattern, replacement);
        hasChanges = true;
        appliedFixes.push(description);
      }
    }
  });

  return { content: newContent, hasChanges, appliedFixes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = fixSyntaxErrors(content);
    
    // Write back if changes were made
    if (result.hasChanges) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      return { changed: true, fixes: result.appliedFixes };
    }

    return { changed: false, fixes: [] };
  } catch (error) {
    log(`Error processing ${filePath}: ${error.message}`, 'red');
    return { changed: false, fixes: [], error: error.message };
  }
}

function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    totalFixes: 0,
    fixesByType: {},
    errors: []
  };

  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // Process TypeScript and JavaScript files
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) 
              && !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = processFile(itemPath);
            if (result.changed) {
              results.filesChanged++;
              results.totalFixes += result.fixes.length;
              
              result.fixes.forEach(fix => {
                results.fixesByType[fix] = (results.fixesByType[fix] || 0) + 1;
              });
              
              log(`✅ Updated: ${itemPath} (${result.fixes.length} fixes)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`Error scanning ${currentPath}: ${error.message}`);
    }
  }

  scanRecursive(dirPath);
  return results;
}

function main() {
  log('🔧 Fixing syntax errors', 'bold');
  
  // Scan src directory
  const srcResults = scanDirectory('./src');
  
  log('\n📊 Results:', 'cyan');
  log(`Files processed: ${srcResults.filesProcessed}`);
  log(`Files changed: ${srcResults.filesChanged}`, srcResults.filesChanged > 0 ? 'green' : 'yellow');
  log(`Total fixes applied: ${srcResults.totalFixes}`, 'green');
  
  if (Object.keys(srcResults.fixesByType).length > 0) {
    log('\n🔧 Fixes by type:', 'cyan');
    Object.entries(srcResults.fixesByType).forEach(([fix, count]) => {
      log(`  ${fix}: ${count}`, 'green');
    });
  }
  
  if (srcResults.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    srcResults.errors.forEach(error => log(`  ${error}`, 'red'));
  }
  
  if (srcResults.filesChanged > 0) {
    log('\n✅ Syntax error fixes completed!', 'green');
    log('📝 Note: Review the changes and test the application', 'yellow');
  } else {
    log('\n✅ No syntax errors found to fix', 'green');
  }
}

if (require.main === module) {
  main();
}
