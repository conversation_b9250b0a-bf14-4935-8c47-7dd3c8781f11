#!/usr/bin/env node

/**
 * 修复重复导入问题
 * 智能合并和清理重复的导入语句
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 解析导入语句
function parseImports(content) {
  const imports = [];
  const importRegex = /import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"];?/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const [fullMatch, namedImports, namespaceImport, defaultImport, source] = match;
    
    imports.push({
      fullMatch,
      namedImports: namedImports ? namedImports.split(',').map(s => s.trim()) : [],
      namespaceImport,
      defaultImport,
      source,
      startIndex: match.index,
      endIndex: match.index + fullMatch.length
    });
  }
  
  return imports;
}

// 合并重复导入
function mergeImports(imports) {
  const importMap = new Map();
  
  imports.forEach(imp => {
    const key = imp.source;
    
    if (!importMap.has(key)) {
      importMap.set(key, {
        source: imp.source,
        namedImports: new Set(imp.namedImports),
        namespaceImport: imp.namespaceImport,
        defaultImport: imp.defaultImport,
        originalImports: [imp]
      });
    } else {
      const existing = importMap.get(key);
      
      // 合并命名导入
      imp.namedImports.forEach(name => existing.namedImports.add(name));
      
      // 保留命名空间导入
      if (imp.namespaceImport) {
        existing.namespaceImport = imp.namespaceImport;
      }
      
      // 保留默认导入
      if (imp.defaultImport) {
        existing.defaultImport = imp.defaultImport;
      }
      
      existing.originalImports.push(imp);
    }
  });
  
  return Array.from(importMap.values());
}

// 生成导入语句
function generateImportStatement(mergedImport) {
  const { source, namedImports, namespaceImport, defaultImport } = mergedImport;
  
  let importParts = [];
  
  if (defaultImport) {
    importParts.push(defaultImport);
  }
  
  if (namespaceImport) {
    importParts.push(`* as ${namespaceImport}`);
  }
  
  if (namedImports.size > 0) {
    const sortedNamed = Array.from(namedImports).sort();
    importParts.push(`{ ${sortedNamed.join(', ')} }`);
  }
  
  return `import ${importParts.join(', ')} from '${source}';`;
}

// 修复文件中的重复导入
function fixDuplicateImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // 解析所有导入
    const imports = parseImports(content);
    
    if (imports.length === 0) {
      return { changed: false, duplicatesFound: 0 };
    }
    
    // 按源分组并检查重复
    const sourceGroups = {};
    imports.forEach(imp => {
      if (!sourceGroups[imp.source]) {
        sourceGroups[imp.source] = [];
      }
      sourceGroups[imp.source].push(imp);
    });
    
    // 找到有重复的源
    const duplicateSources = Object.entries(sourceGroups).filter(([, group]) => group.length > 1);
    
    if (duplicateSources.length === 0) {
      return { changed: false, duplicatesFound: 0 };
    }
    
    // 合并导入
    const mergedImports = mergeImports(imports);
    
    // 创建新内容
    let newContent = content;
    
    // 按索引倒序删除原导入（避免索引偏移）
    const allOriginalImports = imports.sort((a, b) => b.startIndex - a.startIndex);
    allOriginalImports.forEach(imp => {
      newContent = newContent.substring(0, imp.startIndex) + newContent.substring(imp.endIndex);
    });
    
    // 在文件开头添加合并后的导入
    const importStatements = mergedImports
      .map(generateImportStatement)
      .join('\n');
    
    // 找到合适的插入位置（在'use client'或其他指令之后）
    const newLines = newContent.split('\n');
    let insertIndex = 0;
    
    for (let i = 0; i < newLines.length; i++) {
      const line = newLines[i].trim();
      if (line.startsWith("'use") || line.startsWith('"use') || 
          line.startsWith('//') || line.startsWith('/*') || line === '') {
        insertIndex = i + 1;
      } else {
        break;
      }
    }
    
    newLines.splice(insertIndex, 0, importStatements, '');
    newContent = newLines.join('\n');
    
    // 写回文件
    fs.writeFileSync(filePath, newContent, 'utf8');
    
    return { 
      changed: true, 
      duplicatesFound: duplicateSources.length,
      mergedCount: imports.length - mergedImports.length
    };
    
  } catch (error) {
    return { 
      changed: false, 
      duplicatesFound: 0, 
      error: error.message 
    };
  }
}

// 扫描并修复所有文件
function fixAllFiles() {
  const results = {
    filesProcessed: 0,
    filesFixed: 0,
    totalDuplicates: 0,
    totalMerged: 0,
    errors: []
  };
  
  function scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules') {
            scanDirectory(itemPath);
          }
        } else if (stat.isFile()) {
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || 
               item.endsWith('.js') || item.endsWith('.jsx')) &&
              !item.includes('test') && !item.includes('spec')) {
            results.filesProcessed++;
            
            const result = fixDuplicateImports(itemPath);
            
            if (result.changed) {
              results.filesFixed++;
              results.totalDuplicates += result.duplicatesFound;
              results.totalMerged += result.mergedCount;
              
              log(`✅ 修复: ${itemPath} (${result.duplicatesFound} 个重复源, 合并 ${result.mergedCount} 个导入)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`扫描目录错误 ${dirPath}: ${error.message}`);
    }
  }
  
  scanDirectory('./src');
  return results;
}

// 主函数
function main() {
  log('🔧 开始修复重复导入问题...', 'bold');
  log('', 'white');
  
  const startTime = Date.now();
  const results = fixAllFiles();
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  log('📊 修复结果统计:', 'cyan');
  log(`⏱️  执行时间: ${duration}秒`, 'white');
  log(`📁 处理文件: ${results.filesProcessed}`, 'white');
  log(`🔧 修复文件: ${results.filesFixed}`, results.filesFixed > 0 ? 'green' : 'yellow');
  log(`🔄 重复源数: ${results.totalDuplicates}`, 'green');
  log(`📦 合并导入: ${results.totalMerged}`, 'green');
  
  if (results.errors.length > 0) {
    log('', 'white');
    log('❌ 错误列表:', 'red');
    results.errors.forEach(error => log(`   ${error}`, 'red'));
  }
  
  log('', 'white');
  if (results.filesFixed > 0) {
    log('🎉 重复导入修复完成！', 'green');
    log('📝 建议: 运行构建测试验证修复效果', 'yellow');
  } else {
    log('✅ 未发现重复导入问题', 'green');
  }
  
  return results.errors.length === 0;
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main, fixDuplicateImports };
