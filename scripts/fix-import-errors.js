#!/usr/bin/env node

/**
 * Import Errors Fixer
 * Fixes malformed import statements
 */

const fs = require('fs');
const path = require('path');

// Files with import errors
const filesToFix = [
  'src/app/dashboard/clients/[id]/page.tsx',
  'src/app/dashboard/clients/page.tsx',
  'src/app/dashboard/settings/appointment-types/page.tsx',
  'src/app/dashboard/settings/page.tsx',
  'src/app/dashboard/settings/treatment-categories/page.tsx',
  'src/app/dashboard/treatments/page.tsx',
  'src/components/dashboard/CRMSummaryCard.tsx'
];

function fixImportErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // Fix malformed import statements where logger import is inserted incorrectly
    const malformedImportPattern = /import \{\s*\nimport \{ logger \} from '@\/lib\/logger';\s*\n/g;
    if (malformedImportPattern.test(content)) {
      content = content.replace(malformedImportPattern, 'import { logger } from \'@/lib/logger\';\nimport {\n');
      hasChanges = true;
    }

    // Fix another pattern
    const anotherPattern = /import \{\s*import \{ logger \} from '@\/lib\/logger';\s*/g;
    if (anotherPattern.test(content)) {
      content = content.replace(anotherPattern, 'import { logger } from \'@/lib/logger\';\nimport { ');
      hasChanges = true;
    }

    // Fix malformed arrays with keywords
    const malformedArrayPattern = /\[([^\]]*(?:isArray|trim|filter|toLowerCase|return|includes|useEffect|fetchClients|async|json|forEach|entries|map|hsl|var|catch|error)[^\]]*)\]/g;
    if (malformedArrayPattern.test(content)) {
      content = content.replace(malformedArrayPattern, '[]');
      hasChanges = true;
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 Fixing import errors...');
  
  let fixedCount = 0;
  
  filesToFix.forEach(filePath => {
    if (fixImportErrors(filePath)) {
      fixedCount++;
    }
  });
  
  console.log(`\n📊 Results:`);
  console.log(`Files processed: ${filesToFix.length}`);
  console.log(`Files fixed: ${fixedCount}`);
  
  if (fixedCount > 0) {
    console.log('\n✅ Import errors fixed!');
  } else {
    console.log('\n✅ No import errors found to fix');
  }
}

if (require.main === module) {
  main();
}
