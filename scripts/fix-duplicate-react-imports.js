#!/usr/bin/env node

/**
 * 修复重复React导入问题
 * 专门处理重复声明的React hooks和组件
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要特殊处理的文件
const specificFixes = [
  {
    file: 'src/components/ui/enhanced-input.tsx',
    fix: (content) => {
      // 移除重复的React导入
      return content.replace(
        /import React, \{ useState, useCallback \} from 'react';\s*\n/,
        ''
      );
    }
  },
  {
    file: 'src/features/kanban/components/kanban-board.tsx',
    fix: (content) => {
      // 移除重复的React导入
      return content.replace(
        /import React, \{ Fragment, useEffect, useMemo, useRef, useState \} from 'react';\s*\n/,
        ''
      );
    }
  },
  {
    file: 'src/components/dashboard/QuickActionsToolbar.tsx',
    fix: (content) => {
      // 添加'use client'指令
      if (!content.includes("'use client'")) {
        return "'use client';\n\n" + content;
      }
      return content;
    }
  }
];

// 通用的重复导入修复
function fixDuplicateReactImports(content) {
  let fixed = content;
  
  // 查找所有React导入
  const reactImportPattern = /import\s+(?:\{[^}]+\}|\*\s+as\s+\w+|\w+(?:\s*,\s*\{[^}]+\})?)\s+from\s+['"]react['"];?\s*\n/g;
  const imports = [...content.matchAll(reactImportPattern)];
  
  if (imports.length <= 1) {
    return fixed;
  }
  
  // 收集所有导入的内容
  const allImports = new Set();
  let hasNamespaceImport = false;
  let hasDefaultImport = false;
  
  imports.forEach(match => {
    const importStatement = match[0];
    
    // 检查命名空间导入
    if (/import\s+\*\s+as\s+React/.test(importStatement)) {
      hasNamespaceImport = true;
    }
    
    // 检查默认导入
    if (/import\s+React\s*[,{]/.test(importStatement)) {
      hasDefaultImport = true;
    }
    
    // 提取命名导入
    const namedMatch = importStatement.match(/\{([^}]+)\}/);
    if (namedMatch) {
      const named = namedMatch[1].split(',').map(s => s.trim());
      named.forEach(name => allImports.add(name));
    }
  });
  
  // 移除所有现有的React导入
  imports.reverse().forEach(match => {
    const startIndex = match.index;
    const endIndex = startIndex + match[0].length;
    fixed = fixed.substring(0, startIndex) + fixed.substring(endIndex);
  });
  
  // 生成合并后的导入
  let newImport = '';
  if (hasNamespaceImport) {
    newImport = "import * as React from 'react';\n";
  } else if (hasDefaultImport && allImports.size > 0) {
    newImport = `import React, { ${Array.from(allImports).sort().join(', ')} } from 'react';\n`;
  } else if (hasDefaultImport) {
    newImport = "import React from 'react';\n";
  } else if (allImports.size > 0) {
    newImport = `import { ${Array.from(allImports).sort().join(', ')} } from 'react';\n`;
  }
  
  // 插入新的导入
  const lines = fixed.split('\n');
  let insertIndex = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith("'use") || line.startsWith('"use') || 
        line.startsWith('//') || line.startsWith('/*') || line === '') {
      insertIndex = i + 1;
    } else {
      break;
    }
  }
  
  lines.splice(insertIndex, 0, newImport);
  return lines.join('\n');
}

// 修复单个文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 检查是否是特殊处理的文件
    const specificFix = specificFixes.find(fix => filePath.endsWith(fix.file.replace('src/', '')));
    if (specificFix) {
      content = specificFix.fix(content);
    } else {
      // 通用修复
      content = fixDuplicateReactImports(content);
    }
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { changed: true };
    }
    
    return { changed: false };
  } catch (error) {
    return { changed: false, error: error.message };
  }
}

// 扫描并修复所有文件
function fixAllFiles() {
  const results = {
    filesProcessed: 0,
    filesFixed: 0,
    errors: []
  };
  
  function scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules' && item !== 'scripts') {
            scanDirectory(itemPath);
          }
        } else if (stat.isFile()) {
          if ((item.endsWith('.tsx') || item.endsWith('.jsx')) &&
              !item.includes('test') && !item.includes('spec')) {
            results.filesProcessed++;
            
            const result = fixFile(itemPath);
            
            if (result.changed) {
              results.filesFixed++;
              log(`✅ 修复: ${itemPath}`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`扫描目录错误 ${dirPath}: ${error.message}`);
    }
  }
  
  scanDirectory('./src');
  return results;
}

// 主函数
function main() {
  log('🔧 开始修复重复React导入问题...', 'bold');
  log('', 'white');
  
  const startTime = Date.now();
  const results = fixAllFiles();
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  log('📊 修复结果统计:', 'cyan');
  log(`⏱️  执行时间: ${duration}秒`, 'white');
  log(`📁 处理文件: ${results.filesProcessed}`, 'white');
  log(`🔧 修复文件: ${results.filesFixed}`, results.filesFixed > 0 ? 'green' : 'yellow');
  
  if (results.errors.length > 0) {
    log('', 'white');
    log('❌ 错误列表:', 'red');
    results.errors.forEach(error => log(`   ${error}`, 'red'));
  }
  
  log('', 'white');
  if (results.filesFixed > 0) {
    log('🎉 重复React导入修复完成！', 'green');
    log('📝 建议: 运行构建测试验证修复效果', 'yellow');
  } else {
    log('✅ 未发现重复React导入问题', 'green');
  }
  
  return results.errors.length === 0;
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main };
