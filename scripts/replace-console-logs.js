#!/usr/bin/env node

/**
 * Console.log Replacement Script
 * Replaces console.log statements with proper logging
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to exclude from replacement
const excludeFiles = [
  'scripts/',
  'test-',
  'jest.setup.js',
  'CODE_QUALITY_REPORT.md',
  'node_modules/',
  '.next/',
  'dist/',
  'build/'
];

// Patterns to replace
const replacements = [
  {
    pattern: /console\.log\(/g,
    replacement: 'logger.info(',
    needsImport: true
  },
  {
    pattern: /console\.error\(/g,
    replacement: 'logger.error(',
    needsImport: true
  },
  {
    pattern: /console\.warn\(/g,
    replacement: 'logger.warn(',
    needsImport: true
  },
  {
    pattern: /console\.debug\(/g,
    replacement: 'logger.debug(',
    needsImport: true
  }
];

function shouldExcludeFile(filePath) {
  return excludeFiles.some(exclude => filePath.includes(exclude));
}

function addLoggerImport(content, filePath) {
  // Check if logger import already exists
  if (content.includes("from '@/lib/logger'") || content.includes("import { logger }")) {
    return content;
  }

  // Determine the correct import path based on file location
  const relativePath = path.relative(path.dirname(filePath), 'src/lib/logger');
  const importPath = relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  
  // Add import at the top after other imports
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Find the last import statement
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') || lines[i].trim().startsWith("import {")) {
      insertIndex = i + 1;
    } else if (lines[i].trim() === '' && insertIndex > 0) {
      // Found empty line after imports
      break;
    } else if (!lines[i].trim().startsWith('import') && !lines[i].trim().startsWith('//') && lines[i].trim() !== '' && insertIndex > 0) {
      // Found non-import, non-comment line
      break;
    }
  }
  
  // Insert logger import
  const loggerImport = "import { logger } from '@/lib/logger';";
  lines.splice(insertIndex, 0, loggerImport);
  
  return lines.join('\n');
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let hasChanges = false;
    let needsImport = false;

    // Apply replacements
    replacements.forEach(({ pattern, replacement, needsImport: requiresImport }) => {
      if (pattern.test(newContent)) {
        newContent = newContent.replace(pattern, replacement);
        hasChanges = true;
        if (requiresImport) {
          needsImport = true;
        }
      }
    });

    // Add logger import if needed
    if (needsImport && hasChanges) {
      newContent = addLoggerImport(newContent, filePath);
    }

    // Write back if changes were made
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    log(`Error processing ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    errors: []
  };

  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // Process TypeScript and JavaScript files
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) 
              && !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            if (processFile(itemPath)) {
              results.filesChanged++;
              log(`✅ Updated: ${itemPath}`, 'green');
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`Error scanning ${currentPath}: ${error.message}`);
    }
  }

  scanRecursive(dirPath);
  return results;
}

function main() {
  log('🔄 Replacing console.log statements with proper logging', 'bold');
  
  // Scan src directory
  const srcResults = scanDirectory('./src');
  
  log('\n📊 Results:', 'cyan');
  log(`Files processed: ${srcResults.filesProcessed}`);
  log(`Files changed: ${srcResults.filesChanged}`, srcResults.filesChanged > 0 ? 'green' : 'yellow');
  
  if (srcResults.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    srcResults.errors.forEach(error => log(`  ${error}`, 'red'));
  }
  
  if (srcResults.filesChanged > 0) {
    log('\n✅ Console.log replacement completed!', 'green');
    log('📝 Note: Make sure to review the changes and test the application', 'yellow');
  } else {
    log('\n✅ No console.log statements found to replace', 'green');
  }
}

if (require.main === module) {
  main();
}
