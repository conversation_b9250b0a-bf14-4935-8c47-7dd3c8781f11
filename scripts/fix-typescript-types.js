#!/usr/bin/env node

/**
 * TypeScript Types Fixer
 * Fixes common TypeScript type issues
 */

const fs = require('fs');
const path = require('path');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to exclude from replacement
const excludeFiles = [
  'scripts/',
  'test-',
  'jest.setup.js',
  'node_modules/',
  '.next/',
  'dist/',
  'build/',
  'types.ts' // Skip type definition files
];

// Common type replacements
const typeReplacements = [
  // API response types
  {
    pattern: /: any\[\]/g,
    replacement: ': unknown[]',
    description: 'Replace any[] with unknown[]'
  },
  {
    pattern: /: any\s*=/g,
    replacement: ': unknown =',
    description: 'Replace any with unknown in assignments'
  },
  {
    pattern: /\(.*?: any\)/g,
    replacement: (match) => match.replace('any', 'unknown'),
    description: 'Replace any in function parameters'
  },
  // Event handlers
  {
    pattern: /\(e: any\)/g,
    replacement: '(e: React.FormEvent)',
    description: 'Replace any event with React.FormEvent'
  },
  {
    pattern: /\(event: any\)/g,
    replacement: '(event: React.FormEvent)',
    description: 'Replace any event with React.FormEvent'
  },
  // Form data
  {
    pattern: /formData: any/g,
    replacement: 'formData: Record<string, unknown>',
    description: 'Replace formData any with Record type'
  },
  {
    pattern: /data: any/g,
    replacement: 'data: Record<string, unknown>',
    description: 'Replace data any with Record type'
  }
];

// Non-null assertion fixes
const nonNullFixes = [
  {
    pattern: /\.([a-zA-Z_$][a-zA-Z0-9_$]*)!/g,
    replacement: (match, prop) => `?.${prop}`,
    description: 'Replace non-null assertion with optional chaining'
  }
];

function shouldExcludeFile(filePath) {
  return excludeFiles.some(exclude => filePath.includes(exclude));
}

function addReactImportIfNeeded(content) {
  // Check if React types are used but React is not imported
  const hasReactTypes = /React\.(FormEvent|ChangeEvent|MouseEvent|KeyboardEvent)/.test(content);
  const hasReactImport = /import.*React.*from ['"]react['"]/.test(content) || 
                        /import \* as React from ['"]react['"]/.test(content);
  
  if (hasReactTypes && !hasReactImport) {
    // Add React import at the top
    const lines = content.split('\n');
    let insertIndex = 0;
    
    // Find the first import or the beginning
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith('import ')) {
        insertIndex = i;
        break;
      }
    }
    
    lines.splice(insertIndex, 0, "import * as React from 'react';");
    return lines.join('\n');
  }
  
  return content;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let hasChanges = false;
    const appliedFixes = [];

    // Apply type replacements
    typeReplacements.forEach(({ pattern, replacement, description }) => {
      if (typeof replacement === 'function') {
        if (pattern.test(newContent)) {
          newContent = newContent.replace(pattern, replacement);
          hasChanges = true;
          appliedFixes.push(description);
        }
      } else {
        if (pattern.test(newContent)) {
          newContent = newContent.replace(pattern, replacement);
          hasChanges = true;
          appliedFixes.push(description);
        }
      }
    });

    // Apply non-null assertion fixes (be more conservative)
    nonNullFixes.forEach(({ pattern, replacement, description }) => {
      // Only apply if it's a simple property access, not array access
      const matches = newContent.match(pattern);
      if (matches) {
        matches.forEach(match => {
          // Skip if it's array access or complex expressions
          if (!match.includes('[') && !match.includes('(')) {
            newContent = newContent.replace(match, replacement(match, match.slice(1, -1)));
            hasChanges = true;
            if (!appliedFixes.includes(description)) {
              appliedFixes.push(description);
            }
          }
        });
      }
    });

    // Add React import if needed
    if (hasChanges) {
      newContent = addReactImportIfNeeded(newContent);
    }

    // Write back if changes were made
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return { changed: true, fixes: appliedFixes };
    }

    return { changed: false, fixes: [] };
  } catch (error) {
    log(`Error processing ${filePath}: ${error.message}`, 'red');
    return { changed: false, fixes: [], error: error.message };
  }
}

function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    totalFixes: 0,
    fixesByType: {},
    errors: []
  };

  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // Process TypeScript files
          if ((item.endsWith('.ts') || item.endsWith('.tsx')) && !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = processFile(itemPath);
            if (result.changed) {
              results.filesChanged++;
              results.totalFixes += result.fixes.length;
              
              result.fixes.forEach(fix => {
                results.fixesByType[fix] = (results.fixesByType[fix] || 0) + 1;
              });
              
              log(`✅ Updated: ${itemPath} (${result.fixes.length} fixes)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`Error scanning ${currentPath}: ${error.message}`);
    }
  }

  scanRecursive(dirPath);
  return results;
}

function main() {
  log('🔧 Fixing TypeScript type issues', 'bold');
  
  // Scan src directory
  const srcResults = scanDirectory('./src');
  
  log('\n📊 Results:', 'cyan');
  log(`Files processed: ${srcResults.filesProcessed}`);
  log(`Files changed: ${srcResults.filesChanged}`, srcResults.filesChanged > 0 ? 'green' : 'yellow');
  log(`Total fixes applied: ${srcResults.totalFixes}`, 'green');
  
  if (Object.keys(srcResults.fixesByType).length > 0) {
    log('\n🔧 Fixes by type:', 'cyan');
    Object.entries(srcResults.fixesByType).forEach(([fix, count]) => {
      log(`  ${fix}: ${count}`, 'green');
    });
  }
  
  if (srcResults.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    srcResults.errors.forEach(error => log(`  ${error}`, 'red'));
  }
  
  if (srcResults.filesChanged > 0) {
    log('\n✅ TypeScript type fixes completed!', 'green');
    log('📝 Note: Review the changes and run type checking', 'yellow');
  } else {
    log('\n✅ No TypeScript type issues found to fix', 'green');
  }
}

if (require.main === module) {
  main();
}
