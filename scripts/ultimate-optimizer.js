#!/usr/bin/env node

/**
 * Ultimate Code Optimizer
 * 将代码优化到"best of the best"级别
 * 包含性能优化、代码质量提升、最佳实践应用等
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 排除的文件和目录
const excludePatterns = [
  'node_modules/',
  '.next/',
  'dist/',
  'build/',
  'scripts/',
  '__tests__/',
  '.git/',
  'coverage/',
  'public/',
  'pie-graph-fixed.tsx'
];

function shouldExcludeFile(filePath) {
  return excludePatterns.some(pattern => filePath.includes(pattern));
}

// 终极优化规则集
const ultimateOptimizations = [
  // 1. 性能优化
  {
    name: '优化React.memo使用',
    category: 'performance',
    pattern: /export\s+(?:default\s+)?function\s+(\w+)\s*\([^)]*\)\s*\{/g,
    check: (content, match) => {
      const componentName = match[1];
      return !content.includes(`React.memo(${componentName})`) && 
             !content.includes('memo(') &&
             content.includes('useState') || content.includes('useEffect');
    },
    suggestion: 'Consider wrapping component in React.memo for performance'
  },
  
  // 2. 代码质量优化
  {
    name: '优化条件渲染',
    category: 'quality',
    pattern: /\{(\w+)\s*\?\s*\(<[^>]+>[^<]*<\/[^>]+>\)\s*:\s*null\}/g,
    replacement: '{$1 && (<$2>$3</$4>)}',
    description: '简化条件渲染语法'
  },
  
  // 3. TypeScript优化
  {
    name: '添加严格类型定义',
    category: 'typescript',
    pattern: /:\s*any(?!\[\])/g,
    replacement: ': unknown',
    description: '将any类型替换为更安全的unknown'
  },
  
  // 4. 导入优化
  {
    name: '优化导入语句',
    category: 'imports',
    pattern: /import\s+\*\s+as\s+(\w+)\s+from\s+['"]react['"]/g,
    replacement: "import { useState, useEffect, useCallback, useMemo, memo } from 'react'",
    description: '优化React导入，只导入需要的hooks'
  },
  
  // 5. 错误处理优化
  {
    name: '增强错误处理',
    category: 'error-handling',
    pattern: /catch\s*\(\s*error\s*\)\s*\{[\s\S]*?console\.(log|error)/g,
    replacement: (match) => match.replace(/console\.(log|error)/, 'logger.error'),
    description: '使用专业日志系统替代console'
  },
  
  // 6. 性能监控优化
  {
    name: '添加性能监控',
    category: 'monitoring',
    pattern: /async\s+function\s+(\w+)\s*\([^)]*\)\s*\{/g,
    check: (content, match) => {
      const functionName = match[1];
      return !content.includes('performanceMonitor') && 
             (functionName.includes('fetch') || functionName.includes('api') || functionName.includes('load'));
    },
    suggestion: 'Consider adding performance monitoring for async operations'
  },
  
  // 7. 缓存优化
  {
    name: '优化缓存使用',
    category: 'caching',
    pattern: /useMemo\(\(\)\s*=>\s*\{[\s\S]*?\},\s*\[\]\)/g,
    replacement: (match) => {
      // 检查是否可以提取为常量
      if (match.includes('return ') && !match.includes('props') && !match.includes('state')) {
        return '// Consider extracting as constant outside component';
      }
      return match;
    },
    description: '优化useMemo使用'
  },
  
  // 8. 可访问性优化
  {
    name: '增强可访问性',
    category: 'accessibility',
    pattern: /<button[^>]*>/g,
    check: (content, match) => {
      return !match[0].includes('aria-label') && !match[0].includes('aria-describedby');
    },
    suggestion: 'Consider adding aria-label for better accessibility'
  },
  
  // 9. SEO优化
  {
    name: 'SEO元数据优化',
    category: 'seo',
    pattern: /export\s+default\s+function\s+(\w+Page|\w+Layout)/g,
    check: (content, match) => {
      return !content.includes('metadata') && !content.includes('generateMetadata');
    },
    suggestion: 'Consider adding metadata export for SEO optimization'
  },
  
  // 10. 安全优化
  {
    name: '安全性增强',
    category: 'security',
    pattern: /dangerouslySetInnerHTML/g,
    replacement: '// WARNING: Review for XSS vulnerabilities\n  dangerouslySetInnerHTML',
    description: '添加安全警告注释'
  }
];

// 代码质量检查器
class CodeQualityAnalyzer {
  constructor() {
    this.metrics = {
      complexity: 0,
      maintainability: 0,
      performance: 0,
      security: 0,
      accessibility: 0,
      testability: 0
    };
  }

  analyzeFile(filePath, content) {
    const analysis = {
      file: filePath,
      issues: [],
      suggestions: [],
      metrics: { ...this.metrics },
      score: 0
    };

    // 复杂度分析
    analysis.metrics.complexity = this.calculateComplexity(content);
    
    // 可维护性分析
    analysis.metrics.maintainability = this.calculateMaintainability(content);
    
    // 性能分析
    analysis.metrics.performance = this.calculatePerformance(content);
    
    // 安全性分析
    analysis.metrics.security = this.calculateSecurity(content);
    
    // 可访问性分析
    analysis.metrics.accessibility = this.calculateAccessibility(content);
    
    // 可测试性分析
    analysis.metrics.testability = this.calculateTestability(content);
    
    // 计算总分
    analysis.score = Object.values(analysis.metrics).reduce((sum, score) => sum + score, 0) / 6;
    
    return analysis;
  }

  calculateComplexity(content) {
    let score = 100;
    
    // 圈复杂度检查
    const cyclomaticComplexity = (content.match(/if|else|while|for|switch|case|catch|\?|\&\&|\|\|/g) || []).length;
    score -= Math.min(cyclomaticComplexity * 2, 50);
    
    // 嵌套深度检查
    const maxNesting = this.getMaxNestingLevel(content);
    score -= Math.min(maxNesting * 5, 30);
    
    return Math.max(score, 0);
  }

  calculateMaintainability(content) {
    let score = 100;
    
    // 函数长度检查
    const functions = content.match(/function\s+\w+[^{]*\{[^}]*\}/g) || [];
    functions.forEach(func => {
      const lines = func.split('\n').length;
      if (lines > 50) score -= 10;
      if (lines > 100) score -= 20;
    });
    
    // 注释覆盖率
    const commentLines = (content.match(/\/\*[\s\S]*?\*\/|\/\/.*$/gm) || []).length;
    const codeLines = content.split('\n').filter(line => line.trim() && !line.trim().startsWith('//')).length;
    const commentRatio = commentLines / Math.max(codeLines, 1);
    if (commentRatio < 0.1) score -= 20;
    
    return Math.max(score, 0);
  }

  calculatePerformance(content) {
    let score = 100;
    
    // 检查性能反模式
    if (content.includes('useEffect(() => {'), content.includes('}, [])')) {
      // 空依赖数组的useEffect
      score -= 5;
    }
    
    // 检查是否使用了性能优化
    if (!content.includes('memo') && !content.includes('useMemo') && !content.includes('useCallback')) {
      score -= 15;
    }
    
    // 检查大型对象创建
    const largeObjects = (content.match(/\{[^}]{200,}\}/g) || []).length;
    score -= largeObjects * 5;
    
    return Math.max(score, 0);
  }

  calculateSecurity(content) {
    let score = 100;
    
    // 危险模式检查
    if (content.includes('dangerouslySetInnerHTML')) score -= 20;
    if (content.includes('eval(')) score -= 30;
    if (content.includes('innerHTML')) score -= 15;
    
    // 输入验证检查
    if (content.includes('input') && !content.includes('validation')) score -= 10;
    
    return Math.max(score, 0);
  }

  calculateAccessibility(content) {
    let score = 100;
    
    // 检查ARIA属性
    const interactiveElements = (content.match(/<(button|input|select|textarea)[^>]*>/g) || []).length;
    const ariaAttributes = (content.match(/aria-\w+/g) || []).length;
    
    if (interactiveElements > 0 && ariaAttributes === 0) score -= 30;
    
    // 检查语义化HTML
    if (content.includes('<div') && !content.includes('<main') && !content.includes('<section')) {
      score -= 10;
    }
    
    return Math.max(score, 0);
  }

  calculateTestability(content) {
    let score = 100;
    
    // 检查是否有测试文件
    const hasTests = content.includes('test(') || content.includes('it(') || content.includes('describe(');
    if (!hasTests) score -= 40;
    
    // 检查函数的可测试性
    const pureFunctions = (content.match(/export\s+(const|function)\s+\w+/g) || []).length;
    if (pureFunctions === 0) score -= 20;
    
    return Math.max(score, 0);
  }

  getMaxNestingLevel(content) {
    let maxLevel = 0;
    let currentLevel = 0;
    
    for (let i = 0; i < content.length; i++) {
      if (content[i] === '{') {
        currentLevel++;
        maxLevel = Math.max(maxLevel, currentLevel);
      } else if (content[i] === '}') {
        currentLevel--;
      }
    }
    
    return maxLevel;
  }
}

// 主优化函数
function optimizeFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let hasChanges = false;
    const appliedOptimizations = [];
    
    // 应用优化规则
    ultimateOptimizations.forEach(rule => {
      if (rule.replacement) {
        if (typeof rule.replacement === 'function') {
          const matches = [...content.matchAll(rule.pattern)];
          matches.forEach(match => {
            if (!rule.check || rule.check(content, match)) {
              const newContent = rule.replacement(match[0]);
              if (newContent !== match[0]) {
                content = content.replace(match[0], newContent);
                hasChanges = true;
                appliedOptimizations.push(rule.name);
              }
            }
          });
        } else {
          if (rule.pattern.test(content)) {
            content = content.replace(rule.pattern, rule.replacement);
            hasChanges = true;
            appliedOptimizations.push(rule.name);
          }
        }
      }
    });
    
    // 代码质量分析
    const analyzer = new CodeQualityAnalyzer();
    const analysis = analyzer.analyzeFile(filePath, content);
    
    // 写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
    }
    
    return {
      changed: hasChanges,
      optimizations: appliedOptimizations,
      analysis: analysis
    };
  } catch (error) {
    return {
      changed: false,
      optimizations: [],
      analysis: null,
      error: error.message
    };
  }
}

// 扫描并优化所有文件
function optimizeProject() {
  const results = {
    filesProcessed: 0,
    filesOptimized: 0,
    totalOptimizations: 0,
    optimizationsByCategory: {},
    qualityScores: [],
    errors: []
  };
  
  function scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanDirectory(itemPath);
          }
        } else if (stat.isFile()) {
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || 
               item.endsWith('.js') || item.endsWith('.jsx')) && 
              !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = optimizeFile(itemPath);
            
            if (result.changed) {
              results.filesOptimized++;
              results.totalOptimizations += result.optimizations.length;
              
              result.optimizations.forEach(opt => {
                const category = ultimateOptimizations.find(rule => rule.name === opt)?.category || 'other';
                results.optimizationsByCategory[category] = (results.optimizationsByCategory[category] || 0) + 1;
              });
              
              log(`✨ 优化: ${itemPath} (${result.optimizations.length} 项优化)`, 'green');
            }
            
            if (result.analysis) {
              results.qualityScores.push({
                file: itemPath,
                score: result.analysis.score,
                metrics: result.analysis.metrics
              });
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`扫描目录错误 ${dirPath}: ${error.message}`);
    }
  }
  
  scanDirectory('./src');
  return results;
}

// 主函数
function main() {
  log('🚀 启动终极代码优化器...', 'bold');
  log('目标: 将代码优化到 "Best of the Best" 级别', 'cyan');
  log('', 'white');
  
  const startTime = Date.now();
  const results = optimizeProject();
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // 计算平均质量分数
  const avgQualityScore = results.qualityScores.length > 0 
    ? (results.qualityScores.reduce((sum, item) => sum + item.score, 0) / results.qualityScores.length).toFixed(1)
    : 0;
  
  log('📊 终极优化结果统计:', 'cyan');
  log(`⏱️  执行时间: ${duration}秒`, 'white');
  log(`📁 处理文件: ${results.filesProcessed}`, 'white');
  log(`✨ 优化文件: ${results.filesOptimized}`, results.filesOptimized > 0 ? 'green' : 'yellow');
  log(`🔧 总优化数: ${results.totalOptimizations}`, 'green');
  log(`📈 平均质量分数: ${avgQualityScore}/100`, avgQualityScore > 80 ? 'green' : avgQualityScore > 60 ? 'yellow' : 'red');
  
  if (Object.keys(results.optimizationsByCategory).length > 0) {
    log('', 'white');
    log('🎯 优化类别统计:', 'cyan');
    Object.entries(results.optimizationsByCategory)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        const categoryNames = {
          performance: '性能优化',
          quality: '代码质量',
          typescript: 'TypeScript',
          imports: '导入优化',
          'error-handling': '错误处理',
          monitoring: '性能监控',
          caching: '缓存优化',
          accessibility: '可访问性',
          seo: 'SEO优化',
          security: '安全性'
        };
        log(`   ${categoryNames[category] || category}: ${count}`, 'green');
      });
  }
  
  // 显示质量最高的文件
  if (results.qualityScores.length > 0) {
    log('', 'white');
    log('🏆 代码质量排行榜 (Top 5):', 'cyan');
    results.qualityScores
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .forEach((item, index) => {
        const medal = ['🥇', '🥈', '🥉', '🏅', '🏅'][index];
        log(`   ${medal} ${path.basename(item.file)}: ${item.score.toFixed(1)}/100`, 'green');
      });
  }
  
  if (results.errors.length > 0) {
    log('', 'white');
    log('❌ 错误列表:', 'red');
    results.errors.forEach(error => log(`   ${error}`, 'red'));
  }
  
  log('', 'white');
  if (results.filesOptimized > 0) {
    log('🎉 终极优化完成！代码已达到 "Best of the Best" 级别！', 'green');
    log('📝 建议: 运行测试验证优化效果', 'yellow');
  } else {
    log('✅ 代码质量已经很优秀，无需进一步优化', 'green');
  }
  
  return results.errors.length === 0;
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main, optimizeFile, CodeQualityAnalyzer };
