#!/usr/bin/env node

/**
 * 针对性修复脚本
 * 修复构建过程中发现的特定问题
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 特定文件的修复规则
const specificFixes = [
  {
    file: 'src/components/ui/address-input.tsx',
    fixes: [
      {
        pattern: /disabled=\{disabled \?\? \(\(\!searchQuery\.trim\(\) \|\| isSearching\)\)\}/,
        replacement: 'disabled={disabled ?? (!searchQuery.trim() || isSearching)}'
      }
    ]
  },
  {
    file: 'src/app/api/health/route.ts',
    fixes: [
      {
        pattern: /const missingVars = requiredVars\.filter\(varName => !process\.env\[\]\)/,
        replacement: 'const missingVars = requiredVars.filter(varName => !process.env[varName])'
      }
    ]
  },
  {
    file: 'src/app/dashboard/settings/working-hours/page.tsx',
    fixes: [
      {
        pattern: /if \(errors\[\]\) \{/,
        replacement: 'if (errors[errorKey]) {'
      },
      {
        pattern: /setErrors\(prev => \(\{ \.\.\.prev, \[\]: '' \}\)\)/,
        replacement: 'setErrors(prev => ({ ...prev, [errorKey]: \'\' }))'
      }
    ]
  },
  {
    file: 'src/features/kanban/components/kanban-board.tsx',
    fixes: [
      {
        pattern: /\{React\.createElement\(SortableContext as any, \{ items: columnsId \},\s*\[\]'>/,
        replacement: '{React.createElement(SortableContext as any, { items: columnsId },'
      },
      {
        pattern: /\[\]'>\s*<NewSectionDialog \/>/,
        replacement: 'columns.map((col: any) => (\n            <BoardColumn key={col.id} column={col} tasks={tasks.filter((task: any) => task.columnId === col.id)} />\n          ))\n        )}\n        <div className="flex-shrink-0">\n          <NewSectionDialog />'
      }
    ]
  }
];

// 处理特定文件
function processSpecificFile(fileConfig) {
  const filePath = fileConfig.file;
  
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️  文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];
    
    for (const fix of fileConfig.fixes) {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        hasChanges = true;
        appliedFixes.push('修复语法错误');
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复: ${filePath} (${appliedFixes.length} 个修复)`, 'green');
      return true;
    } else {
      log(`ℹ️  无需修复: ${filePath}`, 'blue');
      return false;
    }
  } catch (error) {
    log(`❌ 修复失败 ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

// 修复pie-graph.tsx的模块问题
function fixPieGraphModule() {
  const filePath = 'src/features/overview/components/pie-graph.tsx';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查文件是否有问题
    if (!content.startsWith("'use client';")) {
      log(`⚠️  pie-graph.tsx 缺少 'use client' 指令`, 'yellow');
      content = "'use client';\n\n" + content;
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复: ${filePath} (添加 'use client' 指令)`, 'green');
      return true;
    }
    
    return false;
  } catch (error) {
    log(`❌ 修复pie-graph.tsx失败: ${error.message}`, 'red');
    return false;
  }
}

// 通用的语法修复
function applyGeneralFixes() {
  const generalFixes = [
    // 修复空数组访问
    {
      pattern: /\[\s*\]/g,
      replacement: '[]',
      description: '修复空数组语法'
    },
    // 修复空对象访问
    {
      pattern: /\{\s*\}/g,
      replacement: '{}',
      description: '修复空对象语法'
    },
    // 修复错误的字符串模板
    {
      pattern: /\[\]'>/g,
      replacement: '',
      description: '移除错误的字符串模板'
    }
  ];
  
  const srcFiles = getAllTsxFiles('./src');
  let totalFixed = 0;
  
  srcFiles.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      
      generalFixes.forEach(fix => {
        if (fix.pattern.test(content)) {
          content = content.replace(fix.pattern, fix.replacement);
          hasChanges = true;
        }
      });
      
      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        totalFixed++;
      }
    } catch (error) {
      // 忽略读取错误
    }
  });
  
  if (totalFixed > 0) {
    log(`✅ 通用修复: ${totalFixed} 个文件`, 'green');
  }
  
  return totalFixed;
}

// 获取所有TypeScript文件
function getAllTsxFiles(dir) {
  const files = [];
  
  function scanDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const itemPath = path.join(currentDir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(itemPath);
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(itemPath);
        }
      });
    } catch (error) {
      // 忽略扫描错误
    }
  }
  
  scanDir(dir);
  return files;
}

// 主函数
function main() {
  log('🎯 开始针对性修复...', 'bold');
  log('', 'white');
  
  let totalFixed = 0;
  
  // 处理特定文件修复
  log('📁 处理特定文件修复:', 'blue');
  specificFixes.forEach(fileConfig => {
    if (processSpecificFile(fileConfig)) {
      totalFixed++;
    }
  });
  
  log('', 'white');
  
  // 修复pie-graph模块问题
  log('🔧 修复模块问题:', 'blue');
  if (fixPieGraphModule()) {
    totalFixed++;
  }
  
  log('', 'white');
  
  // 应用通用修复
  log('🔧 应用通用修复:', 'blue');
  const generalFixed = applyGeneralFixes();
  totalFixed += generalFixed;
  
  log('', 'white');
  log('📊 修复结果统计:', 'cyan');
  log(`🔧 总修复数: ${totalFixed}`, totalFixed > 0 ? 'green' : 'yellow');
  
  if (totalFixed > 0) {
    log('', 'white');
    log('🎉 针对性修复完成！', 'green');
    log('📝 建议: 重新运行构建测试', 'yellow');
  } else {
    log('', 'white');
    log('ℹ️  无需针对性修复', 'blue');
  }
  
  return totalFixed > 0;
}

if (require.main === module) {
  main();
}

module.exports = { main };
