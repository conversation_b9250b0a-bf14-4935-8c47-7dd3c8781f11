#!/usr/bin/env node

/**
 * Ultimate Code Quality Fixer
 * 系统性地修复所有代码质量问题，确保bug-free状态
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 排除的文件和目录
const excludePatterns = [
  'node_modules/',
  '.next/',
  'dist/',
  'build/',
  'scripts/',
  'test-',
  '__tests__/',
  '.git/',
  'coverage/',
  'public/'
];

function shouldExcludeFile(filePath) {
  return excludePatterns.some(pattern => filePath.includes(pattern));
}

// 全面的代码修复规则
const fixingRules = [
  // 1. 修复破损的导入语句
  {
    name: '修复破损的导入语句',
    pattern: /import \{\s*\nimport \{ logger \} from '@\/lib\/logger';\s*\n/g,
    replacement: "import { logger } from '@/lib/logger';\nimport {\n",
    priority: 1
  },
  {
    name: '修复内联导入错误',
    pattern: /import \{\s*import \{ logger \} from '@\/lib\/logger';\s*/g,
    replacement: "import { logger } from '@/lib/logger';\nimport { ",
    priority: 1
  },
  
  // 2. 修复破损的数组
  {
    name: '修复包含关键字的数组',
    pattern: /\[([^\]]*(?:async|json|forEach|entries|map|hsl|var|catch|error|fetch|addEventListener|return|removeEventListener|preventDefault|toggleSidebar|Sidebar|useSidebar|calc|spacing|SidebarTrigger|SidebarRail|SidebarInset|SidebarInput|SidebarHeader|SidebarFooter|SidebarSeparator|SidebarContent|SidebarGroup|SidebarGroupLabel|SidebarGroupAction|SidebarGroupContent|SidebarMenu|SidebarMenuItem|cva|SidebarMenuButton|sidebarMenuButtonVariants|SidebarMenuAction|SidebarMenuBadge|SidebarMenuSkeleton|useMemo|floor|random|matchMedia|updateScreenSize|checkMobile|isArray|trim|filter|toLowerCase|includes|useEffect|fetchClients|getMonth|toString|padStart|getDate|toISOString|split|from|fetchTreatmentData|fetchRevenueData|fetchGrowthData)[^\]]*)\]/g,
    replacement: '[]',
    priority: 1
  },
  
  // 3. 修复模板字符串错误
  {
    name: '修复未终止的模板字符串',
    pattern: /\{[^}]*\|\| '[^']*'\}`,/g,
    replacement: (match) => {
      // 移除错误的模板字符串语法
      return match.replace(/\}`,/, '}');
    },
    priority: 1
  },
  
  // 4. 修复运算符优先级问题
  {
    name: '修复nullish coalescing运算符优先级',
    pattern: /(\w+(?:\.\w+)*)\s*\?\?\s*([^|&\s]+(?:\s*\|\|\s*[^|&\s]+)+)/g,
    replacement: '$1 ?? ($2)',
    priority: 2
  },
  
  // 5. 统一使用nullish coalescing
  {
    name: '将||替换为??（适当情况下）',
    pattern: /(\w+(?:\.\w+)*)\s*\|\|\s*(\[\]|{}|''|""|null|undefined|0|false)/g,
    replacement: '$1 ?? $2',
    priority: 2
  },
  
  // 6. 修复类型断言
  {
    name: '添加错误类型断言',
    pattern: /logger\.(error|warn|info|debug)\([^,]+,\s*error\s*\)/g,
    replacement: 'logger.$1($1, error as Error)',
    priority: 2
  },
  {
    name: '修复catch块中的错误处理',
    pattern: /catch\s*\(\s*error\s*\)\s*\{[\s\S]*?error\.message/g,
    replacement: (match) => match.replace('error.message', '(error as Error).message'),
    priority: 2
  },
  
  // 7. 修复any类型
  {
    name: '将any替换为unknown',
    pattern: /:\s*any(?!\[\])/g,
    replacement: ': unknown',
    priority: 3
  },
  {
    name: '将any[]替换为unknown[]',
    pattern: /:\s*any\[\]/g,
    replacement: ': unknown[]',
    priority: 3
  },
  
  // 8. 修复React Hooks依赖
  {
    name: '清理useEffect依赖数组',
    pattern: /useEffect\s*\([^,]+,\s*\[([^\]]*(?:async|json|forEach|entries|map|hsl|var|catch|error|fetch|addEventListener|return|removeEventListener|preventDefault|toggleSidebar|Sidebar|useSidebar|calc|spacing|SidebarTrigger|SidebarRail|SidebarInset|SidebarInput|SidebarHeader|SidebarFooter|SidebarSeparator|SidebarContent|SidebarGroup|SidebarGroupLabel|SidebarGroupAction|SidebarGroupContent|SidebarMenu|SidebarMenuItem|cva|SidebarMenuButton|sidebarMenuButtonVariants|SidebarMenuAction|SidebarMenuBadge|SidebarMenuSkeleton|useMemo|floor|random|matchMedia|updateScreenSize|checkMobile)[^\]]*)\]\s*\)/g,
    replacement: (match) => match.replace(/\[([^\]]*)\]/, '[]'),
    priority: 2
  },
  
  // 9. 修复console语句
  {
    name: '替换console.log为logger.info',
    pattern: /console\.log\(/g,
    replacement: 'logger.info(',
    priority: 3
  },
  {
    name: '替换console.error为logger.error',
    pattern: /console\.error\(/g,
    replacement: 'logger.error(',
    priority: 3
  },
  {
    name: '替换console.warn为logger.warn',
    pattern: /console\.warn\(/g,
    replacement: 'logger.warn(',
    priority: 3
  },
  
  // 10. 修复非空断言
  {
    name: '将非空断言替换为可选链',
    pattern: /(\w+)\.(\w+)!/g,
    replacement: '$1?.$2',
    priority: 3
  },
  
  // 11. 修复字符串模板
  {
    name: '修复错误的字符串模板语法',
    pattern: /\$\{([^}]+)\}\s*\|\|\s*'[^']*'/g,
    replacement: '${$1}',
    priority: 1
  }
];

// 需要添加logger导入的文件检查
function needsLoggerImport(content) {
  return /logger\.(info|error|warn|debug)/.test(content) && 
         !/import.*logger.*from.*@\/lib\/logger/.test(content);
}

// 添加logger导入
function addLoggerImport(content) {
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // 找到最后一个import语句的位置
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') || lines[i].trim().startsWith("import {")) {
      insertIndex = i + 1;
    } else if (lines[i].trim() === '' && insertIndex > 0) {
      break;
    } else if (!lines[i].trim().startsWith('import') && 
               !lines[i].trim().startsWith('//') && 
               !lines[i].trim().startsWith('/*') &&
               !lines[i].trim().startsWith('*') &&
               !lines[i].trim().startsWith("'use") &&
               !lines[i].trim().startsWith('"use') &&
               lines[i].trim() !== '' && 
               insertIndex > 0) {
      break;
    }
  }
  
  lines.splice(insertIndex, 0, "import { logger } from '@/lib/logger';");
  return lines.join('\n');
}

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedFixes = [];
    
    // 按优先级排序并应用修复规则
    const sortedRules = fixingRules.sort((a, b) => a.priority - b.priority);
    
    for (const rule of sortedRules) {
      if (typeof rule.replacement === 'function') {
        if (rule.pattern.test(content)) {
          content = content.replace(rule.pattern, rule.replacement);
          hasChanges = true;
          appliedFixes.push(rule.name);
        }
      } else {
        if (rule.pattern.test(content)) {
          content = content.replace(rule.pattern, rule.replacement);
          hasChanges = true;
          appliedFixes.push(rule.name);
        }
      }
    }
    
    // 检查是否需要添加logger导入
    if (needsLoggerImport(content)) {
      content = addLoggerImport(content);
      hasChanges = true;
      appliedFixes.push('添加logger导入');
    }
    
    // 写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { changed: true, fixes: appliedFixes };
    }
    
    return { changed: false, fixes: [] };
  } catch (error) {
    log(`❌ 处理文件失败 ${filePath}: ${error.message}`, 'red');
    return { changed: false, fixes: [], error: error.message };
  }
}

// 扫描目录
function scanDirectory(dirPath) {
  const results = {
    filesProcessed: 0,
    filesChanged: 0,
    totalFixes: 0,
    fixesByType: {},
    errors: []
  };
  
  function scanRecursive(currentPath) {
    try {
      const items = fs.readdirSync(currentPath);
      
      items.forEach(item => {
        const itemPath = path.join(currentPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeFile(itemPath)) {
            scanRecursive(itemPath);
          }
        } else if (stat.isFile()) {
          // 处理TypeScript和JavaScript文件
          if ((item.endsWith('.ts') || item.endsWith('.tsx') || 
               item.endsWith('.js') || item.endsWith('.jsx')) && 
              !shouldExcludeFile(itemPath)) {
            results.filesProcessed++;
            
            const result = processFile(itemPath);
            if (result.changed) {
              results.filesChanged++;
              results.totalFixes += result.fixes.length;
              
              result.fixes.forEach(fix => {
                results.fixesByType[fix] = (results.fixesByType[fix] || 0) + 1;
              });
              
              log(`✅ 修复: ${itemPath} (${result.fixes.length} 个修复)`, 'green');
            }
            
            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`扫描目录错误 ${currentPath}: ${error.message}`);
    }
  }
  
  scanRecursive(dirPath);
  return results;
}

// 主函数
function main() {
  log('🚀 开始系统性代码完善...', 'bold');
  log('', 'white');
  
  const startTime = Date.now();
  
  // 扫描src目录
  const results = scanDirectory('./src');
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  log('', 'white');
  log('📊 完善结果统计:', 'cyan');
  log(`⏱️  执行时间: ${duration}秒`, 'white');
  log(`📁 处理文件: ${results.filesProcessed}`, 'white');
  log(`🔧 修复文件: ${results.filesChanged}`, results.filesChanged > 0 ? 'green' : 'yellow');
  log(`✨ 总修复数: ${results.totalFixes}`, 'green');
  
  if (Object.keys(results.fixesByType).length > 0) {
    log('', 'white');
    log('🔧 修复类型统计:', 'cyan');
    Object.entries(results.fixesByType)
      .sort(([,a], [,b]) => b - a)
      .forEach(([fix, count]) => {
        log(`   ${fix}: ${count}`, 'green');
      });
  }
  
  if (results.errors.length > 0) {
    log('', 'white');
    log('❌ 错误列表:', 'red');
    results.errors.forEach(error => log(`   ${error}`, 'red'));
  }
  
  log('', 'white');
  if (results.filesChanged > 0) {
    log('🎉 系统性代码完善完成！', 'green');
    log('📝 建议: 运行构建测试验证修复效果', 'yellow');
  } else {
    log('✅ 代码质量良好，无需修复', 'green');
  }
  
  return results.errors.length === 0 && results.filesChanged >= 0;
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main };
