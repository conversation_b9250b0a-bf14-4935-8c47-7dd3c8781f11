#!/usr/bin/env node

/**
 * 修复React导入问题
 * 智能分析组件实际使用的hooks，移除不必要的导入
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// React hooks列表
const reactHooks = [
  'useState',
  'useEffect',
  'useCallback',
  'useMemo',
  'useRef',
  'useContext',
  'useReducer',
  'useLayoutEffect',
  'useImperativeHandle',
  'useDebugValue',
  'useDeferredValue',
  'useTransition',
  'useId',
  'useSyncExternalStore'
];

// 分析文件中实际使用的React功能
function analyzeReactUsage(content) {
  const usage = {
    hooks: new Set(),
    memo: false,
    forwardRef: false,
    createElement: false,
    Fragment: false,
    Component: false,
    PureComponent: false,
    createContext: false,
    lazy: false,
    Suspense: false
  };

  // 检查hooks使用
  reactHooks.forEach(hook => {
    const hookPattern = new RegExp(`\\b${hook}\\s*\\(`, 'g');
    if (hookPattern.test(content)) {
      usage.hooks.add(hook);
    }
  });

  // 检查其他React功能
  if (/\bmemo\s*\(/.test(content)) usage.memo = true;
  if (/\bforwardRef\s*\(/.test(content)) usage.forwardRef = true;
  if (/\bReact\.createElement\s*\(/.test(content)) usage.createElement = true;
  if (/\bFragment\b/.test(content) || /<>/.test(content)) usage.Fragment = true;
  if (/extends\s+Component\b/.test(content)) usage.Component = true;
  if (/extends\s+PureComponent\b/.test(content)) usage.PureComponent = true;
  if (/\bcreateContext\s*\(/.test(content)) usage.createContext = true;
  if (/\blazy\s*\(/.test(content)) usage.lazy = true;
  if (/<Suspense/.test(content)) usage.Suspense = true;

  return usage;
}

// 生成优化的React导入语句
function generateOptimizedReactImport(usage) {
  const imports = [];

  // 添加hooks
  if (usage.hooks.size > 0) {
    imports.push(...Array.from(usage.hooks));
  }

  // 添加其他功能
  if (usage.memo) imports.push('memo');
  if (usage.forwardRef) imports.push('forwardRef');
  if (usage.Fragment) imports.push('Fragment');
  if (usage.Component) imports.push('Component');
  if (usage.PureComponent) imports.push('PureComponent');
  if (usage.createContext) imports.push('createContext');
  if (usage.lazy) imports.push('lazy');
  if (usage.Suspense) imports.push('Suspense');

  // 如果没有使用任何React功能，返回基本导入
  if (imports.length === 0) {
    return "import * as React from 'react';";
  }

  // 如果只使用了createElement，使用namespace导入
  if (usage.createElement && imports.length === 0) {
    return "import * as React from 'react';";
  }

  // 生成命名导入
  return `import { ${imports.sort().join(', ')} } from 'react';`;
}

// 修复单个文件的React导入
function fixReactImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // 分析React使用情况
    const usage = analyzeReactUsage(content);

    // 查找现有的React导入
    const reactImportPattern = /import\s+(?:\{[^}]+\}|\*\s+as\s+\w+|\w+)\s+from\s+['"]react['"];?/g;
    const reactImports = [...content.matchAll(reactImportPattern)];

    if (reactImports.length === 0) {
      // 如果没有React导入但使用了React功能，添加导入
      if (usage.hooks.size > 0 || usage.memo || usage.forwardRef || usage.Fragment) {
        const newImport = generateOptimizedReactImport(usage);
        const lines = content.split('\n');
        
        // 找到合适的插入位置
        let insertIndex = 0;
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.startsWith("'use") || line.startsWith('"use') || 
              line.startsWith('//') || line.startsWith('/*') || line === '') {
            insertIndex = i + 1;
          } else {
            break;
          }
        }
        
        lines.splice(insertIndex, 0, newImport);
        const newContent = lines.join('\n');
        
        fs.writeFileSync(filePath, newContent, 'utf8');
        return { 
          changed: true, 
          action: 'added',
          imports: Array.from(usage.hooks)
        };
      }
      return { changed: false };
    }

    // 替换现有的React导入
    let newContent = content;
    const newImport = generateOptimizedReactImport(usage);

    // 移除所有现有的React导入
    reactImports.reverse().forEach(match => {
      const startIndex = match.index;
      const endIndex = startIndex + match[0].length;
      newContent = newContent.substring(0, startIndex) + newContent.substring(endIndex);
    });

    // 添加优化后的导入
    const lines = newContent.split('\n');
    let insertIndex = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith("'use") || line.startsWith('"use') || 
          line.startsWith('//') || line.startsWith('/*') || line === '') {
        insertIndex = i + 1;
      } else {
        break;
      }
    }
    
    lines.splice(insertIndex, 0, newImport);
    newContent = lines.join('\n');

    // 检查是否需要'use client'指令
    const needsUseClient = usage.hooks.size > 0 || usage.memo;
    const hasUseClient = /['"]use client['"];?/.test(content);

    if (needsUseClient && !hasUseClient) {
      const contentLines = newContent.split('\n');
      contentLines.unshift("'use client';", '');
      newContent = contentLines.join('\n');
    } else if (!needsUseClient && hasUseClient) {
      // 移除不必要的'use client'指令
      newContent = newContent.replace(/['"]use client['"];?\s*\n\s*\n?/, '');
    }

    if (newContent !== originalContent) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return { 
        changed: true, 
        action: 'optimized',
        imports: Array.from(usage.hooks),
        addedUseClient: needsUseClient && !hasUseClient,
        removedUseClient: !needsUseClient && hasUseClient
      };
    }

    return { changed: false };
  } catch (error) {
    return { 
      changed: false, 
      error: error.message 
    };
  }
}

// 扫描并修复所有文件
function fixAllReactImports() {
  const results = {
    filesProcessed: 0,
    filesFixed: 0,
    addedImports: 0,
    optimizedImports: 0,
    addedUseClient: 0,
    removedUseClient: 0,
    errors: []
  };

  function scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);

      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules' && item !== 'scripts') {
            scanDirectory(itemPath);
          }
        } else if (stat.isFile()) {
          if ((item.endsWith('.tsx') || item.endsWith('.jsx')) &&
              !item.includes('test') && !item.includes('spec')) {
            results.filesProcessed++;

            const result = fixReactImports(itemPath);

            if (result.changed) {
              results.filesFixed++;
              
              if (result.action === 'added') {
                results.addedImports++;
                log(`➕ 添加导入: ${itemPath} (${result.imports.join(', ')})`, 'green');
              } else if (result.action === 'optimized') {
                results.optimizedImports++;
                let message = `✅ 优化导入: ${itemPath}`;
                if (result.imports.length > 0) {
                  message += ` (${result.imports.join(', ')})`;
                }
                if (result.addedUseClient) {
                  message += ' [+use client]';
                  results.addedUseClient++;
                }
                if (result.removedUseClient) {
                  message += ' [-use client]';
                  results.removedUseClient++;
                }
                log(message, 'green');
              }
            }

            if (result.error) {
              results.errors.push(`${itemPath}: ${result.error}`);
            }
          }
        }
      });
    } catch (error) {
      results.errors.push(`扫描目录错误 ${dirPath}: ${error.message}`);
    }
  }

  scanDirectory('./src');
  return results;
}

// 主函数
function main() {
  log('🔧 开始修复React导入问题...', 'bold');
  log('目标: 智能优化React导入，解决use client指令问题', 'cyan');
  log('', 'white');

  const startTime = Date.now();
  const results = fixAllReactImports();
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  log('📊 修复结果统计:', 'cyan');
  log(`⏱️  执行时间: ${duration}秒`, 'white');
  log(`📁 处理文件: ${results.filesProcessed}`, 'white');
  log(`🔧 修复文件: ${results.filesFixed}`, results.filesFixed > 0 ? 'green' : 'yellow');
  log(`➕ 添加导入: ${results.addedImports}`, 'green');
  log(`✨ 优化导入: ${results.optimizedImports}`, 'green');
  log(`📱 添加use client: ${results.addedUseClient}`, 'green');
  log(`🗑️  移除use client: ${results.removedUseClient}`, 'green');

  if (results.errors.length > 0) {
    log('', 'white');
    log('❌ 错误列表:', 'red');
    results.errors.forEach(error => log(`   ${error}`, 'red'));
  }

  log('', 'white');
  if (results.filesFixed > 0) {
    log('🎉 React导入修复完成！', 'green');
    log('📝 建议: 运行构建测试验证修复效果', 'yellow');
  } else {
    log('✅ React导入已经优化', 'green');
  }

  return results.errors.length === 0;
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main, fixReactImports };
