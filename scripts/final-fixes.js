#!/usr/bin/env node

/**
 * 最终修复脚本
 * 修复所有剩余的语法和导入问题
 */

const fs = require('fs');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 修复特定文件的问题
function fixSpecificIssues() {
  let fixedCount = 0;

  // 1. 修复 address-input.tsx 的语法错误
  try {
    let content = fs.readFileSync('src/components/ui/address-input.tsx', 'utf8');
    const originalContent = content;
    
    // 修复双重括号问题
    content = content.replace(
      /disabled=\{disabled \?\? \(\(\!searchQuery\.trim\(\) \|\| isSearching\)\)\}/g,
      'disabled={disabled ?? (!searchQuery.trim() || isSearching)}'
    );
    
    if (content !== originalContent) {
      fs.writeFileSync('src/components/ui/address-input.tsx', content, 'utf8');
      log('✅ 修复: address-input.tsx', 'green');
      fixedCount++;
    }
  } catch (error) {
    log(`❌ 修复address-input.tsx失败: ${error.message}`, 'red');
  }

  // 2. 修复 recent-sales.tsx 的语法错误
  try {
    let content = fs.readFileSync('src/features/overview/components/recent-sales.tsx', 'utf8');
    const originalContent = content;
    
    // 修复字符串模板错误
    content = content.replace(
      /clientName: `\$\{payment\.clients\?\.first_name \?\? ''\} \$\{payment\.clients\?\.last_name \?\? \(''\}`.trim\(\) \|\| '未知客户',\)/g,
      "clientName: `${payment.clients?.first_name ?? ''} ${payment.clients?.last_name ?? ''}`.trim() || '未知客户',"
    );
    
    if (content !== originalContent) {
      fs.writeFileSync('src/features/overview/components/recent-sales.tsx', content, 'utf8');
      log('✅ 修复: recent-sales.tsx', 'green');
      fixedCount++;
    }
  } catch (error) {
    log(`❌ 修复recent-sales.tsx失败: ${error.message}`, 'red');
  }

  // 3. 修复 kanban-board.tsx 的语法错误
  try {
    let content = fs.readFileSync('src/features/kanban/components/kanban-board.tsx', 'utf8');
    const originalContent = content;
    
    // 修复错误的语法
    content = content.replace(
      /\('document' in window &&/g,
      "('document' in window) &&"
    );
    
    if (content !== originalContent) {
      fs.writeFileSync('src/features/kanban/components/kanban-board.tsx', content, 'utf8');
      log('✅ 修复: kanban-board.tsx', 'green');
      fixedCount++;
    }
  } catch (error) {
    log(`❌ 修复kanban-board.tsx失败: ${error.message}`, 'red');
  }

  // 4. 修复重复的logger导入
  const filesToCheckLogger = [
    'src/lib/cache.ts',
    'src/lib/database/performance.ts'
  ];

  filesToCheckLogger.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // 查找重复的logger导入
      const loggerImports = content.match(/import \{ logger \} from '@\/lib\/logger';/g);
      if (loggerImports && loggerImports.length > 1) {
        // 移除重复的导入，只保留第一个
        const lines = content.split('\n');
        let foundFirst = false;
        const filteredLines = lines.filter(line => {
          if (line.includes("import { logger } from '@/lib/logger';")) {
            if (!foundFirst) {
              foundFirst = true;
              return true;
            }
            return false;
          }
          return true;
        });
        
        content = filteredLines.join('\n');
        
        if (content !== originalContent) {
          fs.writeFileSync(filePath, content, 'utf8');
          log(`✅ 修复: ${filePath} (移除重复logger导入)`, 'green');
          fixedCount++;
        }
      }
    } catch (error) {
      log(`❌ 修复${filePath}失败: ${error.message}`, 'red');
    }
  });

  return fixedCount;
}

// 通用语法修复
function applyGeneralSyntaxFixes() {
  const srcFiles = getAllTsxFiles('./src');
  let totalFixed = 0;

  srcFiles.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      let hasChanges = false;

      // 修复常见的语法错误
      const fixes = [
        // 修复双重括号
        {
          pattern: /\(\(\!/g,
          replacement: '(!'
        },
        // 修复错误的字符串模板
        {
          pattern: /\?\? \(''\}/g,
          replacement: "?? ''"
        },
        // 修复错误的逗号
        {
          pattern: /,\)/g,
          replacement: ')'
        }
      ];

      fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
          content = content.replace(fix.pattern, fix.replacement);
          hasChanges = true;
        }
      });

      if (hasChanges && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        totalFixed++;
      }
    } catch (error) {
      // 忽略读取错误
    }
  });

  return totalFixed;
}

// 获取所有TypeScript文件
function getAllTsxFiles(dir) {
  const files = [];
  const path = require('path');
  
  function scanDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const itemPath = path.join(currentDir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(itemPath);
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(itemPath);
        }
      });
    } catch (error) {
      // 忽略扫描错误
    }
  }
  
  scanDir(dir);
  return files;
}

// 主函数
function main() {
  log('🔧 开始最终修复...', 'bold');
  log('', 'white');
  
  let totalFixed = 0;
  
  // 修复特定问题
  log('🎯 修复特定问题:', 'blue');
  const specificFixed = fixSpecificIssues();
  totalFixed += specificFixed;
  
  log('', 'white');
  
  // 应用通用语法修复
  log('🔧 应用通用语法修复:', 'blue');
  const generalFixed = applyGeneralSyntaxFixes();
  totalFixed += generalFixed;
  
  if (generalFixed > 0) {
    log(`✅ 通用修复: ${generalFixed} 个文件`, 'green');
  }
  
  log('', 'white');
  log('📊 最终修复结果:', 'cyan');
  log(`🔧 总修复数: ${totalFixed}`, totalFixed > 0 ? 'green' : 'yellow');
  
  if (totalFixed > 0) {
    log('', 'white');
    log('🎉 最终修复完成！', 'green');
    log('📝 建议: 运行构建测试验证', 'yellow');
  } else {
    log('', 'white');
    log('ℹ️  无需最终修复', 'blue');
  }
  
  return totalFixed > 0;
}

if (require.main === module) {
  main();
}

module.exports = { main };
