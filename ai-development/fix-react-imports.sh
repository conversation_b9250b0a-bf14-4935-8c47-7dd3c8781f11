#!/bin/bash

# 自动修复 React 导入问题的脚本
echo "🔧 自动修复 React 导入问题"
echo "========================"
echo "修复时间: $(date)"
echo ""

# 计数器
total_fixed=0
total_files=0

# 修复使用 React. 但没有导入 React 的文件
echo "🔧 修复缺少 React 导入的文件..."
echo ""

for file in $(find src -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js"); do
    ((total_files++))
    
    # 检查文件是否使用了 React. 但没有导入 React
    if grep -q "React\." "$file"; then
        # 检查是否有正确的 React 导入
        if ! grep -q "import.*React.*from.*['\"]react['\"]" "$file"; then
            echo "🔧 修复 $file"
            
            # 备份原文件
            cp "$file" "$file.backup"
            
            # 检查文件是否已经有其他 react 导入
            if grep -q "import.*{.*}.*from.*['\"]react['\"]" "$file"; then
                # 如果已经有 hooks 导入，添加 React 到导入中
                sed -i.tmp "s/import { \([^}]*\) } from ['\"]react['\"]/import React, { \1 } from 'react'/" "$file"
            else
                # 如果没有任何 react 导入，在文件开头添加
                if grep -q "'use client'" "$file"; then
                    # 在 'use client' 后添加
                    sed -i.tmp "/'use client';/a\\
import React from 'react';" "$file"
                else
                    # 在文件开头添加
                    sed -i.tmp "1i\\
import React from 'react';" "$file"
                fi
            fi
            
            # 清理临时文件
            rm -f "$file.tmp"
            
            ((total_fixed++))
        fi
    fi
done

echo ""
echo "📊 修复结果:"
echo "==========="
echo "扫描文件数: $total_files"
echo "修复文件数: $total_fixed"

if [ $total_fixed -gt 0 ]; then
    echo ""
    echo "✅ 已修复 $total_fixed 个文件的 React 导入问题"
    echo ""
    echo "🔍 建议运行以下命令验证修复结果:"
    echo "npm run build"
    echo ""
    echo "📝 如果需要恢复，可以使用备份文件:"
    echo "find src -name '*.backup' -exec bash -c 'mv \"\$1\" \"\${1%.backup}\"' _ {} \\;"
else
    echo ""
    echo "ℹ️  没有找到需要修复的文件"
fi
