#!/bin/bash

# 查找缺失状态变量的脚本
echo "🔍 查找缺失的状态变量"
echo "===================="
echo "查找时间: $(date)"
echo ""

# 查找所有包含空useState声明的文件
echo "📁 扫描模态框组件中的状态变量问题..."
echo ""

# 计数器
total_files=0
problem_files=0

# 查找所有模态框文件
modal_files=$(find src -name "*Modal*.tsx" -o -name "*modal*.tsx")

for file in $modal_files; do
    ((total_files++))
    echo "检查文件: $file"
    
    # 查找空的useState声明
    if grep -n "const \[\] = useState" "$file" > /dev/null 2>&1; then
        echo "❌ 发现问题: $file"
        echo "   问题行:"
        grep -n "const \[\] = useState" "$file" | sed 's/^/   /'
        ((problem_files++))
        echo ""
    else
        echo "✅ 正常: $file"
    fi
done

echo ""
echo "📊 扫描结果汇总:"
echo "==============="
echo "扫描文件数: $total_files"
echo "问题文件数: $problem_files"

if [ $problem_files -eq 0 ]; then
    echo ""
    echo "🎉 没有发现状态变量问题！"
    exit 0
else
    echo ""
    echo "⚠️  发现 $problem_files 个文件存在状态变量问题"
    echo "需要修复这些文件中的空useState声明"
    exit 1
fi
