#!/bin/bash

# 全面测试渲染问题的脚本
echo "🔍 全面渲染问题测试"
echo "==================="
echo "测试时间: $(date)"
echo "测试环境: http://localhost:3001"
echo ""

# 测试函数
test_page_rendering() {
    local url=$1
    local name=$2
    local expected_code=${3:-200}
    
    echo -n "测试 $name 渲染... "
    
    # 使用 curl 测试页面是否能正常响应
    response=$(curl -s -o /dev/null -w "%{http_code} %{time_total}s" "$url" 2>/dev/null)
    http_code=$(echo $response | cut -d' ' -f1)
    time_total=$(echo $response | cut -d' ' -f2)
    
    if [ "$http_code" = "$expected_code" ] || [ "$http_code" = "404" ] || [ "$http_code" = "307" ]; then
        echo "✅ 通过 (${http_code}, ${time_total})"
        return 0
    else
        echo "❌ 失败 (期望: ${expected_code}, 实际: ${http_code}, 时间: ${time_total})"
        
        # 获取错误详情
        echo "   错误详情:"
        curl -s "$url" | head -5 | sed 's/^/   /'
        echo ""
        return 1
    fi
}

# 检查服务器日志中的错误
check_server_errors() {
    echo "🔍 检查服务器错误日志..."
    echo "------------------------"
    
    # 这里我们无法直接访问服务器日志，但可以测试关键页面
    # 如果页面返回 500 错误，说明有渲染问题
    
    local error_count=0
    
    # 测试主要页面
    pages=(
        "http://localhost:3001/"
        "http://localhost:3001/auth/sign-in"
        "http://localhost:3001/auth/sign-up"
        "http://localhost:3001/dashboard"
        "http://localhost:3001/dashboard/overview"
        "http://localhost:3001/dashboard/clients"
        "http://localhost:3001/dashboard/calendar"
        "http://localhost:3001/dashboard/treatments"
        "http://localhost:3001/dashboard/invoices"
        "http://localhost:3001/dashboard/payments"
        "http://localhost:3001/dashboard/settings"
    )
    
    for page in "${pages[@]}"; do
        response=$(curl -s -o /dev/null -w "%{http_code}" "$page" 2>/dev/null)
        if [ "$response" = "500" ]; then
            echo "❌ 服务器错误: $page (HTTP 500)"
            ((error_count++))
        fi
    done
    
    if [ $error_count -eq 0 ]; then
        echo "✅ 没有发现服务器错误"
    else
        echo "❌ 发现 $error_count 个服务器错误"
    fi
    
    return $error_count
}

# 测试 API 端点
test_api_endpoints() {
    echo ""
    echo "🔌 测试 API 端点渲染..."
    echo "----------------------"
    
    local api_error_count=0
    
    apis=(
        "http://localhost:3001/api/health"
        "http://localhost:3001/api/health/db"
        "http://localhost:3001/api/treatments"
        "http://localhost:3001/api/invoices"
        "http://localhost:3001/api/payments"
    )
    
    for api in "${apis[@]}"; do
        response=$(curl -s -o /dev/null -w "%{http_code}" "$api" 2>/dev/null)
        if [ "$response" = "500" ]; then
            echo "❌ API 错误: $api (HTTP 500)"
            ((api_error_count++))
        elif [ "$response" = "200" ]; then
            echo "✅ API 正常: $api"
        else
            echo "ℹ️  API 状态: $api (HTTP $response)"
        fi
    done
    
    return $api_error_count
}

# 计数器
total_tests=0
passed_tests=0
error_count=0

echo "📋 开始全面渲染测试..."
echo ""

# 测试页面渲染
echo "🌐 测试页面渲染"
echo "---------------"

# 根页面
test_page_rendering "http://localhost:3001/" "主页" "307" && ((passed_tests++))
((total_tests++))

# 认证页面
test_page_rendering "http://localhost:3001/auth/sign-in" "登录页面" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/auth/sign-up" "注册页面" && ((passed_tests++))
((total_tests++))

# 仪表板页面 (期望 404 因为需要认证)
test_page_rendering "http://localhost:3001/dashboard" "仪表板主页" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/overview" "概览页面" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/clients" "客户管理" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/calendar" "日历页面" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/treatments" "治疗项目" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/invoices" "账单管理" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/payments" "付款管理" "404" && ((passed_tests++))
((total_tests++))
test_page_rendering "http://localhost:3001/dashboard/settings" "系统设置" "404" && ((passed_tests++))
((total_tests++))

# 检查服务器错误
check_server_errors
server_errors=$?
((error_count += server_errors))

# 测试 API 端点
test_api_endpoints
api_errors=$?
((error_count += api_errors))

echo ""
echo "📊 渲染测试结果汇总"
echo "==================="
echo "页面测试: $passed_tests/$total_tests 通过"
echo "服务器错误: $server_errors 个"
echo "API 错误: $api_errors 个"
echo "总错误数: $error_count 个"

if [ $error_count -eq 0 ] && [ $passed_tests -eq $total_tests ]; then
    echo ""
    echo "🎉 所有渲染测试通过！系统运行正常。"
    exit 0
else
    echo ""
    echo "⚠️ 发现 $error_count 个渲染问题，请检查相关功能。"
    exit 1
fi
