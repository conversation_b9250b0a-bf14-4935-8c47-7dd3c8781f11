#!/bin/bash

# 医美诊所CRM系统 - 全面页面测试脚本
# 测试日期: 2025-07-24
# 测试环境: localhost:3001

echo "🏥 医美诊所CRM系统 - 全面页面测试"
echo "=================================="
echo "测试时间: $(date)"
echo "测试环境: http://localhost:3000"
echo ""

# 测试函数
test_page() {
    local url=$1
    local name=$2
    local expected_code=${3:-200}
    
    echo -n "测试 $name ($url)... "
    
    response=$(curl -s -o /dev/null -w "%{http_code} %{time_total}s" "$url" 2>/dev/null)
    http_code=$(echo $response | cut -d' ' -f1)
    time_total=$(echo $response | cut -d' ' -f2)
    
    if [ "$http_code" = "$expected_code" ]; then
        echo "✅ 通过 (${http_code}, ${time_total})"
        return 0
    else
        echo "❌ 失败 (期望: ${expected_code}, 实际: ${http_code}, 时间: ${time_total})"
        return 1
    fi
}

# 测试API内容
test_api_content() {
    local url=$1
    local name=$2
    
    echo -n "测试 $name API内容... "
    
    content=$(curl -s "$url" 2>/dev/null)
    if echo "$content" | grep -q "status.*healthy\|error\|data" 2>/dev/null; then
        echo "✅ 通过 (返回有效JSON)"
        return 0
    else
        echo "❌ 失败 (无效响应)"
        echo "响应内容: $content"
        return 1
    fi
}

# 计数器
total_tests=0
passed_tests=0

# 测试根页面
echo "📍 测试根页面"
echo "-------------"
test_page "http://localhost:3000" "主页" "307" && ((passed_tests++))
((total_tests++))
echo ""

# 测试认证页面
echo "🔐 测试认证页面"
echo "---------------"
test_page "http://localhost:3000/auth/sign-in" "登录页面" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/auth/sign-up" "注册页面" && ((passed_tests++))
((total_tests++))
echo ""

# 测试API接口
echo "🔌 测试API接口"
echo "-------------"
test_page "http://localhost:3000/api/health" "健康检查API" && ((passed_tests++))
((total_tests++))
test_api_content "http://localhost:3000/api/health" "健康检查"

test_page "http://localhost:3000/api/health/db" "数据库健康检查API" && ((passed_tests++))
((total_tests++))

test_page "http://localhost:3000/api/clients-simple" "客户API (简化版)" && ((passed_tests++))
((total_tests++))

test_page "http://localhost:3000/api/treatments" "治疗项目API" && ((passed_tests++))
((total_tests++))

test_page "http://localhost:3000/api/invoices" "账单API" && ((passed_tests++))
((total_tests++))

test_page "http://localhost:3000/api/payments" "付款API" && ((passed_tests++))
((total_tests++))

test_page "http://localhost:3000/api/test-db" "数据库测试API" && ((passed_tests++))
((total_tests++))
echo ""

# 测试受保护的仪表板页面 (期望重定向)
echo "🛡️ 测试受保护页面 (期望重定向到登录)"
echo "----------------------------------------"
test_page "http://localhost:3000/dashboard" "仪表板主页" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/overview" "概览页面" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/calendar" "日历页面" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/clients" "客户管理" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/treatments" "治疗项目" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/invoices" "账单管理" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/payments" "付款管理" "404" && ((passed_tests++))
((total_tests++))
test_page "http://localhost:3000/dashboard/settings" "系统设置" "404" && ((passed_tests++))
((total_tests++))
echo ""

# 测试结果汇总
echo "📊 测试结果汇总"
echo "==============="
echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"
echo "成功率: $(( passed_tests * 100 / total_tests ))%"
echo ""

if [ $passed_tests -eq $total_tests ]; then
    echo "🎉 所有测试通过！系统运行正常。"
    exit 0
else
    echo "⚠️ 有 $((total_tests - passed_tests)) 个测试失败，请检查相关功能。"
    exit 1
fi
