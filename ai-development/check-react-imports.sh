#!/bin/bash

# 检查 React 导入问题的脚本
# 查找可能的 React 导入不一致问题

echo "🔍 检查 React 导入问题"
echo "====================="
echo "检查时间: $(date)"
echo ""

# 计数器
total_files=0
problem_files=0

echo "📁 扫描 src 目录中的 TypeScript/JavaScript 文件..."
echo ""

# 查找使用 React. 但没有导入 React 的文件
echo "🔍 检查使用 React. 但可能缺少导入的文件:"
echo "----------------------------------------"

for file in $(find src -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js"); do
    ((total_files++))
    
    # 检查文件是否使用了 React. 但没有导入 React
    if grep -q "React\." "$file"; then
        # 检查是否有正确的 React 导入
        if ! grep -q "import.*React.*from.*['\"]react['\"]" "$file"; then
            echo "❌ $file - 使用 React. 但缺少 React 导入"
            ((problem_files++))
            
            # 显示问题行
            echo "   问题行:"
            grep -n "React\." "$file" | head -3 | sed 's/^/   /'
            echo ""
        fi
    fi
done

echo ""
echo "🔍 检查可能的 React hooks 导入问题:"
echo "--------------------------------"

# 检查常见的 React hooks 使用但没有导入的情况
hooks=("useState" "useEffect" "useCallback" "useMemo" "useContext" "useRef" "useReducer")

for file in $(find src -name "*.tsx" -o -name "*.ts"); do
    for hook in "${hooks[@]}"; do
        if grep -q "\b$hook\b" "$file"; then
            # 检查是否有正确的导入
            if ! grep -q "import.*{.*$hook.*}.*from.*['\"]react['\"]" "$file" && ! grep -q "import.*React.*from.*['\"]react['\"]" "$file"; then
                echo "⚠️  $file - 使用 $hook 但可能缺少导入"
                
                # 显示使用该 hook 的行
                echo "   使用行:"
                grep -n "\b$hook\b" "$file" | head -2 | sed 's/^/   /'
                echo ""
            fi
        fi
    done
done

echo ""
echo "🔍 检查 'use client' 指令:"
echo "----------------------"

client_components=0
for file in $(find src -name "*.tsx" -o -name "*.ts"); do
    if grep -q "'use client'" "$file" || grep -q '"use client"' "$file"; then
        ((client_components++))
    fi
done

echo "✅ 找到 $client_components 个客户端组件"

echo ""
echo "📊 扫描结果汇总:"
echo "==============="
echo "总文件数: $total_files"
echo "问题文件数: $problem_files"
echo "客户端组件数: $client_components"

if [ $problem_files -eq 0 ]; then
    echo ""
    echo "🎉 没有发现 React 导入问题！"
    exit 0
else
    echo ""
    echo "⚠️  发现 $problem_files 个文件可能存在 React 导入问题"
    echo "建议检查并修复这些文件的导入语句"
    exit 1
fi
