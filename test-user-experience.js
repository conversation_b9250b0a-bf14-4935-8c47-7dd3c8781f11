#!/usr/bin/env node

// 用户体验测试脚本
const http = require('http');

const BASE_URL = 'http://localhost:3001';

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// HTTP请求工具函数
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({ 
          status: res.statusCode, 
          headers: res.headers,
          data: body,
          size: Buffer.byteLength(body, 'utf8')
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试函数
async function runTest(name, testFn) {
  try {
    console.log(`🧪 测试: ${name}`);
    await testFn();
    console.log(`✅ 通过: ${name}`);
    testResults.passed++;
    testResults.tests.push({ name, status: 'PASSED' });
  } catch (error) {
    console.log(`❌ 失败: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, status: 'FAILED', error: error.message });
  }
}

// 断言函数
function assert(condition, message) {
  if (!condition) {
    throw new Error(message);
  }
}

// 页面加载性能测试
async function testPageLoadPerformance() {
  const startTime = Date.now();
  const response = await makeRequest('/api/health'); // 使用API端点而不是页面
  const loadTime = Date.now() - startTime;

  assert(response.status === 200, 'API端点应该正常响应');
  assert(loadTime < 5000, `API响应时间应该少于5秒，实际: ${loadTime}ms`);
  assert(response.size > 100, 'API响应应该有合理的大小');

  console.log(`   📊 API响应时间: ${loadTime}ms, 大小: ${(response.size / 1024).toFixed(2)}KB`);
}

// API响应时间测试
async function testAPIResponseTime() {
  const apis = [
    '/api/health',
    '/api/clients',
    '/api/treatments',
    '/api/appointments?start_date=2025-07-01&end_date=2025-07-31',
    '/api/invoices',
    '/api/payments'
  ];

  for (const api of apis) {
    const startTime = Date.now();
    const response = await makeRequest(api);
    const responseTime = Date.now() - startTime;
    
    assert(response.status === 200, `${api} 应该返回200状态码`);
    assert(responseTime < 2000, `${api} 响应时间应该少于2秒，实际: ${responseTime}ms`);
    
    console.log(`   📊 ${api}: ${responseTime}ms`);
  }
}

// 错误处理测试
async function testErrorHandling() {
  // 测试404错误
  const notFoundResponse = await makeRequest('/api/clients/non-existent-id');
  assert(notFoundResponse.status === 404, '不存在的客户应该返回404');
  
  // 测试无效数据
  const invalidDataResponse = await makeRequest('/api/clients', 'POST', {
    first_name: '', // 空姓名应该失败
    phone: 'invalid-phone' // 无效电话号码
  });
  assert(invalidDataResponse.status >= 400, '无效数据应该返回错误状态码');
  
  console.log('   📊 错误处理正常工作');
}

// 数据验证测试
async function testDataValidation() {
  // 测试客户数据验证
  const testCases = [
    {
      data: { first_name: '', last_name: 'Test', phone: '1234567890' },
      shouldFail: true,
      description: '空姓名'
    },
    {
      data: { first_name: 'Test', last_name: 'User', phone: '123' },
      shouldFail: true,
      description: '无效电话号码'
    },
    {
      data: { first_name: 'Test', last_name: 'User', phone: '1234567890', email: 'invalid-email' },
      shouldFail: true,
      description: '无效邮箱'
    }
  ];

  for (const testCase of testCases) {
    const response = await makeRequest('/api/clients', 'POST', testCase.data);
    
    if (testCase.shouldFail) {
      assert(response.status >= 400, `${testCase.description} 应该失败`);
    } else {
      assert(response.status < 400, `${testCase.description} 应该成功`);
    }
  }
  
  console.log('   📊 数据验证规则正常工作');
}

// 并发请求测试
async function testConcurrentRequests() {
  const requests = [];
  const numRequests = 5;
  
  // 创建多个并发请求
  for (let i = 0; i < numRequests; i++) {
    requests.push(makeRequest('/api/health'));
  }
  
  const startTime = Date.now();
  const responses = await Promise.all(requests);
  const totalTime = Date.now() - startTime;
  
  // 验证所有请求都成功
  responses.forEach((response, index) => {
    assert(response.status === 200, `并发请求 ${index + 1} 应该成功`);
  });
  
  assert(totalTime < 5000, `${numRequests}个并发请求应该在5秒内完成，实际: ${totalTime}ms`);
  
  console.log(`   📊 ${numRequests}个并发请求完成时间: ${totalTime}ms`);
}

// 内存使用测试
async function testMemoryUsage() {
  // 发送多个请求来测试内存使用
  const requests = [];
  for (let i = 0; i < 10; i++) {
    requests.push(makeRequest('/api/clients'));
  }
  
  await Promise.all(requests);
  
  // 检查健康状态中的内存使用
  const healthResponse = await makeRequest('/api/health');
  const healthData = JSON.parse(healthResponse.data);
  
  assert(healthData.checks.memory.status === 'healthy', '内存使用应该正常');
  
  const memoryUsageMB = (healthData.checks.memory.usage / 1024 / 1024).toFixed(2);
  console.log(`   📊 内存使用: ${memoryUsageMB}MB`);
}

// 缓存测试
async function testCaching() {
  // 第一次请求
  const startTime1 = Date.now();
  const response1 = await makeRequest('/api/treatments');
  const time1 = Date.now() - startTime1;
  
  // 第二次请求（可能被缓存）
  const startTime2 = Date.now();
  const response2 = await makeRequest('/api/treatments');
  const time2 = Date.now() - startTime2;
  
  assert(response1.status === 200, '第一次请求应该成功');
  assert(response2.status === 200, '第二次请求应该成功');
  
  console.log(`   📊 第一次请求: ${time1}ms, 第二次请求: ${time2}ms`);
}

// 安全性测试
async function testSecurity() {
  // 测试API端点的基本安全性
  const healthResponse = await makeRequest('/api/health');
  assert(healthResponse.status === 200, '健康检查端点应该正常工作');

  // 测试不存在的端点
  const notFoundResponse = await makeRequest('/api/nonexistent');
  assert(notFoundResponse.status === 404, '不存在的端点应该返回404');

  // 测试基本的输入验证
  const emptyDataResponse = await makeRequest('/api/clients', 'POST', {});
  assert(emptyDataResponse.status >= 400, '空数据应该返回验证错误');

  console.log('   📊 基本安全检查通过');
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始用户体验测试...\n');

  await runTest('页面加载性能', testPageLoadPerformance);
  await runTest('API响应时间', testAPIResponseTime);
  await runTest('错误处理', testErrorHandling);
  await runTest('数据验证', testDataValidation);
  await runTest('并发请求处理', testConcurrentRequests);
  await runTest('内存使用', testMemoryUsage);
  await runTest('缓存机制', testCaching);
  await runTest('安全性', testSecurity);

  console.log('\n📊 用户体验测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests.filter(t => t.status === 'FAILED').forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }

  return testResults.failed === 0;
}

// 运行测试
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行出错:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests };
